import { Component, Input } from '@angular/core';
import { TextAIComponent } from '../ai-widget/text/ai-text.component';
import { AbstractControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { QuillEditorComponent } from 'ngx-quill';

@Component({
  selector: 'app-rich-text',
  imports: [
    TextAIComponent,
    QuillEditorComponent,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './rich-text.component.html',
})
export class RichTextComponent {
  @Input({ required: true }) form: AbstractControl;
  @Input() label = 'Description';
  @Input() height = 250;

  get editorStyle() {
    return {
      height: this.height + 'px',
      backgroundColor: 'transparent',
      border: 'none',
    };
  }

  get f() {
    return (this.form as FormGroup);
  }

  getContent(text: string) {
    this.form.patchValue({
      description: text,
    });
  }
}
