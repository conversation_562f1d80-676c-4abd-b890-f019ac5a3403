import { Component, Input, ResourceRef } from '@angular/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';

@Component({
  selector: 'app-resource-loader',
  template: `
    @if (source.isLoading()) {
    <mat-progress-bar mode="indeterminate" />
    } @if (source.error()) {
    <div
      class="text-red-800 bg-red-50 rounded-lg p-4 font-semibold w-full border border-red-100 flex items-center gap-2 mb-3"
    >
      <span class="material-symbols-outlined">error</span>
      <span>{{ source.error() }}</span>
    </div>
    }
  `,
  imports: [MatProgressBarModule],
})
export class ResourceHeaderComponent {
  @Input({ required: true }) source!: ResourceRef<any>;
}
