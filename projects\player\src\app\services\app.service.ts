import {
  computed,
  HostListener,
  inject,
  Injectable,
  resource,
  signal,
} from '@angular/core';
import {
  AuthService,
  Course,
  CourseCoreService,
  getId,
  Lesson,
  mapCollection,
  Module,
  TrainingService,
  tryPromise,
  UserTrackingItem,
  VisitedLesson,
} from '@lms/core';
import { CoursePlayQuery } from './course.query';
import { NavigationItem } from './common.model';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PlayerService {
  readonly service = inject(CourseCoreService);
  readonly authService = inject(AuthService);
  trainingService = inject(TrainingService);

  source: 'ADMIN' | 'LEARNER' = 'ADMIN';
  viewChat = true;
  filter = signal<
    | {
        id: string;
        source: 'ADMIN' | 'LEARNER';
      }
    | undefined
  >(undefined);

  currentLecture = signal<
    | {
        id: string;
        type: 'LESSON' | 'MODULE';
      }
    | undefined
  >(undefined);

  launch = new Subject<number>();

  courseSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request?.id) {
        return;
      }
      const [error, res] = await tryPromise(
        request.source === 'ADMIN'
          ? this.queryCourse(request.id)
          : this.queryCourseForLearner(request.id)
      );
      if (error || !res) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }

      this.visited.set(res.trackings ?? []);
      if (request.source === 'ADMIN') {
        return res as UserTrackingItem;
      }
      setTimeout(() => {
        this.launch.next(1);
      }, 100);
      return res as UserTrackingItem;
    },
  });

  courseNavigation = computed(() => {
    const items = (this.courseSource.value()?.course?.modules ?? [])
      .map(
        (m: Module) =>
          ({
            id: m.id,
            name: m.name,
            icon: 'line_start_circle',
            type: 'MODULE',
            items: (m.lessons ?? []).map((l) => ({
              id: l.id,
              name: l.name,
              icon: 'line_end',
            })),
          } as NavigationItem)
      )
      .map((i: NavigationItem) => {
        return {
          ...i,
          visited: this.visited().some((v) => v.id === i.id),
          current: this.currentLecture()?.id === i.id,
          items: i.items?.map((j) => {
            return {
              ...j,
              visited: this.visited().some((v) => v.id === j.id),
              current: this.currentLecture()?.id === j.id,
            };
          }),
        };
      });
    return items;
  });

  visited = signal<VisitedLesson[]>([]);

  async queryCourse(id: string) {
    const { data } = await this.service.state.graphql.rawRequest(
      CoursePlayQuery,
      {
        id,
      }
    );
    if (!data) {
      return;
    }
    const items = mapCollection(data, 'coursesCollection').map(
      (x) =>
        ({
          ...x,
          modules: (mapCollection(x, 'modulesCollection') as Module[]).map(
            (w) =>
              ({
                ...w,
                lessons: (
                  mapCollection(w, 'lessonsCollection') as Lesson[]
                ).map((z) => ({
                  ...z,
                  contents:
                    typeof z.contents === 'string'
                      ? JSON.parse(z.contents)
                      : z.contents,
                })),
                module: w.id,
              } as Module)
          ),
        } as Course)
    ) as Course[];

    return {
      course: items[0],
      progress: 0,
      status: 'IN_PROGRESS' as 'IN_PROGRESS' | 'PAST' | 'DUE' | 'COMPLETED',
      author: {
        name: 'LearnOrTeam Editor',
        email: 'help@learnorteam',
        bio: '',
        avatar:
          'https://images.pexels.com/photos/810775/pexels-photo-810775.jpeg',
      },
    } as UserTrackingItem & any;
  }

  async queryCourseForLearner(id: string) {
    return await this.trainingService.queryCourseForLearner(id);
  }

  async logExist() {
    await this.service.state.supabase
      .from('tags')
      .insert({
        name: 'tracking log closed',
        description: 'Closed tab on ' + Date.now(),
        created_by: getId(this.service.user),
        organization: getId(this.service.user.organization),
      })
      .select('*');
  }
}
