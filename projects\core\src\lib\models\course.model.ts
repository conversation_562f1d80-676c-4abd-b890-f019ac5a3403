import { Organization } from './user-info.model';
import { Common } from './common.model';
import { LearningType } from './course-assign.model';

export enum CourseType {
  'MY' = 'MY',
  'LIBRARY' = 'LIBRARY',
  'TEMPLATE' = 'TEMPLATE',
}

export type Course = {
  organization: string;
  name: string;
  short: string;
  description: string;
  cover: string;
  venue: string;
  duration: number;
  type: CourseType;
  scormCourseId?: string;
  tags: string;
  language: string;
  creator: {
    name: string;
    email: string;
    bio: string;
    avatar: string;
  };
  modules?: Module[];
  frequency: '6M' | '3M' | '1YR' | '2Y' | '3Y';
  requiredScore: number;
  requisites: { id: string; name: string; cover: string; type: 'PRE' | 'POST' }[];
  coWriters: { id: string; name: string; image: string }[];
} & Common;

export type Module = Common & {
  name: string;
  description?: string;
  lessons?: Lesson[];
  position?: number;
  order: number;
  cover: string;
  course: string;
};

export type Lesson = Common & {
  name: string;
  module: string;
  contents?: Record<string, any>[];
  order: number;
};

export type CourseSubscription = {
  organization: string;
  course: string;
} & Common;

export type Category = Common & {
  name: string;
  description?: string;
  organization: string | Organization;
  isPublic: boolean;
  subjects?: string[];
  courses?: Course[];
};
