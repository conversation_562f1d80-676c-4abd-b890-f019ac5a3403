import { inject, Injectable, resource, signal } from '@angular/core';
import {
  getId,
  GlobalStateService,
  GroupItem,
  ITeamGroupItem,
  mapCollection,
  Organization,
  Team,
  UserItem,
  UserTeamGroupItem,
} from '@lms/core';
import { teamsGroupQuery, teamsQuery } from '../queries';

@Injectable({
  providedIn: 'root',
})
export class TeamCoreService {
  state = inject(GlobalStateService);

  get user(): UserItem {
    return this.state.user();
  }
  get organization(): Organization {
    return this.state.user().organization as Organization;
  }

  viewType = signal<'USER' | 'TEAM' | 'GROUP' | 'ASSIGN'>('USER');
  viewTeam = signal<Team | null>(null);
  filter = signal<{
    teamQuery?: string;
    groupQuery?: string;
  } | null>(null);

  teamSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request?.groupQuery && !request?.teamQuery) {
        return [] as Team[];
      }
      return await this.queryTeamsAndGroups();
    },
  });

  async queryTeams() {
    const { data } = await this.state.graphql.rawRequest(teamsQuery, {
      organizationId: getId(this.user?.organization),
    });
    if (!data) {
      return [] as Team[];
    }
    const items = mapCollection(data, 'teamsCollection').map(
      (x) =>
        ({
          ...x,
          groups: mapCollection(x, 'groupsCollection') as GroupItem[],
        } as Team)
    ) as Team[];
    return items;
  }

  async queryTeamsAndGroups() {
    const { data } = await this.state.graphql.rawRequest(teamsGroupQuery, {
      organizationId: getId(this.user?.organization),
    });
    if (!data) {
      return [] as Team[];
    }
    const assigns = mapCollection(data, 'user_groupsCollection');
    const items = mapCollection(data, 'teamsCollection').map(
      (x) =>
        ({
          ...x,
          groups: (mapCollection(x, 'groupsCollection') as GroupItem[]).map(
            (g) => ({
              ...g,
              users: (
                assigns
                  .filter((y) => y['group'] === g['id'])

                  .map((y) => ({
                    ...y['users'],
                    username: `${y['users']['firstname']} ${y['users']['lastname']}`,
                  })) as UserItem[]
              ).filter(
                (v, i, _) => _.map((u) => u['id']).indexOf(v['id']) === i
              ),
            })
          ),
          users: (
            assigns
              .filter((y) => y['team'] === x['id'])
              .map((y) => ({
                ...y['users'],
                username: `${y['users']['firstname']} ${y['users']['lastname']}`,
              })) as UserItem[]
          ).filter((v, i, _) => _.map((u) => u['id']).indexOf(v['id']) === i),
        } as Team)
    ) as Team[];
    return items;
  }

  async saveTeam(payloads: Team[]) {
    const user = this.user;
    if (!user) {
      return { data: [] as Team[], error: ['User data not found.'] };
    }
    const req = this.state.supabase.from('teams');

    const newRecords = payloads
      .filter((x) => !x.id)
      .map((payload) => ({
        ...payload,
        created_by: user.id,
      }));

    const updateRecords = payloads
      .filter((x) => !!x.id)
      .map((payload) => ({
        ...payload,
        updated_by: user.id,
        updated_at: new Date().toISOString(),
      }));

    const response: any[] = [];
    const errors: string[] = [];

    if (updateRecords.length) {
      const res = await Promise.all(
        updateRecords.map(async ({ id, ...payload }) =>
          req.update(payload).eq('id', id).select('*')
        )
      );
      response.push(...(res.map((x) => x.data).filter(Boolean) ?? []));
      errors.push(
        ...((res.map((x) => x.error?.message).filter(Boolean) ??
          []) as string[])
      );
    }
    
    const names = newRecords.map((x) => x.name.trim());
    const duplicates = await this.checkTeamGroupDuplication(names, true);
    if (duplicates.length) {
      return {
        data: [] as Team[],
        error: duplicates.map((x) => `Team name ${x.name} already exists.`),
      };
    }
    const { data, error } = await req.insert(newRecords).select('*');
    response.push(...(data ?? []));
    errors.push(error?.message!);
    return {
      data: response.filter((x) => !!x) as Team[],
      error: errors.filter(Boolean),
    };
  }

  async checkTeamGroupDuplication(names: string[], isTeam: boolean){
    if (!names.length) {
      return [];
    }
    names = names.map((x) => x.trim());
    const res = await this.state.supabase
      .from(isTeam ? 'teams' : 'groups')
      .select('id, name')
      .or(`name.in.(${names.join(',')})`)
      .eq('organization', getId(this.organization));
    return (res.data || []) as {
      id: string;
      name: string;
    }[];
  }

  async saveGroup(payloads: GroupItem[]) {
    const user = this.user;
    if (!user) {
      return { data: [] as GroupItem[], error: 'User data not found.' };
    }
    const req = this.state.supabase.from('groups');

    const newRecords = payloads
      .filter((x) => !x.id)
      .map((payload) => ({
        ...payload,
        created_by: user.id,
      }));

    const updateRecords = payloads
      .filter((x) => !!x.id)
      .map((payload) => ({
        ...payload,
        updated_by: user.id,
        updated_at: new Date().toISOString(),
      }));

    const response: any[] = [];
    const errors: string[] = [];

    if (updateRecords.length) {
      const res = await Promise.all(
        updateRecords.map(async ({ id, ...payload }) =>
          req.update(payload).eq('id', id).select('*')
        )
      );
      response.push(...(res.map((x) => x.data).filter(Boolean) ?? []));
      errors.push(
        ...((res.map((x) => x.error?.message).filter(Boolean) ??
          []) as string[])
      );
    }

    const names = newRecords.map((x) => x.name.trim());
    const duplicates = await this.checkTeamGroupDuplication(names, true);
    if (duplicates.length) {
      return {
        data: [] as Team[],
        error: duplicates.map((x) => `Group name ${x.name} already exists.`),
      };
    }

    const { data, error } = await req.insert(newRecords).select('*');
    response.push(...(data ?? []));
    errors.push(error?.message!);
    return {
      data: response.filter(Boolean) as GroupItem[],
      error: errors.filter(Boolean),
    };
  }

  async assignUsers(data: { news: UserTeamGroupItem[]; olds: string[] }) {
    const payloadNews = data.news.map((d) => ({
      ...d,
      created_by: this.user?.id,
    }));
    const error: string[] = [];
    if (payloadNews.length) {
      const res = await this.state.supabase
        .from('user_groups')
        .insert(payloadNews)
        .select('*');
      error.push(res.error?.message!);
    }
    if (data.olds.length) {
      const res = await Promise.all(
        data.olds.map(
          async (id) =>
            await this.state.supabase.from('user_groups').delete().eq('id', id)
        )
      );
      error.push(...res.map((r) => r.error?.message!));
    }
    return error.filter(Boolean);
  }

  async unassignUser(userId: string, groupId: string) {
    const sUser = this.user;
    if (sUser?.id == userId && sUser.role === 'manager') {
      return {
        error: 'You cannot remove yourself from a Group!',
      };
    }
    const res = await this.state.supabase
      .from('user_groups')
      .delete()
      .eq('user', userId)
      .eq('group', groupId);
    return { data: res.error ? undefined : groupId, error: res.error?.message };
  }

  async getUserAssignedGroups(userId: string, organizationId: string) {
    const res = await this.state.supabase
      .from('user_groups')
      .select('id, team, group')
      .eq('user', userId)
      .eq('organization', organizationId);

    return {
      data: (res.data || []) as UserTeamGroupItem[],
      error: res.error?.message,
    };
  }

  async getUsersAssignedByType(id: string, isTeam: boolean) {
    const res = await this.state.supabase
      .from('user_groups')
      .select('id, user(id, firstname, lastname)')
      .eq(isTeam ? 'team' : 'group', id);

    return {
      data: (res.data || []).map((x: any) => ({
        userGroupId: x.id,
        userId: x.user.id,
        userName: `${x.user.firstname} ${x.user.lastname}`,
      })) as ITeamGroupItem[],
      error: res.error?.message,
    };
  }

  async getTeamGroupByNames(
    type: 'TEAM' | 'GROUP',
    names: string[],
    organizationId: string
  ) {
    if (!names.length) {
      return {
        data: [] as GroupItem[],
        error: undefined,
      };
    }
    if (type === 'TEAM') {
      const res = await this.state.supabase
        .from('teams')
        .select('id, name')
        .or(`name.in.(${names.join(',')})`)
        .eq('organization', organizationId);
      return {
        data: (res.data || []) as GroupItem[],
        error: res.error?.message,
      };
    }
    const res = await this.state.supabase
      .from('groups')
      .select('id, name, team')
      .or(`name.in.(${names.join(',')})`)
      .eq('organization', organizationId);

    return {
      data: (res.data || []) as GroupItem[],
      error: res.error?.message,
    };
  }

  async deleteByType(id: string, type: 'TEAM' | 'GROUP') {
    const { error } = await this.state.supabase
      .from(type === 'TEAM' ? 'teams' : 'groups')
      .delete()
      .eq('id', id);

    return {
      data: error ? undefined : id,
      error: error?.message,
    };
  }
}
