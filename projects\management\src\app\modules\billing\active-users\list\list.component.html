<table class="w-full divide-y divide-gray-300">
  <thead>
    <tr>
      <th
        scope="col"
        class="py-3.5 pr-3 pl-4 text-left text-sm font-normal italic text-lot-dark-gray sm:pl-3"
      >
        Name
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Email Address
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Team
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Permission Level
      </th>

      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Last Login
      </th>

      <th scope="col" class="relative py-3.5 pr-4 pl-3 sm:pr-3"></th>
    </tr>
  </thead>
  <tbody class="bg-white">
    @for (item of data; track $index) {
    <tr class="even:bg-gray-50 border-t">
      <td
        class="py-4 pr-3 pl-4 text-base font-medium whitespace-nowrap text-lot-dark sm:pl-3"
      >
        {{ item.username }}
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.email }}
      </td>

      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        @if (item.teams?.length) {
        <a
          href="javascript:void(0)"
          class="text-lot-blue underline flex items-center gap-1"
          [matMenuTriggerFor]="menu"
        >
          {{
            (item.teams || [])[0].teamName +
              " " +
              ((item.teams?.length || 0) > 1
                ? "(" + (item.teams?.length || 0) + ")"
                : "")
          }}
          <mat-icon>keyboard_arrow_down</mat-icon>
        </a>
        <mat-menu #menu="matMenu">
          @for (team of item.teams; track $index) {
          <button mat-menu-item>{{ team.teamName }}</button>
          }
        </mat-menu>
        }@else {
        <a href="javascript:void(0)" class="text-lot-blue underline">
          Assign Team
        </a>
        }
      </td>
      <td
        class="px-3 py-4 text-base font-medium whitespace-nowrap text-lot-dark"
      >
        {{ item.role }}
      </td>
      <td
        class="px-3 py-4 text-base font-medium whitespace-nowrap text-lot-dark"
      >
        {{ item.last_sign_in_at | date : "EEE MMM d, y" }}
      </td>

      <td
        class="py-4 pr-4 pl-3 text-sm sm:pr-3 flex items-center justify-end gap-3"
      >
        <a
          href="javascript:void(0)"
          (click)="action('edit', item)"
          class="text-lot-blue/70 hover:text-lot-blue text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="action('delete', item)"
          class="text-lot-danger/70 hover:text-lot-danger text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </td>
    </tr>
    }
  </tbody>
</table>
<mat-paginator [length]="data.length" class="border-t" />
