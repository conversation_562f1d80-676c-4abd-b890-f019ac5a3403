html,
body {
  height: 100%;
  font-family: "Hanken Grotesk", sans-serif;
}
body {
  margin: 0;
  font-family: "Hanken Grotesk", sans-serif;
  font-optical-sizing: auto;
  color: black;

  --mat-select-panel-background-color: white;
  --mat-menu-container-color: white;
  --mat-autocomplete-background-color: white;
  --mat-datepicker-calendar-container-background-color: white;
  --mat-paginator-container-background-color: white;
  --mat-dialog-container-color: white;
  --mdc-dialog-container-color: white;
  --mat-paginator-container-text-font: "Hanken Grotesk", sans-serif;
  --mat-select-container-text-font: "Hanken Grotesk", sans-serif;
  --mat-menu-container-text-font: "Hanken Grotesk", sans-serif;
  --mat-option-label-text-font: "Hanken Grotesk", sans-serif;
  --mat-paginator-container-text-size: 15px;
  --mat-menu-container-shape: 10px;
  --mdc-dialog-container-shape: 18px;
  --mdc-linear-progress-active-indicator-color: #2e7ddb;
  --mat-timepicker-container-background-color: white;
  --mdc-snackbar-container-color: white;
}

.material-symbols-outlined {
  font-variation-settings: "FILL" 0, "wght" 200, "GRAD" 0, "opsz" 24;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
  border: 2px dashed #202020;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.learning-main {
  display: flex;
  width: 100%;
  margin: 50px 0 0;
}

app-my-learning,
.mat-tab-group {
  width: 100%;
}

.mat-expansion-indicator {
  width: 23px;
  height: 23px;
  display: flex;
  border-radius: 50%;
  background: #202020; // rgb(22, 57, 91);
  align-items: center;
  justify-content: center;
}

.mat-expansion-indicator::after {
  border-width: 0 2px 2px 0 !important;
}

.doc-view-dialog {
  width: 60%;
  height: 90%;
}

.no-padding-container {
  mat-dialog-container {
    padding: 0% !important;
  }
  .event-blue {
    height: 500px !important;
  }
}

.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  border: none;
}

.ql-toolbar.ql-snow {
  border-bottom: 1px solid #ccc !important;
}

.text-wrapper {
  word-break: break-word;
  overflow-wrap: normal;
  white-space: normal !important;
  text-align: justify;
}
