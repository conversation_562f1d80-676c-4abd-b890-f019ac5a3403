import { CurrencyPipe, NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { BillFrequency, BillingPlan, BillingService } from '@lms/core';
import { PaymentComponent } from './payment/payment.component';
import { PaymentService } from './payment.service';

@Component({
  selector: 'app-sub-plan',
  imports: [
    CurrencyPipe,
    MatProgressBarModule,
    PaymentComponent,
    NgTemplateOutlet,
  ],
  templateUrl: './plans.component.html',
})
export class PlansComponent implements OnInit {
  service = inject(BillingService);
  pymentService = inject(PaymentService);
  public dialogRef: MatDialogRef<PlansComponent> = inject(MatDialogRef);

  data: BillingPlan[] = [];

  view: 'PLAN' | 'PAYMENT' = 'PLAN';

  isLoading = false;
  frequency = signal<BillFrequency>(BillFrequency.MONTHLY);
  frequencies = [
    {
      value: BillFrequency.MONTHLY,
      label: 'Pay Monthly',
    },
    {
      value: BillFrequency.YEARLY,
      label: 'Pay Yearly (20% off)',
    },
    {
      value: BillFrequency.YEARLY3,
      label: 'Pay every 3 years (30% off)',
    },
  ];

  plans = computed(() => {
    return this.data.filter(
      (p) => p.type === 'PLAN' && p.frequency === this.frequency()
    );
  });

  addOns = computed(() => {
    return this.data.filter((p) => p.type === 'ADDON');
  });

  selectedPlan?: BillingPlan;

  selectedPlans: BillingPlan[] = [];

  async ngOnInit(): Promise<void> {
    this.isLoading = true;
    const res = await this.service.getPlans();
    this.data = res.data;
    this.isLoading = false;
  }

  selectFrequency(item: { value: BillFrequency; label: string }) {
    this.frequency.set(item.value);
  }

  addAddon(checked: boolean, plan: BillingPlan) {
    if (checked) {
      this.selectedPlans.push(plan);
    } else {
      this.selectedPlans = this.selectedPlans.filter((p) => p.id !== plan.id);
    }
  }

  selectPlan(plan: BillingPlan) {
    this.selectedPlan = plan;
    this.selectedPlans.push(plan);
  }

  proceedPayment() {
    if (!this.selectedPlans.length) return;
    this.view = 'PAYMENT';
  }

  // addPrices() {
  //   Promise.all(this.data.map(async (p) => await this.pymentService.savePrice(p))).then(
  //     (prices) => {
  //       console.log(prices);
  //     }
  //   );
  // }
}
