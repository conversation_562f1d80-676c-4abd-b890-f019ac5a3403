<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col gap-5 px-12 pb-5"
>
  <h1 class="text-xl text-lot-dark-gray font-semibold mb-5">
    General Settings
  </h1>
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (error) {
  <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
  }

  <div class="grid grid-cols-2 gap-10 pb-5">
    <div class="form-lot-input">
      <div class="field">
        <input
          type="text"
          id="name"
          formControlName="name"
          placeholder="How to Make a Good Sale"
        />
        <label for="name" [class.error]="f['name'].invalid && f['name'].dirty">
          Course Name
        </label>
      </div>
      @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
      <div class="error">
        @if (f['name'].errors['required']) {
        <span>Course name is required</span>
        } @if (f['name'].errors['minlength']) {
        <span>Course name must be at least 3 characters</span>
        }
      </div>
      }
    </div>
    <div class="form-lot-input">
      <div class="field">
        <input
          type="text"
          id="language"
          formControlName="language"
          class="capitalize"
          placeholder="English"
        />
        <label for="language">Language</label>
      </div>
    </div>

    <app-rich-text [form]="form" label="Give your course a description" />
    <div class="h-full w-full flex flex-col gap-4">
      <span>Course Header</span>
      <div class="flex w-full">
        <app-file-loader
          [type]="uploadType"
          [url]="data.cover"
          (sendFile)="onFileUploaded($event)"
        />
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          type="number"
          id="requiredScore"
          formControlName="requiredScore"
          placeholder="70"
        />
        <label for="requiredScore">Score Required to Pass</label>
      </div>
    </div>
    <div class="form-lot-input">
      <div class="field">
        <select id="frequency" formControlName="frequency">
          @for (item of frequencyOptions; track $index) {
          <option [value]="item.id">{{ item.label }}</option>
          }
        </select>
        <label for="frequency">Frequency</label>
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          type="text"
          id="tags"
          placeholder="Sale"
          [matChipInputFor]="chipGrid"
          (matChipInputTokenEnd)="addTag($event)"
        />
        <label for="tags">Tags</label>
      </div>
      <mat-chip-grid #chipGrid aria-label="Enter keywords">
        @for (tag of tags(); track tag) {
          <mat-chip-row (removed)="removeTag(tag)">
            {{tag}}
            <button matChipRemove [attr.aria-label]="'remove ' + tag">
              <mat-icon>cancel</mat-icon>
            </button>
          </mat-chip-row>
        }
      </mat-chip-grid>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          type="text"
          id="scormCourseId"
          formControlName="scormCourseId"
        />
        <label for="scormCourseId">SCORM Cloud ID</label>
      </div>
    </div>
  </div>

  <!-- <div class="flex flex-col gap-5 py-10 border-t">
    <h1 class="text-xl text-lot-dark-gray font-semibold">Requisites</h1>
    <div class="grid grid-cols-2 gap-10 w-full">
      <div class="flex flex-col gap-5">
        <a href="javascript:void(0)" (click)="addRequisite('PRE')">
          + Add pre-requisites
        </a>
        <ul class="list-none">
          @for (requisite of preRequisites; track requisite.id; let i = $index)
          {
          <li class="flex justify-between items-center mb-2">
            <span>{{ requisite.name }}</span>
            <button (click)="removeRequisite(i, 'PRE')" class="text-red-500">
              <i class="fa-solid fa-trash"></i>
            </button>
          </li>
          }
        </ul>
      </div>
      <div class="flex flex-col gap-5">
        <a href="javascript:void(0)" (click)="addRequisite('POST')">
          + Add post-requisites
        </a>
        <ul class="list-none">
          @for (requisite of postRequisites; track requisite.id; let i = $index)
          {
          <li class="flex justify-between items-center mb-2">
            <span>{{ requisite.name }}</span>
            <button (click)="removeRequisite(i, 'POST')" class="text-red-500">
              <i class="fa-solid fa-trash"></i>
            </button>
          </li>
          }
        </ul>
      </div>
    </div>
  </div> -->

  <div class="flex flex-col gap-5 py-10 border-t">
    <h1 class="text-xl text-lot-dark-gray font-semibold">Manage Co-Authors</h1>
    <div class="grid grid-cols-2 gap-10 w-full">
      <div class="flex flex-col gap-5">
        <a href="javascript:void(0)" (click)="addCoWriter()">
          + Add co-authors
        </a>
        <ul class="list-none">
          @for (writer of coWriters; track writer.id; let i = $index) {
          <li class="flex justify-between items-center mb-2">
            <span>{{ writer.name }}</span>
            <button (click)="removeCoWriter(i)" class="text-red-500">
              <i class="fa-solid fa-trash"></i>
            </button>
          </li>
          }
        </ul>
      </div>
    </div>
  </div>

  <div class="flex flex-col gap-5 py-10 border-t">
    <h1 class="text-xl text-lot-dark-gray font-semibold">{{ author.bio }}</h1>
    <div class="flex flex-col gap-5">
      <div class="flex gap-5">
        <div
          class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
        >
          <img
            [src]="author.avatar"
            alt=""
            srcset=""
            class="w-full h-full object-cover"
          />
        </div>
        <div>
          <h3 class="font-semibold text-lot-blue text-xl">{{ author.name }}</h3>
          <p class="text-xs text-lot-dark">{{ author.email }}</p>
        </div>
      </div>
    </div>
  </div>
</form>
