<div
  class="relative h-screen bg-lot-light-gray flex justify-center items-center"
>
  <img
    src="assets/images/new/coffee.png"
    alt=""
    srcset=""
    class="absolute top-0 left-0"
  />
  <section
    class="relative w-[573px] min-h-[639px] bg-white neat rounded-lg flex flex-col gap-5 items-center px-20 py-16 z-40"
  >
    <div class="h-10 w-56">
      <img src="assets/images/new/logo-light.png" alt="" srcset="" />
    </div>
    @if (isLoading) {
    <mat-progress-bar color="accent" class="my-3 ml-6" mode="indeterminate" />
    }
    <router-outlet />
  </section>
  <img
    src="assets/images/new/sitting-reading.png"
    alt=""
    srcset=""
    class="absolute bottom-0 right-0"
  />
  <button
    class="absolute bg-white bottom-10 right-20 z-30 rounded-lg border border-lot-ai text-lot-ai-dark px-5 py-2 flex items-center gap-2"
  >
    <span class="material-symbols-outlined"> help </span>
    Need any Help?
  </button>
</div>
