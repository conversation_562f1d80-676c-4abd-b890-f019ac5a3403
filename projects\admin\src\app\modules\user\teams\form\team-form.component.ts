import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import {
  getId,
  GroupItem,
  Team,
  TeamCoreService,
  ToastMessageType,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { markControlsDirty } from '@lms/core';
import { AssignUserFormComponent } from './assign/user-form.component';
import { GroupFormComponent } from './group/group-form.component';

export type TeamFormView =
  | 'TEAM'
  | 'GROUP'
  | 'ASSIGN_USER'
  | 'ASSIGN_TEAM_GROUP';

@Component({
  selector: 'app-team-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    NgTemplateOutlet,
    AssignUserFormComponent,
    GroupFormComponent,
  ],
  templateUrl: 'team-form.component.html',
})
export class TeamFormComponent implements OnInit {
  readonly service = inject(TeamCoreService);
  userService = inject(UsersCoreService);
  public dialogRef: MatDialogRef<TeamFormComponent> = inject(MatDialogRef);

  data: {
    item?: Team;
    group?: GroupItem;
    user?: UserItem;
    isTeam: boolean;
    type: TeamFormView;
  } = inject(MAT_DIALOG_DATA);

  view = this.data.type;
  isLoading = false;
  error?: string;

  groupInput?: {
    item?: GroupItem;
    team: Team;
  };

  get assignerInput() {
    return {
      item: (this.data.isTeam ? this.data.item : this.data.group) as GroupItem,
      user: this.data.user as UserItem,
      isTeam: this.data.isTeam,
      type: this.data.type,
    };
  }

  form = new FormGroup({
    name: new FormControl('', Validators.required),
  });

  get f() {
    return this.form.controls;
  }

  ngOnInit(): void {
    if (this.data.type === 'GROUP') {
      this.groupInput = {
        item: this.data.group,
        team: this.data.item as Team,
      };
    }
    if (this.data.item) {
      this.form.patchValue({
        name: this.data.item.name,
      });
    }
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.isLoading = true;

    await this.executeSaveTeam();
    this.dialogRef.close();
    this.service.teamSource.reload();
  }

  async saveGroupNew() {
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const team = await this.executeSaveTeam();
    this.service.viewTeam.set(team as Team);
    this.data.item = team as Team;
    this.service.teamSource.reload();
    this.groupInput = {
      item: undefined,
      team: team as Team,
    };
    this.view = 'GROUP';
  }

  async executeSaveTeam() {
    const payload = {
      name: this.form.value.name,
    } as any;
    if (this.data.item?.id) {
      payload['id'] = this.data.item?.id;
    } else {
      payload['organization'] = getId(this.service.organization);
    }
    const res = await this.service.saveTeam([payload]);
    this.isLoading = false;
    if (res.error.length) {
      this.error = res.error[0];
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error[0],
        type: ToastMessageType.ERROR,
      });
      return;
    }
    
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Team saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    return res.data[0];
  }

  onClose({ action }: { action: 'BACK' | 'SAVE' | 'CANCEL' }) {
    if (action === 'SAVE') {
      this.dialogRef.close();
      this.service.teamSource.reload();
      this.userService.userSource.reload();
    }
    if (action === 'CANCEL') {
      this.dialogRef.close();
    }
    if (action === 'BACK') {
      this.view = 'TEAM';
    }
  }
}
