import { MatProgressBarModule } from '@angular/material/progress-bar';
import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MediaBucket, MediaCenterService, ToastMessageType, countries } from '@lms/core';
import { MatDialog } from '@angular/material/dialog';
import {
  AccountStatus,
  GlobalStateService,
  Organization,
  OrganizationService,
} from '@lms/core';
import { OrgFormComponent } from './form/user-form.component';
import { FileLoaderComponent } from '@lms/shared';

@Component({
  selector: 'app-settings-organization',
  imports: [
    CommonModule,
    FormsModule,
    MatProgressBarModule,
    ReactiveFormsModule,
    MatButtonModule,
  ],
  templateUrl: './org.component.html',
  styles: [
    `
      :host {
        width: 100%;
      }
    `,
  ],
})
export class SettingsOrgComponent implements OnInit {
  readonly state = inject(GlobalStateService);
  readonly service = inject(OrganizationService);
  readonly mediaService = inject(MediaCenterService);
  readonly dialog = inject(MatDialog);

  get UserItem() {
    return this.state.user();
  }

  get organization() {
    return this.state.user().organization as Organization;
  }

  imageFile: File;
  orgUrl = this.organization.logo ?? 'assets/images/logo-placeholder.jpg';

  countryList = countries.sort((a, b) => (a.label < b.label ? 1 : -1));
  isLoading = false;
  error?: string;

  get hasImage() {
    return !!this.state.user().avatar || !!this.imageFile;
  }

  get isLearner() {
    return this.state.user().role === AccountStatus.LEARNER;
  }
  get isOwner() {
    return this.state.user().role === AccountStatus.OWNER;
  }
  get role() {
    return this.state.user().role;
  }

  ngOnInit(): void {}

  editOrg() {
    this.dialog.open(OrgFormComponent, {
      minWidth: '800px',
      data: this.organization,
    });
  }

  getImageFile(files: File[]) {
    this.imageFile = files[0];
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;
    const file = input.files[0];
    if (!file.type.includes('image/')) {
      this.error = 'Please upload a image file.';
      return;
    }
    this.saveLogo(file);
  }

  async saveLogo(file: File) {
    const folder = (this.organization?.name ?? '_orgo_logo_')
      .replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    this.isLoading = true;
    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.isLoading = false;
      this.error = error;
      return;
    }

    if (!data) {
      this.isLoading = false;
      return;
    }
    this.orgUrl = data.url ?? 'assets/images/logo-placeholder.jpg';
    await this.service.saveOrganization({
      ...this.organization,
      logo: data.url,
    });
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Organization saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.isLoading = false;
  }
}
