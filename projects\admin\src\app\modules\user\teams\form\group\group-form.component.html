<div class="flex flex-col gap-5 p-5 h-full">
  <h1 class="text-2xl font-semibold flex flex-col">
    {{ data.item?.id ? "Edit Group " : "Create Group under" }}
    @if (data.team?.id && !data.item?.id) {
    <div class="flex gap-2">
      the
      <a
        href="javascript:void(0)"
        class="text-lot-blue underline italic"
        (click)="close.emit({ action: 'BACK' })"
      >
        {{ data.team?.name }}
      </a>
      Team
    </div>
    }
  </h1>

  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 py-5"
  >
    @if (data.item?.id) {
    <div class="form-lot-input min-h-32">
      <div class="field">
        <input type="text" id="name" formControlName="name" />
        <label for="name" [class.error]="f['name'].invalid && f['name'].dirty">
          Name your Group
        </label>
      </div>
      @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
      <div class="error">
        @if (f['name'].errors['required']) {
        <span>Group name is required</span>
        }
      </div>
      }
    </div>
    } @else {
    <div class="flex flex-col min-h-60" formArrayName="groups">
      @for (item of groups.controls; track $index; let i= $index; let last =
      $last) {
      <div
        [formGroupName]="i"
        class="flex justify-between items-center border-lot-gray py-1"
        [class.border-y]="i === 0"
        [class.border-b]="i !== 0"
      >
        <div class="flex flex-col px-2 w-full">
          <label for="{{ 'name' + i }}" class="sr-only">groupe name</label>
          <input
            [id]="'name' + i"
            type="text"
            formControlName="name"
            name="name"
            placeholder="New Group Name"
            autocomplete="new-password"
            class="bg-transparent rounded-md border-none px-5 py-2.5 w-full text-lot-dark placeholder:text-lot-dark-gray outline-none"
          />
          @if (item.get('error')?.value) {
          <span class="pl-2 text-xs text-lot-danger"
            >Group name is required</span
          >
          }
        </div>
        @if (last) {
        <button
          class="button-primary w-fit"
          (click)="addGroup(item)"
          type="button"
        >
          Add
        </button>
        } @if (!last) {
        <a
          href="javascript:void(0)"
          (click)="removeGroup(i)"
          class="text-lot-danger/70 hover:text-lot-danger text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
        }
      </div>
      }
    </div>
    }
    <div class="flex gap-4">
      <button
        class="button-primary w-fit"
        type="button"
        (click)="close.emit({ action: 'CANCEL' })"
      >
        Cancel
      </button>
      <button class="button-primary w-32" type="submit">Save Group</button>
    </div>
  </form>
</div>
