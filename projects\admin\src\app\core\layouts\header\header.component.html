<header class="w-full items-center bg-white py-2 px-6 hidden sm:flex">
  <div
    class="w-1/2 flex flex-col md:flex-row justify-center md:justify-between gap-3"
  >
    <div class="flex gap-3">
      <a
        (click)="state.isMenuOpened.set(!state.isMenuOpened())"
        class="flex items-center"
      >
        <i
          class="text-2xl fa-solid"
          [class]="state.isMenuOpened() ? 'fa-angles-left' : 'fa-angles-right'"
        ></i>
      </a>
      <h2>Hi, {{ fullName }}</h2>
    </div>
    <div
      *ngIf="showTrialAlert"
      class="w-72 flex justify-between items-center bg-green-500 font-medium text-white p-0 rounded-md"
    >
      <span class="px-4 m-0 text-xs"
        >Free trial: {{ remainingDays }} days remaining</span
      >
      <span
        class="bg-green-700 px-4 py-2 m-0 rounded-md cursor-pointer"
        (click)="gotToBilling()"
        >Upgrade</span
      >
    </div>
  </div>
  <div class="relative w-1/2 flex justify-end">
    <button
      (click)="isOpen = !isOpen"
      class="realtive z-10 w-12 h-12 rounded-full overflow-hidden border-2 border-gray-400 hover:border-gray-300 focus:border-gray-300 focus:outline-none"
    >
      <img [src]="profileImage" class="object-cover w-full h-full m-auto" />
    </button>
    <button
      *ngIf="isOpen"
      (click)="isOpen = false"
      class="h-full w-full fixed inset-0 cursor-default"
    ></button>
    <div
      *ngIf="isOpen"
      class="absolute w-48 bg-white rounded-lg shadow-lg py-2 mt-16 z-50"
    >
      <a
        href="javascript:void(0)"
        [routerLink]="['/settings']"
        routerLinkActive="router-link-active"
        class="block px-4 py-2 account-link hover:text-lot-blue"
      >
        <i class="fas fa-cog mr-3"></i>Account Settings
      </a>
      <a
        href="javascript:void(0)"
        [routerLink]="['/help']"
        class="block px-4 py-2 account-link hover:text-lot-blue"
      >
        <i class="fa-solid fa-circle-info mr-3"></i>Help
      </a>

      <a
        href="javascript:void(0)"
        [routerLink]="['/contact-us']"
        class="block px-4 py-2 account-link hover:text-lot-blue"
      >
        <i class="fa-solid fa-address-card mr-3"></i>Contact Us
      </a>

      <a
        (click)="signOut()"
        class="block px-4 py-2 account-link hover:text-lot-blue cursor-pointer"
      >
        <i class="fas fa-sign-out-alt mr-3"></i>
        Sign Out</a
      >
    </div>
  </div>
</header>

<!-- Mobile Header & Nav -->
<header class="w-full bg-primary-200 py-5 px-6 sm:hidden">
  <div class="flex items-center justify-between">
    <a
      href="/"
      class="text-white text-3xl font-semibold uppercase hover:text-gray-300"
    >
      <img alt="Company Logo" class="w-auto h-6" [src]="logo" />
    </a>
    <button
      (click)="isOpen = !isOpen"
      class="text-white text-3xl focus:outline-none"
    >
      <i *ngIf="!isOpen" class="fas fa-bars"></i>
      <i *ngIf="isOpen" class="fas fa-times"></i>
    </button>
  </div>

  <!-- Dropdown Nav -->
  <nav [ngClass]="{ flex: isOpen, hidden: !isOpen }" class="flex flex-col pt-4">
    <ng-container *ngFor="let item of mainMenu">
      <a
        *ngIf="item.path"
        class="flex items-center text-white py-2 pl-4"
        [routerLink]="[item.path]"
        routerLinkActive="active-nav-link"
      >
        <i [ngClass]="item.icon" class="mr-3"></i>
        {{ item.title }}
      </a>

      <mat-accordion *ngIf="!item.path">
        <mat-expansion-panel
          class="flex items-center text-white bg-primary-200 opacity-75 hover:opacity-100 py-0 pl-4 mat-elevation-z0"
          (opened)="panelOpenState = true"
          (closed)="panelOpenState = false"
        >
          <mat-expansion-panel-header>
            <mat-panel-title>
              <i [ngClass]="item.icon" class="mr-3"></i
              ><span> {{ item.title }}</span>
            </mat-panel-title>
          </mat-expansion-panel-header>
          <a
            *ngFor="let sub of item.children || []"
            class="flex items-center text-white p-2"
            [routerLink]="[sub.path]"
            routerLinkActive="active-nav-link"
          >
            <i [ngClass]="sub.icon" class="mr-3"></i>
            {{ sub.title }}
          </a>
        </mat-expansion-panel>
      </mat-accordion>
    </ng-container>
    <a
      href="javascript:void(0)"
      [routerLink]="['/settings']"
      routerLinkActive="router-link-active"
      class="flex items-center text-white opacity-75 hover:opacity-100 py-2 pl-4 nav-item"
    >
      <i class="fas fa-cog mr-3"></i>Account Settings
    </a>
    <a
      href="javascript:void(0)"
      [routerLink]="['/contact-us']"
      routerLinkActive="router-link-active"
      class="flex items-center text-white opacity-75 hover:opacity-100 py-2 pl-4 nav-item"
    >
      <i class="fas fa-cog mr-3"></i>Contact Us
    </a>

    <a
      href="javascript:void(0)"
      [routerLink]="['/help']"
      class="flex items-center text-white opacity-75 hover:opacity-100 py-2 pl-4 nav-item"
    >
      <i class="fas fa-help mr-3"></i>Help
    </a>

    <a
      (click)="signOut()"
      class="flex items-center text-white opacity-75 hover:opacity-100 py-2 pl-4 nav-item"
    >
      <i class="fas fa-sign-out-alt mr-3"></i>
      Sign Out
    </a>
  </nav>
</header>
