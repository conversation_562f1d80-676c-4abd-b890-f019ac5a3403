import { Component, OnInit, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { AccountStatus, AuthService, GlobalStateService } from '@lms/core';
import dayjs from 'dayjs';
import { mainMenu } from '../services';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'app-header',
  imports: [CommonModule, RouterLink, MatExpansionModule],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit {
  public state: GlobalStateService = inject(GlobalStateService);
  public authService: AuthService = inject(AuthService);
  private router: Router = inject(Router);

  isOpen = false;
  panelOpenState = false;
  mainMenu = mainMenu;
  footerMenu = [];
  currentUser = this.state.user();

  get profileImage() {
    return this.currentUser?.avatar
      ? this.currentUser.avatar
      : 'assets/images/svg/user.svg';
  }

  get fullName() {
    return this.currentUser?.firstname + ' ' + this.currentUser?.lastname;
  }

  get remainingDays() {
    // this.currentUser?.subscription?.expirationDate
    return dayjs().diff(dayjs(), 'days');
  }

  get showTrialAlert() {
    return true; // 'this.currentUser?.subscription?.plan' === 'TRIAL';
  }

  get contactUrl() {
    return `${this.state.envConfig.env.homePageUrl}/contact`;
  }

  get isAdmin() {
    return this.currentUser?.role === AccountStatus.ADMINISTRATOR;
  }
  get isManager() {
    return this.currentUser?.role === AccountStatus.MANAGER;
  }
  get isLearner() {
    return this.currentUser?.role === AccountStatus.LEARNER;
  }
  get isOwner() {
    return this.currentUser?.role === AccountStatus.OWNER;
  }

  logo = 'assets/images/logo.svg';

  ngOnInit(): void {
    this.logo = this.state.envConfig.env.organization['logo'] ?? this.logo;
    this.mainMenu = mainMenu.filter((x) => {
      let visible = true;
      if (x.title === 'User Management') {
        visible = this.isLearner ? false : true;
      }
      if (x.title === 'Reports') {
        visible = this.isLearner ? false : true;
      }
      if (x.title === 'Billing') {
        visible = this.isOwner ? true : false;
      }
      if (x.title === 'Courses') {
        visible = this.isOwner || this.isAdmin ? true : false;
      }
      return visible;
    });
  }

  async signOut(): Promise<void> {
    await this.authService.signOutAndRedirect();
  }
  requestFeature(): void {
    return;
  }
  gotToSettings(): void {
    return;
  }

  gotToBilling(): void {
    this.router.navigate(['/lms/billings']);
  }
}
