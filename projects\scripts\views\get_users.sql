CREATE OR REPLACE FUNCTION get_users(filter json)
RETURNS json SECURITY DEFINER AS $function$
DECLARE 
    data_records record;
    count_record int;
    _page int := (filter->>'page')::int;
    _page_size int := (filter->>'size')::int;
    organization_id uuid := (filter->>'organization')::uuid;
BEGIN

        SELECT  count(*) into count_record
        FROM public.users
        WHERE (organization = organization_id
              AND 
                role::text = coalesce(
                    case
                        when (filter->>'role'::text) IS NULL then role::text
                        else filter->>'role'::text
                    end,
                    role::text
                )
              )
              AND (
                email ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then email
                        else filter->>'query'::text
                    end,
                    email
                ) || '%'
                OR firstname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then firstname
                        else filter->>'query'::text
                    end,
                    firstname
                ) || '%'
                OR lastname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then lastname
                        else filter->>'query'::text
                    end,
                    lastname
                ) || '%'
        );

        CREATE TEMP TABLE temp_users (
        id uuid,
        email text,
        firstname text,
        lastname text,
        role text,
        title text,
        phone text,
        organization uuid,
        last_sign_in_at timestamp with time zone, team_groups jsonb);
        
        WITH user_assignments AS (
            SELECT 
                u.id,
                COALESCE(
                    json_agg(json_build_object(
                        'userGroupId', ug.id, 
                        'userId', u.id, 
                        'teamId', tm.id, 
                        'teamName', tm.name, 
                        'groupId', gm.id, 
                        'groupName', gm.name
                        )) FILTER (WHERE ug.id IS NOT NULL),
                    '[]'::json
                ) AS team_groups
            FROM public.users u
                LEFT JOIN public.user_groups ug ON  ug.user = u.id
                LEFT JOIN public.teams tm on tm.id = ug.team
                LEFT JOIN public.groups gm on gm.id = ug.group
            WHERE u.organization = organization_id
                AND 
                 (   u.role::text = coalesce(
                        case
                            when (filter->>'role'::text) IS NULL then u.role::text
                            else filter->>'role'::text
                        end,
                        u.role::text
                    )
                )
                AND (
                    u.email ILIKE '%' || coalesce(
                        case
                            when (filter->>'query'::text) IS NULL then u.email
                            else filter->>'query'::text
                        end,
                        u.email
                    ) || '%'
                    OR u.firstname ILIKE '%' || coalesce(
                        case
                            when (filter->>'query'::text) IS NULL then u.firstname
                            else filter->>'query'::text
                        end,
                        u.firstname
                    ) || '%'
                    OR u.lastname ILIKE '%' || coalesce(
                        case
                            when (filter->>'query'::text) IS NULL then u.lastname
                            else filter->>'query'::text
                        end,
                        u.lastname
                    ) || '%'
                )
            GROUP BY u.id
            LIMIT _page_size
            OFFSET (_page - 1) * _page_size
        )
        INSERT INTO temp_users (
        id,
        email,
        firstname,
        lastname,
        role,
        title,
        phone,
        organization,
        last_sign_in_at, team_groups)
        SELECT 
            ux.id,
            ux.email,
            ux.firstname,
            ux.lastname,
            ux.role,
            ux.title,
            ux.phone,
            ux.organization,
            au.last_sign_in_at,
               ua.team_groups
        FROM public.users ux
             INNER JOIN auth.users au ON au.id = ux.id
              INNER JOIN user_assignments ua ON ux.id = ua.id;

        SELECT array_agg(json_build_object('user', tu.*)) INTO data_records 
        FROM temp_users tu;

    DROP TABLE temp_users;

    RETURN json_build_object('users', data_records, 'total', count_record);

END;
$function$
LANGUAGE plpgsql;
