import { AbstractControl, FormControl, FormGroup, ValidationErrors, Validator, ValidatorFn } from '@angular/forms';

export const samePasswordValidator = (controlName: string): ValidatorFn => {
  return (control: AbstractControl): ValidationErrors | null => {
    const confirmPassword = control.value;
    const password = control.root.get(controlName);
    if (!password?.value || !confirmPassword) {
      return null;
    }
    if (password.value === confirmPassword) {
      return null;
    }
    return { mustMatch: { message: 'Should have the same value.' } };
  };
};

export const markControlsDirty = (group: FormGroup) => {
  Object.keys(group.controls).forEach((key: string) => {
    group.controls[key].markAsDirty();
  });
};

export function validatePassword(password: string): boolean {
  // Create a regular expression object to match the required password format.
  const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+{}|:<>?,./;'"])[a-zA-Z\d!@#$%^&*()_+{}|:<>?,./;'"]{8,}$/;

  // Return true if the password matches the regular expression, false otherwise.
  return regex.test(password);
}
