<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col gap-5 px-12 pb-5"
>
  <h1 class="text-xl text-lot-dark-gray font-semibold mb-5">
    General Settings
  </h1>
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (error) {
  <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
  }

  <div class="grid grid-cols-1 gap-10 pb-5">
    <div class="form-lot-input">
      <div class="field">
        <input
          type="text"
          id="name"
          formControlName="name"
          placeholder="How to Make a Good Sale"
        />
        <label for="name" [class.error]="f['name'].invalid && f['name'].dirty">
          {{ typeLabel }} Name
        </label>
      </div>
      @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
      <div class="error">
        @if (f['name'].errors['required']) {
        <span>{{ typeLabel }} name is required</span>
        } @if (f['name'].errors['minlength']) {
        <span>{{ typeLabel }} name must be at least 3 characters</span>
        }
      </div>
      }
    </div>

    <app-rich-text [form]="form" label="Give your course a description" />
    <div class="h-full w-full flex flex-col gap-4">
      <span>{{ typeLabel }} Header</span>
      <div class="flex w-full">
        <app-file-loader
          [type]="uploadType"
          [url]="data.cover!"
          (sendFile)="onFileUploaded($event)"
        />
      </div>
    </div>

  </div>

  <div class="flex flex-col gap-5 py-10 border-t">
    <h1 class="text-xl text-lot-dark-gray font-semibold">{{ author.bio }}</h1>
    <div class="flex flex-col gap-5">
      <div class="flex gap-5">
        <div
          class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
        >
          <img
            [src]="author.avatar"
            alt=""
            srcset=""
            class="w-full h-full object-cover"
          />
        </div>
        <div>
          <h3 class="font-semibold text-lot-blue text-xl">{{ author.name }}</h3>
          <p class="text-xs text-lot-dark">{{ author.email }}</p>
        </div>
      </div>
    </div>
  </div>
</form>
