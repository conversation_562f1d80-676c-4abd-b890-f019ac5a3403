export const usersQuery = `
query getUsers($organizationId: UUID!) {
  usersCollection(first:10, filter: {
    organization: {
      eq: $organizationId
    }}
  ){
    pageInfo {
      hasNextPage
      hasPreviousPage
      endCursor
      endCursor
    }
    edges {
      node {
        lastname
        firstname
        email
        role
        avatar
        organization
      }
    }
  }
  user_groupsCollection(first: 10, filter: {
    organization: {
      eq: $organizationId
    }}){
    pageInfo {
      hasNextPage
      hasPreviousPage
      endCursor
      startCursor
    }
    edges {
      node {
        users {
          id
          firstname
          lastname
          role
          email
          phone
        }
        teams {
          id
          name
        }
        groups {
          id
          name
        }
      }
    }
  }
}
`;