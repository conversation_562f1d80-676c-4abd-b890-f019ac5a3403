import {
  Component,
  ComponentRef,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { markControlsDirty } from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NgStyle, NgTemplateOutlet } from '@angular/common';
import { QuillEditorComponent } from 'ngx-quill';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatDialogModule } from '@angular/material/dialog';
import { LessonWidgetTypes, RichImageComponent, RichTextComponent, WidgetType } from '@lms/shared';

@Component({
  selector: 'app-widget-card-form',
  imports: [
    NgStyle,
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    MatSelectModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    NgTemplateOutlet,
    MatDialogModule,
    RichTextComponent,
    RichImageComponent,
  ],
  templateUrl: './card-form.component.html',
})
export class WidgetCardFormComponent implements OnInit {
  readonly fb = inject(FormBuilder);
  @Input({ required: true }) data: {
    content: any;
    type: WidgetType;
  };

  @Output() save = new EventEmitter<any>();

  isLoading = false;
  error?: string;
  widgetTypes = LessonWidgetTypes;
  form!: FormGroup;

  get widgetContent() {
    return this.data.content;
  }

  get widgetType() {
    return this.data.type;
  }

  editorStyle = {
    height: '200px',
    backgroundColor: '#ffffff',
    border: 'none',
  };

  ngOnInit(): void {
    this.initializeForm();
  }

  initializeForm(): void {
    this.form = this.fb.group({
      id: [this.widgetContent?.id || ''],
    });

    // Add specific form controls based on widget type
    this.addWidgetSpecificControls();

    this.form.valueChanges.subscribe(() => {
      this.onSubmit();
    });
  }

  addWidgetSpecificControls(): void {
    switch (this.widgetType) {
      case 'TEXT':
      case 'PROCESS':
      case 'SORTING':
      case 'INTERACTIVE':
        this.addTextControls();
        break;
      case 'IMAGE':
        this.addImageControls();
        break;
      case 'VIDEO':
        this.addVideoControls();
        break;
      case 'CARD':
        this.addCardControls();
        break;
      case 'QUOTE':
        this.addQuoteControls();
        break;
      case 'LIST':
        this.addListControls();
        break;
      case 'GALLERY':
        this.addGalleryControls();
        break;
      default:
        this.addTextControls(); // Default to text controls
        break;
    }
  }

  addTextControls(): void {
    this.form.addControl(
      'heading',
      new FormControl(this.widgetContent?.heading || '')
    );
    this.form.addControl(
      'subHeading',
      new FormControl(this.widgetContent?.subHeading || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );

    // Add columns if they exist
    if (this.widgetContent?.columns) {
      this.form.addControl(
        'columnSize',
        new FormControl(this.widgetContent?.columnSize || 2)
      );
      this.form.addControl(
        'columns',
        this.fb.array(
          this.widgetContent?.columns.map((col: string) =>
            this.fb.control(col)
          ) || []
        )
      );
    } else {
      this.form.addControl('columnSize', new FormControl(2));
      this.form.addControl('columns', this.fb.array([]));
    }

    // Add table if it exists
    if (this.widgetContent?.table) {
      const tableGroup = this.fb.group({
        headers: this.fb.array(
          this.widgetContent?.table.headers.map((header: string) =>
            this.fb.control(header)
          ) || []
        ),
        rows: this.fb.array([]),
      });

      // Add rows
      if (this.widgetContent?.table.rows) {
        const rowsArray = tableGroup.get('rows') as FormArray;
        this.widgetContent.table.rows.forEach((row: string[]) => {
          rowsArray.push(
            this.fb.array(row.map((cell: string) => this.fb.control(cell)))
          );
        });
      }

      this.form.addControl('table', tableGroup);
    } else {
      this.form.addControl(
        'table',
        this.fb.group({
          headers: this.fb.array([]),
          rows: this.fb.array([]),
        })
      );
    }
  }

  addImageControls(): void {
    this.form.addControl(
      'url',
      new FormControl(this.widgetContent?.url || '', Validators.required)
    );
    this.form.addControl(
      'caption',
      new FormControl(this.widgetContent?.caption || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'position',
      new FormControl(this.widgetContent?.position || '')
    );
    this.form.addControl(
      'textPosition',
      new FormControl(this.widgetContent?.textPosition || '')
    );
  }

  addVideoControls(): void {
    this.form.addControl(
      'url',
      new FormControl(this.widgetContent?.videoUrl || '', Validators.required)
    );
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'isEmbedded',
      new FormControl(this.widgetContent?.isEmbedded !== false)
    );
  }

  addCardControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'subtitle',
      new FormControl(this.widgetContent?.subtitle || '')
    );
    this.form.addControl(
      'content',
      new FormControl(this.widgetContent?.content || '')
    );
    this.form.addControl(
      'imageUrl',
      new FormControl(this.widgetContent?.imageUrl || '')
    );
  }

  addQuoteControls(): void {
    this.form.addControl(
      'quote',
      new FormControl(this.widgetContent?.quote || '', Validators.required)
    );
    this.form.addControl(
      'author',
      new FormControl(this.widgetContent?.author || '')
    );
    this.form.addControl(
      'source',
      new FormControl(this.widgetContent?.source || '')
    );
  }

  addListControls(): void {
    this.form.addControl(
      'style',
      new FormControl(this.widgetContent?.style || 'disc')
    );
    this.form.addControl(
      'heading',
      new FormControl(this.widgetContent?.heading || '')
    );

    // Add items array
    const itemsArray = this.fb.array([] as FormGroup[]);
    if (this.widgetContent?.items) {
      this.widgetContent.items.forEach((item: any) => {
        itemsArray.push(
          this.fb.group({
            text: [item.text || ''],
            checked: [item.checked || false],
          })
        );
      });
    }
    this.form.addControl('items', itemsArray);
  }

  addGalleryControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );

    // Add images array
    const imagesArray = this.fb.array([] as FormGroup[]);
    if (this.widgetContent?.images) {
      this.widgetContent.images.forEach((image: any) => {
        imagesArray.push(
          this.fb.group({
            url: [image.url || '', Validators.required],
            caption: [image.caption || ''],
            altText: [image.altText || ''],
          })
        );
      });
    }
    this.form.addControl('images', imagesArray);
  }

  // Helper methods for form arrays
  getFormArray(path: string): FormArray {
    return this.form.get(path) as FormArray;
  }

  getFormArrayForChild(control: string, form: AbstractControl): FormArray {
    return form.get(control) as FormArray;
  }

  getFormForChild(form: AbstractControl) {
    return Object.values((form as FormGroup).controls);
  }

  addColumn(): void {
    const columns = this.getFormArray('columns');
    columns.push(this.fb.control(''));
  }

  removeColumn(index: number): void {
    const columns = this.getFormArray('columns');
    columns.removeAt(index);
  }

  addTableHeader(): void {
    const headers = this.getFormArray('table.headers');
    headers.push(this.fb.control(''));
  }

  removeTableHeader(index: number): void {
    const headers = this.getFormArray('table.headers');
    headers.removeAt(index);
  }

  addTableRow(): void {
    const rows = this.getFormArray('table.rows');
    const headers = this.getFormArray('table.headers');
    const cells = this.fb.array([]);

    // Create a cell for each header
    for (let i = 0; i < headers.length; i++) {
      cells.push(this.fb.control(''));
    }

    rows.push(cells);
  }

  removeTableRow(index: number): void {
    const rows = this.getFormArray('table.rows');
    rows.removeAt(index);
  }

  addListItem(): void {
    const items = this.getFormArray('items');
    items.push(
      this.fb.group({
        text: [''],
        checked: [false],
      })
    );
  }

  removeListItem(index: number): void {
    const items = this.getFormArray('items');
    items.removeAt(index);
  }

  addGalleryImage(): void {
    const images = this.getFormArray('images');
    images.push(
      this.fb.group({
        url: ['', Validators.required],
        caption: [''],
        altText: [''],
      })
    );
  }

  removeGalleryImage(index: number): void {
    const images = this.getFormArray('images');
    images.removeAt(index);
  }

  onSubmit(): void {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.isLoading = true;

    try {
      // this.data.component.instance.content
      const content = this.prepareContentFromForm();
      let data = this.data;
      // data.component.instance.content = content;
      data.content = content;
      this.save.emit(data);
      this.error = undefined;
    } catch (error) {
      this.error = 'An error occurred while saving the widget content.';
      console.error(error);
    } finally {
      this.isLoading = false;
    }
  }

  prepareContentFromForm(): any {
    const formValue = this.form.value;

    const content: any = {};

    switch (this.widgetType) {
      case 'TEXT':
      case 'PROCESS':
      case 'SORTING':
      case 'INTERACTIVE':
        if (formValue.heading) content.heading = formValue.heading;
        if (formValue.subHeading) content.subHeading = formValue.subHeading;
        if (formValue.description) content.description = formValue.description;

        // Add columns if they exist and are not empty
        if (formValue.columns && formValue.columns.length > 0) {
          content.columns = formValue.columns.filter(
            (col: string) => col.trim() !== ''
          );
          content.columnSize = formValue.columnSize;
        }

        // Add table if headers exist and are not empty
        if (
          formValue.table &&
          formValue.table.headers &&
          formValue.table.headers.length > 0
        ) {
          content.table = {
            headers: formValue.table.headers.filter(
              (header: string) => header.trim() !== ''
            ),
            rows: formValue.table.rows,
          };
        }
        break;

      case 'IMAGE':
        content.url = formValue.url;
        if (formValue.caption) content.caption = formValue.caption;
        if (formValue.description) content.description = formValue.description;
        if (formValue.position) content.position = formValue.position;
        if (formValue.textPosition)
          content.textPosition = formValue.textPosition;
        break;

      case 'VIDEO':
        content.url = formValue.url;
        if (formValue.title) content.title = formValue.title;
        if (formValue.description) content.description = formValue.description;
        content.isEmbedded = formValue.isEmbedded;
        break;

      case 'CARD':
        if (formValue.title) content.title = formValue.title;
        if (formValue.subtitle) content.subtitle = formValue.subtitle;
        if (formValue.content) content.content = formValue.content;
        if (formValue.imageUrl) content.imageUrl = formValue.imageUrl;
        break;

      case 'QUOTE':
        content.quote = formValue.quote;
        if (formValue.author) content.author = formValue.author;
        if (formValue.source) content.source = formValue.source;
        break;

      case 'LIST':
        content.style = formValue.style;
        if (formValue.heading) content.heading = formValue.heading;
        content.items = formValue.items;
        break;

      case 'GALLERY':
        if (formValue.title) content.title = formValue.title;
        content.images = formValue.images;
        break;
    }

    return content;
  }
}
