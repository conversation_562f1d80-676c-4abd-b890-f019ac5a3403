import {
  Component,
  inject,
  input,
  OnInit,
  resource,
  signal,
} from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import {
  Course,
  LearningInstructor,
  TrainingService,
  UserTrackingItem,
} from '@lms/core';
import {
  ExpandableComponent,
  HtmlWrapperComponent,
  ProgressComponent,
  ResourceHeaderComponent,
} from '@lms/shared';

@Component({
  selector: 'app-learning-path',
  imports: [
    ResourceHeaderComponent,
    HtmlWrapperComponent,
    ExpandableComponent,
    RouterLink,
    ProgressComponent,
  ],
  templateUrl: './learning-path.component.html',
})
export class LearningPathComponent implements OnInit {
  router = inject(Router);
  trainingService = inject(TrainingService);

  id = input<string>('');

  rating = signal<number>(0);
  comments = signal<string>('');

  source = resource<
    { instructor: LearningInstructor; tracking?: UserTrackingItem },
    string
  >({
    request: () => this.id(),
    loader: async ({ request }) => {
      const res = await this.trainingService.queryInstructorOne(request);
      if (!res) {
        throw new Error('Instructor Led not found');
      }
      return {
        instructor: res.instructor!,
        tracking: res,
      };
    },
  });

  viewCourse?: Course;

  tab = 1;

  tabs = [
    {
      id: 1,
      name: 'Course Content',
    },
    {
      id: 2,
      name: 'Instructor Bio',
    },
    {
      id: 3,
      name: 'Resource',
    },
  ];

  ngOnInit(): void {
    // this.trainingService.learningPathId.set(this.id());
  }
  goToCourse(id: string) {
    // if (!mapCollection(info, 'user_trackingsCollection').length) {
    //   await this.addTracking(item.course, item);
    // }
    this.router.navigate(['/myTraining/training', id]);
  }
}
