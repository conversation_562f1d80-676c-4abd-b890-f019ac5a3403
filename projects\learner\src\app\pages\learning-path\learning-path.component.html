<div class="flex flex-col">
  <app-resource-loader [source]="source" />
  @if (source.value(); as item) {
  <div class="grid grid-cols-2 items-center gap-5 p-12 bg-lot-dark text-white">
    <div class="flex flex-col gap-6">
      <div class="flex gap-12">
        <div class="flex flex-col">
          <h1 class="text-3xl font-bold">{{ item.instructor.name }}</h1>
          <span class="text-xs">Learning Path</span>
        </div>
        <a
          [routerLink]="['/myTraining/course-library']"
          class="bg-transparent hover:bg-lot-blue text-white font-semibold px-3 py-1 rounded-md border-2 border-white text-center h-fit"
        >
          <span class="material-symbols-outlined">reply</span>
        </a>
      </div>
      <div class="flex gap-5 max-w-3xl text-wrapper">
        <app-ui-html-wrapper 
          [content]="(item.instructor.short) + '...'"
        />
      </div>
    </div>

    <div class="w-full flex justify-end">
      <div class="flex flex-col gap-10 w-72">
        <div class="flex flex-col">
          <app-progress [status]="item.tracking?.status!" [progress]="item.tracking?.progress || 0" />
          <span class="text-white">{{
            item.tracking?.progress
              ? item.tracking?.progress + "% complete - Keep Going"
              : "0% complete - Time to Start!"
          }}</span>
        </div>
        @if (!item.tracking?.progress) {
        <button class="button-primary w-fit py-2">Start Learning Path</button>
        }
      </div>
    </div>
  </div>

  <div
    class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 mt-4"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="tab = item.id"
          class="inline-block p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === item.id"
          [class.font-bold]="tab === item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>

  <div
    class="grid gap-5 mb-20"
    [class.grid-cols-1]="!viewCourse"
    [class.grid-cols-2]="!!viewCourse"
  >
    <div class="flex mb-20">
      @if (tab === 1) {
      <div class="flex flex-col gap-5 w-full mt-8">
        @for (course of item.instructor.items; track $index; let i=$index) {
        <app-expandable [headerTemplate]="customHeader" [isExpanded]="i === 0">
          <ng-template #customHeader let-context let-toggle="toggle">
            <a
              href="javascript:void(0)"
              (click)="toggle(context)"
              class="bg-transparent border-0 outline-none h-fit flex justify-between"
            >
              <span class="text-lot-blue font-semibold text-xl">
                {{ course.category }}
              </span>
              <span
                class="material-symbols-outlined text-lot-dark font-bold text-3xl"
                >{{ context.isExpanded ? "remove" : "add" }}</span
              >
            </a>
          </ng-template>
          <div class="px-10 py-3 bg-lot-light-blue/10 rounded-lg">
            @for (module of course.courses; track $index) {
            <div class="flex items-center justify-between py-5 border-b">
              <span>{{ module.name }}</span>
              <div class="flex items-center gap-2">
                <!-- 
                  (click)="viewCourse = module" -->
                <button
                  class="button-primary-outline"
                >
                  Learn More
                </button>
                <button
                  class="button-primary"
                  (click)="goToCourse(module.id!)"
                >
                  Start Course
                </button>
              </div>
            </div>
            }
          </div>
        </app-expandable>
        }
      </div>
      } @if (tab === 2) {
      <div class="flex flex-col gap-5 pt-5">
        <div class="flex gap-5">
          <div
            class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
          >
            <svg
              class="absolute size-14 text-gray-400 pl-2"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-lot-blue text-xl">
              {{ item.instructor.creator.name }}
            </h3>
            <p class="text-xs text-lot-dark">{{ item.instructor.creator.email }}</p>
          </div>
        </div>
        <div>
          <p>
            {{ item.instructor.creator.bio }}
          </p>
        </div>
      </div>
      } @if (tab === 3) {
      <div class="flex flex-col gap-1 w-[389px] md:w-[440px]">
        @for (item of item.instructor.resources; track $index) {
        <div class="flex items-center justify-between p-4 border-b rounded-md">
          <div class="flex items-center">
            <span class="material-symbols-outlined mr-4 border p-3 rounded-lg"
              >description</span
            >
            <div>
              <p class="font-semibold">{{ item.name }}</p>
              <p class="text-sm text-gray-500">PDF</p>
            </div>
          </div>
          <button class="button-primary">View</button>
        </div>
        }
      </div>

      }
    </div>

    @if (viewCourse) {
    <div class="flex flex-col gap-5 rounded-3xl bg-lot-light-blue/10 p-5">
      <div class="w-[574px] h-80">
        <img
          src="{{ viewCourse.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
      </div>
      <div class="flex items-center justify-between">
        <span class="font-semibold text-lot-dark text-xl">{{
          viewCourse.name
        }}</span>
        <button
          class="button-primary w-fit"
          (click)="goToCourse(viewCourse.id!)"
        >
          Start Course
        </button>
      </div>
      <div class="flex flex-col gap-4 mb-6">
        <span class="text-sm font-semibold pb-3 border-b border-lot-blue w-fit">
          Course Brief
        </span>
        <app-ui-html-wrapper
          class="text-xs"
          [content]="viewCourse.description"
        />
        <a
          href="javascript:void(0)"
          class="text-xs italic text-lot-blue underline"
          (click)="viewCourse = undefined"
        >
          Close Panel
        </a>
      </div>
    </div>
    }
  </div>

  }@else {
  <p class="text-center py-4">No active loads currently.</p>
  }
</div>
