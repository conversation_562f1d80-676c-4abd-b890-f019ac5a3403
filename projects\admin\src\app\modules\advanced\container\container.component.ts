import { Component, computed, inject, OnInit, resource, signal } from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { InstructorFilter, LearningInstructor, LearningInstructorService } from '@lms/core';
import { ListComponent } from '../components/list/list.component';
import { FloatingComponent, ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-instructor-container',
  imports: [
    CommonModule,
    MatPaginatorModule,
    MatTableModule,
    ReactiveFormsModule,
    ListComponent,
    ResourceHeaderComponent,
    FloatingComponent,
  ],
  templateUrl: 'container.component.html',
})
export class InstructorContainerComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  service = inject(LearningInstructorService);

  filter = signal<InstructorFilter | undefined>(undefined);
  source = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request) {
        return {
          data: [] as LearningInstructor[],
          total: 0,
        };
      }
      const { data, error, count } = await this.service.get(request);
      if (error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return {
        data,
        total: count,
      };
    },
  });

  data = computed(
    () => (this.source.value()?.data || []) as LearningInstructor[]
  );

  totalCount = computed(() => this.source.value()?.total || 0);

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'Learning Paths',
    },
    {
      id: 2,
      name: 'Instructor Led',
    },
  ];

  sortBy = [
    { id: 'nameASC', name: 'A to Z' },
    { id: 'nameDESC', name: 'Z to A' },
    { id: 'createdDateASC', name: 'Oldest' },
    { id: 'createdDateDESC', name: 'Latest' },
  ];
  search = signal<string | undefined>(undefined);
  sort = signal<string | undefined>(undefined);

  ngOnInit(): void {
    this.filter.set({
      type: 'LEARNINGPATH',
      paging: {
        page: 1,
        size: 10,
      },
    });
  }

  setTab(tab: number) {
    this.tab = tab;
    if (tab === 1) {
      this.filter.set({
        type: 'LEARNINGPATH',
        paging: {
          page: 1,
          size: 10,
        },
      });
    }
    if (tab === 2) {
      this.filter.set({
        type: 'INSTRUCTORLED',
        paging: {
          page: 1,
          size: 10,
        },
      });
    }
  }

  onPage(page: number) {}
}
