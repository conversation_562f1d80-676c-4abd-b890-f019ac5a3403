import { MatDividerModule } from '@angular/material/divider';
import { SelectionModel } from '@angular/cdk/collections';
import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import {
  AudienceItemEnroll,
  GroupItem,
  setsAreEqual,
  Team,
  UserAudienceItem,
  UserItem,
} from '@lms/core';
import { debounceTime, distinctUntilChanged, startWith } from 'rxjs/operators';
import { MatChipsModule } from '@angular/material/chips';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';

@Component({
  selector: 'app-assign-user-v2',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatDatepickerModule,
    MatCheckboxModule,
    MatDividerModule,
    MatChipsModule,
    MatPaginatorModule,
  ],
  templateUrl: './a-user-v2.component.html',
  styleUrls: ['./a-user-v2.component.scss'],
})
export class AssignUserV2Component implements OnChanges, OnInit {
  @Input() public data: Array<UserAudienceItem> = [];
  // @Input() public users: Array<UserAudienceItem> = [];
  @Input() userPaged: {
    data: {
      user: UserItem;
      enrolls: {
        duedate?: string;
        type?: 'user' | 'subgroup' | 'group' | 'course' | null | undefined;
        id?: string | undefined;
      }[];
    }[];
    count: number;
  } = { data: [], count: 0 };
  @Input() filteredUsers: Array<UserAudienceItem> = [];
  @Input() public groups: { [key: string]: Team } = {};
  @Input() public subgroups: { [key: string]: GroupItem } = {};
  @Output() public save = new EventEmitter<{
    new: Array<any>;
    deleted: Array<string>;
  }>();
  @Output() public onAssignmentItemClicked =
    new EventEmitter<AudienceItemEnroll>();
  @Output() public search = new EventEmitter<string>();
  @Output() public pageChange = new EventEmitter<number>();

  userSearchKey = new FormControl('');
  selection: { [key: string]: UserAudienceItem } = {};

  get users() {
    return this.userPaged?.data ?? [];
  }

  get items() {
    return this.filteredUsers?.length ? this.filteredUsers : this.users ?? [];
  }
  get itemsSelected() {
    return Object.values(this.selection) ?? [];
  }

  get allSelected(): boolean {
    return (
      !!(this.items ?? []).length &&
      Object.keys(this.selection).length === this.items.length
    );
  }
  usersAdded: UserItem[] = [];
  usersDeleted: UserItem[] = [];
  hasSearched = false;
  get displaySaveButton() {
    return !setsAreEqual(
      new Set(
        this.users
          ?.filter((d) => this.isDirectAssigned(d))
          .map((d) => d.user.id) ?? []
      ),
      new Set(
        Object.values(this.selection)
          ?.filter((d) => this.isDirectAssigned(d))
          .map((d) => d.user.id) ?? []
      )
    );
  }

  ngOnInit(): void {
    this.userSearchKey.valueChanges
      .pipe(startWith(''), debounceTime(1000), distinctUntilChanged())
      .subscribe((term) => {
        this.search.emit(term?.toLowerCase()?.trim());
        this.hasSearched = !!term?.toLowerCase()?.trim();
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.users?.length && !this.hasSearched) {
      this.selection = {};
      this.selection = Object.assign(
        {},
        ...this.items
          .filter((x) => !!x.enrolls?.length)
          .map((x) => ({
            [x.user.id]: {
              user: { ...x.user },
              enrolls: [...(x.enrolls ?? [])],
            },
          }))
      );
    }
  }

  toggleSelection() {
    this.items.forEach((item) => this.onUserSelectionChanged(undefined, item));
  }

  handlePageEvent(e: PageEvent) {
    // this.pageIndex = e.pageIndex;
    this.pageChange.emit(e.pageIndex);
  }

  onUserSelectionChanged(e: any, item: UserAudienceItem) {
    e?.preventDefault();
    if (this.selection[item.user.id]) {
      if (this.isDirectAssigned(this.selection[item.user.id])) {
        const idx =
          this.selection[item.user.id].enrolls?.findIndex(
            (x) => x.type === 'user'
          ) ?? -1;
        if (idx >= 0) this.selection[item.user.id].enrolls?.splice(idx, 1);
      } else {
        this.selection[item.user.id].enrolls = [
          ...(this.selection[item.user.id].enrolls ?? []),
          { type: 'user', id: item.user.id },
        ];
      }
      if (!this.selection[item.user.id].enrolls?.length) {
        delete this.selection[item.user.id];
      }
    } else {
      this.selection[item.user.id] = {
        user: { ...item.user },
        enrolls: [...(item.enrolls ?? [])],
      };

      if (!this.isDirectAssigned(this.selection[item.user.id])) {
        this.selection[item.user.id].enrolls = [
          ...(this.selection[item.user.id].enrolls ?? []),
          { type: 'user', id: item.user.id },
        ];
      }
    }
  }

  saveItems() {
    const existings = this.users
      .filter((d) => this.isDirectAssigned(d))
      .map((u) => u.user.id);
    const response = {
      new: Object.values(this.selection)
        .filter((d) => this.isDirectAssigned(d))
        .filter((x) => !existings.includes(x.user.id ?? ''))
        .map((u) => u.user),
      deleted: existings.filter(
        (x) =>
          Object.values(this.selection)
            .filter((d) => this.isDirectAssigned(d))
            .filter((d) => d.user.id === x).length == 0
      ),
    };
    this.save.emit(response);
  }
  getNAssigns(item: UserAudienceItem, type: 'subgroup' | 'group') {
    return item.enrolls?.filter((e) => e.type === type)?.length ?? 0;
  }
  isDirectAssigned(item: UserAudienceItem) {
    const r = item?.enrolls?.filter((e) => e.type === 'user')?.length ?? 0;
    return r > 0;
  }
  isUserAssigned(item: UserAudienceItem) {
    return !!this.selection[item.user.id];
  }
  getAssignments(item: UserAudienceItem) {
    return (
      item.enrolls?.map((e) => {
        let label = '';
        if (e.type === 'user') {
          label = 'Directly';
        }
        if (e.type === 'subgroup') {
          label = 'Subgroup';
        }
        if (e.type === 'group') {
          label = 'Group';
        }
        return label;
      }) ?? []
    );
  }
  canUpdateDueDate(item: UserAudienceItem, idx: number) {
    return (
      this.items
        .filter((x) => x.user.id === item.user.id)
        ?.at(0)
        ?.enrolls?.at(idx)?.id === item.enrolls?.at(idx)?.id
    );
  }
  assignmentItemClicked(item: UserAudienceItem, idx: number) {
    if (
      this.items
        .filter((x) => x.user.id === item.user.id)
        ?.at(0)
        ?.enrolls?.at(idx)?.id === item.enrolls?.at(idx)?.id
    )
      this.onAssignmentItemClicked.emit(item.enrolls?.at(idx));
  }
}
