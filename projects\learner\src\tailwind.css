@tailwind base;
@tailwind components;
@tailwind utilities;

/* -lot-danger: #F95757; */

@layer base {
  h1 {
    @apply font-semibold text-2xl;
  }

  h2 {
    @apply font-semibold text-xl;
  }

  h3 {
    @apply font-semibold text-base;
  }

  h4 {
    @apply font-semibold text-sm;
  }
}

:root {
  --lot-danger: #f95757;
}

@layer components {
  .button-primary {
    @apply bg-lot-blue hover:bg-lot-blue/25 text-white font-semibold py-1 px-4 rounded-md border-0;
    @apply text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-primary-outline {
    @apply bg-white hover:bg-lot-blue hover:text-white font-semibold py-1 px-4 rounded-md border-2 border-lot-blue;
    @apply text-lot-blue text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-ghost-outline {
    @apply bg-transparent font-semibold py-1 px-4 rounded-md hover:border-2 hover:border-lot-blue text-lot-blue text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-primary-dark {
    @apply bg-lot-dark hover:bg-lot-dark/55 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-secondary {
    @apply bg-lot-blue hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-danger {
    @apply bg-accent hover:bg-red-700 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-outline {
    @apply bg-white hover:bg-secondary hover:text-white font-semibold py-2 px-4 rounded border-2 border-secondary;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-outline-danger {
    @apply bg-white hover:bg-accent text-accent hover:text-white font-semibold py-2 px-4 rounded border-2 border-accent;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }

  .form-input {
    @apply w-full px-3 flex flex-col;

    label {
      @apply text-sm font-semibold px-1 mb-2;
    }

    input,
    textarea,
    select {
      @apply w-full pl-4 pr-3 py-2 rounded-md focus:border-primary-100 border-2 border-gray-200 outline-none;

      &.ng-invalid,
      &.invalid {
        color: var(--lot-danger);
      }
    }
  }

  .router-link-active {
    @apply border-blue-500 text-blue-500 border-b-4;
  }
}

input.ng-dirty.ng-invalid,
textarea.ng-dirty.ng-invalid,
select.ng-dirty.ng-invalid {
  border: 2px solid var(--lot-danger);
}

@layer utilities {
  .scrollbar::-webkit-scrollbar {
    width: 15px;
    height: 50px;
  }

  .scrollbar::-webkit-scrollbar-track {
    border-radius: 100vh;
    background: #e5e5e6;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    background: #213d77;
    border-radius: 100vh;
    border: 3px solid #e5e5e6;
  }

  .scrollbar::-webkit-scrollbar-thumb:hover {
    background: #213d77;
  }
}
