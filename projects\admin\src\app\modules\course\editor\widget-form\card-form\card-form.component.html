<form [formGroup]="form" (ngSubmit)="onSubmit()" class="flex flex-col h-full">
  <div class="scrollbar flex-grow">
    @if (error) {
    <div class="text-lot-danger text-sm">{{ error }}</div>
    } @if (!widgetContent) {
    <div class="text-lot-danger text-sm">Content not found</div>
    } @else {
    <div class="flex flex-col gap-6 p-4 mt-3">
      @switch (widgetType) { @case ('TEXT') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('PROCESS') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('SORTING') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('INTERACTIVE') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('IMAGE') {
      <ng-container *ngTemplateOutlet="imageWidget" />
      } @case ('VIDEO') {
      <ng-container *ngTemplateOutlet="videoWidget" />
      } @case ('CARD') {
      <ng-container *ngTemplateOutlet="cardWidget" />
      } @case ('QUOTE') {
      <ng-container *ngTemplateOutlet="quoteWidget" />
      } @case ('LIST') {
      <ng-container *ngTemplateOutlet="listWidget" />
      } @case ('GALLERY') {
      <ng-container *ngTemplateOutlet="galleryWidget" />
      } }
    </div>
    }
  </div>
  <!-- <div class="flex gap-3 justify-end items-center p-4">
    <button
      type="submit"
      class="button-primary w-fit py-1.5"
      [disabled]="isLoading"
    >
      Save
    </button>
  </div> -->
</form>

<ng-template #textWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if(widgetContent.heading) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="heading"
          formControlName="heading"
          placeholder="Enter heading"
        />
        <label for="heading">Heading</label>
      </div>
    </div>
    } @if(widgetContent.subHeading){
    <div class="form-lot-input">
      <div class="field">
        <input
          id="subHeading"
          formControlName="subHeading"
          placeholder="Enter sub heading"
        />
        <label for="subHeading">Sub Heading</label>
      </div>
    </div>
    } @if(widgetContent.description){

    <app-rich-text [form]="form" />
    <!-- <div class="form-lot-input">
      <div class="field">
        <quill-editor
          formControlName="description"
          [styles]="editorStyle"
          placeholder="Enter description"
        ></quill-editor>
        <label>Description</label>
      </div>
    </div> -->
    } @if ( widgetContent.columnSize) {
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>Columns</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="flex flex-col gap-4 p-2">
        <div class="form-lot-input">
          <div class="field">
            <select id="columnSize" formControlName="columnSize">
              <option [value]="1">1</option>
              <option [value]="2">2</option>
              <option [value]="3">3</option>
              <option [value]="4">4</option>
            </select>
            <label for="columnSize">Number of Columns</label>
          </div>
        </div>

        <div formArrayName="columns" class="flex flex-col gap-2">
          @for (column of getFormArray('columns').controls; track $index) {
          <div class="flex items-center gap-2 group">
            <div class="form-lot-input flex-grow">
              <div class="field">
                <input
                  [id]="'column-' + $index"
                  [formControlName]="$index"
                  placeholder="Enter column content"
                />
                <label [for]="'column-' + $index"
                  >Column {{ $index + 1 }}</label
                >
              </div>
            </div>
            <a
              href="javascript:void(0)"
              (click)="removeColumn($index)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addColumn()"
          >
            <i class="fa-solid fa-plus"></i> Add Column
          </button>
        </div>
      </div>
    </mat-expansion-panel>
    } @if (widgetContent.table) {
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>Table</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="flex flex-col gap-4 p-2" formGroupName="table">
        <!-- Table Headers -->
        <div formArrayName="headers" class="flex flex-col gap-2">
          <h3 class="text-lot-dark font-medium">Headers</h3>

          @for (header of getFormArray('table.headers').controls; track $index)
          {
          <div class="flex items-center gap-2 group">
            <div class="form-lot-input flex-grow">
              <div class="field">
                <input
                  [id]="'header-' + $index"
                  [formControlName]="$index"
                  placeholder="Enter header"
                />
                <label [for]="'header-' + $index"
                  >Header {{ $index + 1 }}</label
                >
              </div>
            </div>

            <a
              href="javascript:void(0)"
              (click)="removeTableHeader($index)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addTableHeader()"
          >
            <i class="fa-solid fa-plus"></i> Add Header
          </button>
        </div>

        <!-- Table Rows -->
        <div formArrayName="rows" class="flex flex-col gap-4 mt-4">
          <h3 class="text-lot-dark font-medium">Rows</h3>

          @for (row of getFormArray('table.rows').controls; track $index; let
          i=$index) {
          <div class="flex flex-col gap-2 p-2 border rounded-md group">
            <div class="flex justify-between items-center">
              <h4 class="text-lot-dark">Row {{ i + 1 }}</h4>

              <a
                href="javascript:void(0)"
                (click)="removeTableRow($index)"
                class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
              >
                <i class="fa-solid fa-trash"></i>
              </a>
            </div>

            <div
              [formArrayName]="i"
              class="grid gap-2"
              [ngStyle]="{
                'grid-template-columns':
                  'repeat(' + getFormArray('table.headers').length + ', 1fr)'
              }"
            >
              @for (cell of getFormForChild(row); track $index; let
              cellIndex=$index) {
              <div class="form-lot-input">
                <div class="field">
                  <input
                    [id]="'cell-' + i + '-' + cellIndex"
                    [formControlName]="cellIndex"
                    placeholder="Enter cell content"
                  />
                  <label [for]="'cell-' + i + '-' + cellIndex">
                    {{
                      getFormArray("table.headers").at(cellIndex).value ||
                        "Cell"
                    }}
                  </label>
                </div>
              </div>
              }
            </div>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addTableRow()"
            [disabled]="getFormArray('table.headers').length === 0"
          >
            <i class="fa-solid fa-plus"></i> Add Row
          </button>
        </div>
      </div>
    </mat-expansion-panel>
    }
  </div>
</ng-template>

<ng-template #imageWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <select id="fileUrlType" formControlName="fileUrlType">
          <option value="1">Upload Here</option>
          <option value="2">Use Existing Url (your own)</option>
        </select>
        <label for="fileUrlType">Image Type</label>
      </div>
    </div>
    @if (+form.get('fileUrlType')?.value === 1) {
    <div class="border rounded-lg overflow-hidden shadow-sm h-48">
      <div class="flex relative">
        <app-rich-image
          [url]="widgetContent.url"
          (content)="form.get('url')?.setValue($event)"
        />
      </div>
    </div>
    } @if (+form.get('fileUrlType')?.value === 2) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="url"
          formControlName="url"
          placeholder="Enter image URL"
          required
        />
        <label for="url">Image URL</label>
      </div>
    </div>
    }

    <!--  -->
    <div class="flex flex-col gap-3">
      <label>Position</label>
      <div class="flex items-center gap-4">
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posCenter"
            name="position"
            formControlName="position"
            value="center"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posCenter">Center</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posFull"
            name="position"
            formControlName="position"
            value="full"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posFull">Full</label>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-3 my-4">
      <label>Text Position</label>
      <div class="flex items-center gap-4">
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posBottom"
            name="textPosition"
            formControlName="textPosition"
            value="bottom"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posBottom">Bottom</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posRight"
            name="textPosition"
            formControlName="textPosition"
            value="right"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posRight">Right</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posOverlay"
            name="textPosition"
            value="overlay"
            formControlName="textPosition"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posOverlay">Overlay</label>
        </div>
      </div>
    </div>

    @if (widgetContent.caption) {
    <div class="form-lot-input my-4">
      <div class="field">
        <input
          id="caption"
          formControlName="caption"
          placeholder="Enter image caption"
        />
        <label for="caption">Caption</label>
      </div>
    </div>
    } @if (widgetContent.description) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="description"
          formControlName="description"
          placeholder="Enter description"
        />
        <label for="description">Description</label>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #videoWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <input
          id="url"
          type="url"
          formControlName="url"
          placeholder="Enter video URL"
          required
        />
        <label for="url">Video URL</label>
      </div>
    </div>

    @if (widgetContent.title) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="videoTitle"
          formControlName="title"
          placeholder="Enter video title"
        />
        <label for="videoTitle">Title</label>
      </div>
    </div>
    } @if (widgetContent.description) {

    <app-rich-text [form]="form" />
    <!-- <div class="form-lot-input">
      <div class="field">
        <quill-editor
          formControlName="description"
          [styles]="editorStyle"
          placeholder="Enter video description"
        ></quill-editor>
        <label>Description</label>
      </div>
    </div> -->
    } @if (widgetContent.isEmbedded) {
    <mat-checkbox formControlName="isEmbedded"
      >Embed video (instead of linking)</mat-checkbox
    >
    }
  </div>
</ng-template>

<ng-template #cardWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.title) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="cardTitle"
          formControlName="title"
          placeholder="Enter card title"
        />
        <label for="cardTitle">Title</label>
      </div>
    </div>
    } @if (widgetContent.subtitle) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="cardSubtitle"
          formControlName="subtitle"
          placeholder="Enter card subtitle"
        />
        <label for="cardSubtitle">Subtitle</label>
      </div>
    </div>
    } @if (widgetContent.content) {

    <app-rich-text [form]="form" />
    <!-- <div class="form-lot-input">
      <div class="field">
        <quill-editor
          formControlName="content"
          [styles]="editorStyle"
          placeholder="Enter card content"
        ></quill-editor>
        <label>Content</label>
      </div>
    </div> -->
    } @if (widgetContent.imageUrl) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="cardImageUrl"
          formControlName="imageUrl"
          placeholder="Enter image URL (optional)"
        />
        <label for="cardImageUrl">Image URL</label>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #quoteWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <textarea
          id="quoteText"
          formControlName="quote"
          placeholder="Enter quote text"
          rows="4"
          required
        ></textarea>
        <label for="quoteText">Quote</label>
      </div>
    </div>

    @if (widgetContent.author) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="quoteAuthor"
          formControlName="author"
          placeholder="Enter quote author"
        />
        <label for="quoteAuthor">Author</label>
      </div>
    </div>
    } @if (widgetContent.source) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="quoteSource"
          formControlName="source"
          placeholder="Enter quote source"
        />
        <label for="quoteSource">Source</label>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #listWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.heading) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="listHeading"
          formControlName="heading"
          placeholder="Enter list heading"
        />
        <label for="listHeading">Heading</label>
      </div>
    </div>
    }

    <div class="form-lot-input">
      <div class="field">
        <select id="listStyle" formControlName="style">
          <option value="disc">Bullet (Disc)</option>
          <option value="circle">Circle</option>
          <option value="number">Numbered</option>
          <option value="checkbox">Checkbox</option>
        </select>
        <label for="listStyle">List Style</label>
      </div>
    </div>

    <div formArrayName="items" class="flex flex-col gap-2">
      <h3 class="text-lot-dark font-medium">List Items</h3>

      @for (item of getFormArray('items').controls; track $index) {
      <div [formGroupName]="$index" class="flex items-center gap-2 group">
        <div class="form-lot-input flex-grow">
          <div class="field">
            <input
              [id]="'listItem-' + $index"
              formControlName="text"
              placeholder="Enter list item"
            />
            <label [for]="'listItem-' + $index">Item {{ $index + 1 }}</label>
          </div>
        </div>

        @if (form.get('style')?.value === 'checkbox') {
        <div class="flex items-center">
          <input
            type="checkbox"
            [id]="'itemChecked-' + $index"
            formControlName="checked"
            class="mr-2"
          />
          <label [for]="'itemChecked-' + $index">Checked</label>
        </div>
        }
        <a
          href="javascript:void(0)"
          (click)="removeListItem($index)"
          class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </div>
      }

      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addListItem()"
      >
        <i class="fa-solid fa-plus"></i> Add Item
      </button>
    </div>
  </div>
</ng-template>

<ng-template #galleryWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.title) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="galleryTitle"
          formControlName="title"
          placeholder="Enter gallery title"
        />
        <label for="galleryTitle">Title</label>
      </div>
    </div>
    }

    <div formArrayName="images" class="flex flex-col gap-4">
      <h3 class="text-lot-dark font-medium">Images</h3>

      @for (image of getFormArray('images').controls; track $index) {
      <div
        [formGroupName]="$index"
        class="flex flex-col gap-2 p-4 border rounded-md group"
      >
        <div class="flex justify-between items-center">
          <h4 class="text-lot-dark">Image {{ $index + 1 }}</h4>
          <a
            href="javascript:void(0)"
            (click)="removeGalleryImage($index)"
            class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
          >
            <i class="fa-solid fa-trash"></i>
          </a>
        </div>

        <div class="form-lot-input">
          <div class="field">
            <input
              [id]="'galleryImageUrl-' + $index"
              formControlName="url"
              placeholder="Enter image URL"
              required
            />
            <label [for]="'galleryImageUrl-' + $index">Image URL</label>
          </div>
        </div>

        <div class="form-lot-input">
          <div class="field">
            <input
              [id]="'galleryCaption-' + $index"
              formControlName="caption"
              placeholder="Enter image caption"
            />
            <label [for]="'galleryCaption-' + $index">Caption</label>
          </div>
        </div>

        <div class="form-lot-input">
          <div class="field">
            <input
              [id]="'galleryAltText-' + $index"
              formControlName="altText"
              placeholder="Enter alt text for accessibility"
            />
            <label [for]="'galleryAltText-' + $index">Alt Text</label>
          </div>
        </div>
      </div>
      }

      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addGalleryImage()"
      >
        <i class="fa-solid fa-plus"></i> Add Image
      </button>
    </div>
  </div>
</ng-template>
