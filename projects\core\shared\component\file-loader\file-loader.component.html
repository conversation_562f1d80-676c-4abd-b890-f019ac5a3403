<div class="flex flex-col gap-1">
  @if (fileUrl) {
  <div class="flex justify-between items-center">
    <span class="text-lot-dark-gray/50 italic">draft preview</span>
    <a
      href="javascript:void(0)"
      (click)="onRemove(null)"
      class="text-lot-danger/70 text-xl"
    >
      <i class="fa-solid fa-trash"></i>
    </a>
  </div>
  <div class="rounded-2xl shadow-md border border-gray-200">
    @if (type === 'image') {
    <img
      class="object-cover w-full h-full rounded-2xl"
      [src]="fileUrl"
      alt=""
    />
    } @if (type === 'video') {
      <video controls class="w-full h-full rounded-2xl">
        <source [src]="fileUrl" />
      </video>
    } 
    @if (type === 'audio') {
      <audio
          controls
          class="w-full h-12 [&::-webkit-media-controls-panel]:bg-lot-blue [&::-webkit-media-controls-current-time-display]:text-white [&::-webkit-media-controls-time-remaining-display]:text-white [&::-webkit-media-controls-timeline]:text-white [&::-webkit-media-controls-play-button]:text-white [&::-webkit-media-controls-mute-button]:text-white rounded-lg"
        >
          <source [src]="fileUrl" />
          Your browser does not support the audio element.
        </audio>
    }
  </div>
  } @if(error) {
  <p class="text-red-500 text-center">{{ error }}</p>
  } @if (!fileUrl) {
  <ngx-dropzone
    [accept]="fileType"
    [maxFileSize]="MAX_SIZE"
    [multiple]="false"
    (change)="onSelect($event)"
  >
    <ngx-dropzone-label class="px-3 w-full">Upload/Drag & Drop your file here</ngx-dropzone-label>
    <!-- <ngx-dropzone-preview
      *ngFor="let f of files"
      [removable]="true"
      (removed)="onRemove(f)"
    >
      <ngx-dropzone-label>{{ f.name }} ({{ f.type }})</ngx-dropzone-label>
    </ngx-dropzone-preview> -->
  </ngx-dropzone>
  }
</div>
