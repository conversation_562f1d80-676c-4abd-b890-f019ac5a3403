<div class="flex flex-col h-full">
  <div class="flex justify-between items-end">
    <div class="text-sm font-medium text-center text-gray-500">
      <ul class="flex flex-wrap -mb-px">
        <li class="me-2">
          <a
            href="javascript:void(0)"
            (click)="setTab(1)"
            class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
            [class.text-lot-dark]="tab === 1"
            [class.border-transparent]="tab !== 1"
            [class.border-lot-blue]="tab === 1"
          >
            User Management
          </a>
        </li>
        <li class="me-2">
          <a
            href="javascript:void(0)"
            (click)="setTab(2)"
            class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
            [class.text-lot-dark]="tab === 2"
            [class.border-transparent]="tab !== 2"
            [class.border-lot-blue]="tab === 2"
          >
            Team Management
          </a>
        </li>
      </ul>
    </div>

    <button type="button" (click)="add()" class="button-primary w-36 py-2 px-6">
      {{ type() === 'USER'  ? "Add Users +" : type() === 'TEAM' ? 'Add Team' : "Add Group" }}
    </button>
  </div>

  <div class="flex-grow my-10 bg-white p-8 rounded-xl shadow-md w-full">
    @if (tab === 1) {
    <app-users class="w-full h-full" />
    } @if (tab === 2) {
    <app-teams class="w-full h-full" />
    }
  </div>
</div>
