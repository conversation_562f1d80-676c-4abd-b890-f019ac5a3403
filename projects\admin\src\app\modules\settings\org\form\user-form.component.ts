import { MatIconModule } from '@angular/material/icon';
import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { Organization, OrganizationService, ToastMessageType } from '@lms/core';
import { markControlsDirty } from '@lms/core';
import { MatSelectModule } from '@angular/material/select';
import { NgTemplateOutlet } from '@angular/common';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { ValidationTextComponent } from '@lms/shared';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-org-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatSelectModule,
    NgTemplateOutlet,
    MatChipsModule,
    MatIconModule,
    MatProgressBarModule,
    ValidationTextComponent,
    NgxMaskDirective,
  ],
  templateUrl: 'user-form.component.html',
})
export class OrgFormComponent implements OnInit {
  service = inject(OrganizationService);
  public dialogRef: MatDialogRef<OrgFormComponent> = inject(MatDialogRef);

  data: Organization = inject(MAT_DIALOG_DATA);

  isLoading = false;
  error?: string;

  form = new FormGroup({
    id: new FormControl(''),
    email: new FormControl('', [Validators.required, Validators.email]),
    phone: new FormControl('', Validators.required),
    name: new FormControl('', Validators.required),
    nbEmployees: new FormControl('', Validators.required),
    pocName: new FormControl('', Validators.required),
    pocTitle: new FormControl(''),
    description: new FormControl('', Validators.required),
    address: new FormControl('', Validators.required),
    website: new FormControl(''),
  });

  ngOnInit(): void {
    if (this.data) {
      this.form.patchValue({
        id: this.data.id,
        email: this.data.contact?.email,
        phone: this.data.contact?.phone,
        name: this.data.name,
        nbEmployees: this.data.about!['nbEmployees'],
        address: this.data.about!['address'],
        website: this.data.about!['website'],
        pocName: this.data.contact?.name,
        pocTitle: this.data.contact?.title,
        description: this.data.description,
      });
    }
  }

  async onSubmit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    this.isLoading = true;
    const formData = this.form.getRawValue();

    const res = await this.service.saveOrganization({
      ...this.data,
      name: formData.name!,
      description: formData.description!,
      contact: {
        name: formData.pocName!,
        email: formData.email!,
        phone: formData.phone!,
        title: formData.pocTitle!,
      },
      about: {
        nbEmployees: formData.nbEmployees,
        address: formData.address,
        website: formData.website,
      },
    });

    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }

    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Organization saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.dialogRef.close();
  }
}
