import { AccountStatus } from '@lms/core';

export type UploadRequest = {
  Email: string;
  Role: string;
  FirstName: string;
  LastName: string;
  Phone: string;
  Team: string;
  Group: string;
};

export type UploadItem = {
  email: string;
  role: AccountStatus;
  phone?: string;
  firstname?: string;
  lastname?: string;
  teams?: string[];
  groups?: string[];
};

export class UploadUtils {
  static onFileLoad(input: HTMLInputElement) {
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      if (
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.name.endsWith('.xlsx')
      ) {
        return { file };
      } else {
        return { file: null, error: 'Please upload a valid EXCEL file.' };
      }
    }
    return { file: null, error: 'Please select a file.' };
  }

  static validateRows(rows: UploadRequest[]) {
    return rows.map((row, i) => {
      const errors: string[] = [];
      if (!UploadUtils.validateEmail(row.Email)) {
        errors.push('Invalid email');
      }
      if (!UploadUtils.validateRole(row.Role)) {
        errors.push('Invalid role');
      }
      if (row.Phone && !UploadUtils.validatePhone(row.Phone)) {
        errors.push('Invalid phone');
      }

      return {
        row: i + 1,
        data: {
          email: row.Email?.trim(),
          firstname: row.FirstName?.trim(),
          lastname: row.LastName?.trim(),
          phone: row.Phone?.toString()?.trim(),
          role: row.Role,
          teams: row.Team ? row.Team.split(',').map((v) => v.trim()) : [],
          groups: row.Group ? row.Group.split(',').map((v) => v.trim()) : [],
        } as UploadItem,
        error: errors.join(', '),
      };
    });
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validateRole(role: string): boolean {
    const validRoles = [
      AccountStatus.ADMINISTRATOR,
      AccountStatus.MANAGER,
      AccountStatus.LEARNER,
    ];
    if (!role) {
      return false;
    }
    return validRoles.includes(role.toLowerCase() as AccountStatus);
  }

  static validatePhone(phone: string): boolean {
    const phoneRegex = /^\d{10}$/;
    return phoneRegex.test(phone);
  }
}
