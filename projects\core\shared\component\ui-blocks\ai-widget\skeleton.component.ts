import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-skeleton',
  template: `
    <div
      class="bg-gray-300 animate-pulse rounded"
      [style.width]="width"
      [style.height]="height"
      [class.rounded-full]="shape === 'circle'"
      [class.rounded-lg]="shape === 'rectangle'"
      [class.rounded-none]="shape === 'none'"
    ></div>
  `,
  styles: [],
})
export class SkeletonComponent {
  @Input() width: string = '100%';
  @Input() height: string = '1rem';
  @Input() shape: 'rectangle' | 'circle' | 'none' = 'rectangle';

  static base64ToFile(
    base64String: string,
    filename: string,
    mimeType = 'image/png'
  ) {
    const byteString = atob(base64String);

    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }
    const blob = new Blob([ab], { type: mimeType });
    return new File([blob], filename, {
      type: mimeType,
      lastModified: Date.now(),
    });
  }
}
