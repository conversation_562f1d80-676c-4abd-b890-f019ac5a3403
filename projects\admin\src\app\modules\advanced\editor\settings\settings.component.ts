import { Component, inject, Input, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Course,
  LearningInstructor,
  LearningInstructorService,
  markControlsDirty,
  MediaBucket,
  MediaCenterService,
  stripHtml,
  ToastMessageType,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { FileLoaderComponent, FileType, RichTextComponent } from '@lms/shared';
import { MatDialog } from '@angular/material/dialog';
import { MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';
import { filter, switchMap } from 'rxjs';

@Component({
  selector: 'app-instructor-settings',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    RichTextComponent,
    FileLoaderComponent,
    MatChipsModule,
    MatIconModule,
  ],
  templateUrl: 'settings.component.html',
})
export class SettingsComponent implements OnInit {
  readonly mediaService = inject(MediaCenterService);
  readonly service = inject(LearningInstructorService);
  readonly dialog = inject(MatDialog);

  @Input({ required: true }) data: LearningInstructor;

  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl('', Validators.required),
  });

  uploadType: FileType = 'image';

  isLoading = false;
  error?: string;
  uploadedFile?: File;

  get typeLabel() {
    return this.data.type === 'LEARNINGPATH'
      ? 'Learning Path'
      : 'Instructor Led';
  }

  get f() {
    return this.form.controls;
  }
  get author() {
    return this.data.creator;
  }

  ngOnInit(): void {
    if (this.data) {
      this.form.patchValue({
        name: this.data.name,
        description: this.data.description,
      });
    }

    this.service.actionTrigger
      .pipe(
        filter((res) => res.type === 'SETTINGS'),
        switchMap(() => this.onSubmit())
      )
      .subscribe(() => {});
  }

  async onFileUploaded(files: File[]) {
    this.uploadedFile = files[0];
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const cover = await this.saveCover();
    const payload = {
      id: this.data.id,
      name: this.form.value.name,
      description: this.form.value.description,
      cover: cover,
      short: stripHtml(this.form.value.description!)?.substring(0, 250),
    } as any;

    const res = await this.service.update(payload);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Course saved successfully',
      type: ToastMessageType.SUCCESS,
    });
  }

  async saveCover() {
    const file = this.uploadedFile;
    if (!file) return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    const folder = (
      this.service.courseService.organization?.name ?? '_orgo_logo_'
    )
      .replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    this.isLoading = true;
    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.isLoading = false;
      this.error = error;
      return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    }

    if (!data) {
      this.isLoading = false;
      return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    }
    this.isLoading = false;
    return data.url ?? this.data.cover ?? 'assets/images/new/card-3.jpeg';
  }
}
