import { HttpClient } from '@angular/common/http';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { firstValueFrom } from 'rxjs';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { NgFor, NgIf } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';

export type FileType = 'image' | 'video' | 'audio' | 'doc';

@Component({
  selector: 'app-file-loader',
  imports: [NgxDropzoneModule, MatButtonModule],
  templateUrl: './file-loader.component.html',
  styles: [
    `
      :host ngx-dropzone {
        border-radius: 1.5rem;
      }
    `,
  ],
})
export class FileLoaderComponent implements OnChanges {
  private _fileTypeConfig = {
    image: ['image/*'],
    video: ['video/*'],
    audio: ['audio/*'],
    doc: [
      'application/pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.csv',
      '.ppt',
      '.txt',
      '.pptx',
      '.rtf',
    ],
  };
  files: File[] = [];

  @Input() type: FileType;
  @Input() url: string | null;
  @Input() reset = false;
  @Output() sendFile = new EventEmitter<File[]>();
  @Output() urlResource = new EventEmitter<SafeResourceUrl>();

  // get previewImage(): boolean {
  //   return (!!this.url && this.type === 'image') || !!this.fileUrl;
  // }

  get fileUrl(): SafeResourceUrl | null {
    switch (this.type) {
      case 'image':
      case 'video':
      case 'audio':
      case 'doc':
        return this.files[0]
          ? this.sanitizer.bypassSecurityTrustResourceUrl(
              URL.createObjectURL(this.files[0])
            )
          : !!this.url
          ? this.sanitizer.bypassSecurityTrustUrl(this.url)
          : null;

      default:
        return null;
    }
  }

  get extension() {
    return this.files[0]?.type;
  }

  MAX_SIZE = 200000000;
  fileType: string = this._fileTypeConfig['image'].join(',');
  error?: string;

  constructor(private sanitizer: DomSanitizer, private http: HttpClient) {}

  async ngOnChanges(changes: SimpleChanges): Promise<void> {
    if (this.type) {
      this.fileType = this._fileTypeConfig[this.type].join(',');
    }

    if (this.url) {
      try {
        const file = await this.getFileFromUrl(this.url);
        if (file) {
          this.files.push(file);
        }
      } catch (error) {
        void 0;
      }
    }
  }

  async getFileFromUrl(url: string): Promise<File | null> {
    try {
      if (!/^(ftp|http|https):\/\/[^ "]+$/.test(url)) {
        return null;
      }
      const fileName = url.split('/').pop() ?? 'fileName';
      const data = await firstValueFrom(
        this.http.get(url, { responseType: 'blob' })
      );
      return new File([data], fileName);
    } catch (error) {
      void 0;
    }
    return null;
  }

  onSelect(event: any) {
    this.files.push(...event.addedFiles);

    if (this.files.some((x) => !this.validateFile(x))) {
      this.files = [];
      return;
    }

    this.sendFile.emit(this.files);

    if (this.reset) {
      this.files = [];
    }
  }

  onRemove(event: any) {
    if (event) {
      this.files.splice(this.files.indexOf(event), 1);
      this.sendFile.emit(this.files);
      if (this.reset) {
        this.files = [];
      }
      return;
    }
    if (this.files.length >= 0) {
      this.files = [];
      this.sendFile.emit([]);
      this.url = null;
      return;
    }
  }

  validateFile(file: File) {
    const fileType = file.type.split('/')[0];
    let isValid = true;

    const validVideoType = (type: string) => {
      const extension = type.replace('video/', '').toLowerCase();
      return ['mp4', 'webm', 'avi', 'mkv', 'mpeg'].includes(extension);
    };

    // Validate file type
    if (
      this.type === 'image' &&
      !this._fileTypeConfig.image.map((x) => x.split('/')[0]).includes(fileType)
    ) {
      this.error =
        'Invalid image format. Please upload JPEG, PNG, GIF, or WebP.';
      isValid = false;
    } else if (this.type === 'video' && !validVideoType(file.type)) {
      this.error =
        'Invalid video format. Please upload MP4, WebM, avi, mkv, or mpeg.';
      isValid = false;
    } else if (
      this.type === 'doc' &&
      !this._fileTypeConfig.doc.map((x) => x.split('/')[0]).includes(fileType)
    ) {
      this.error =
        'Invalid document format. Please upload PDF or Word documents.';
      isValid = false;
    }

    // Validate file size
    if (isValid) {
      if (this.type === 'image' && file.size > 1024 * 1024 * 2) {
        // 1MB
        this.error = 'Image size must be less than 2MB.';
        isValid = false;
      } else if (
        ['video', 'doc'].includes(this.type) &&
        file.size > 25 * 1024 * 1024
      ) {
        // 20MB
        this.error = 'File size must be less than 20MB.';
        isValid = false;
      }
    }

    if (isValid) {
      this.error = '';
    }
    return isValid;
  }
}
