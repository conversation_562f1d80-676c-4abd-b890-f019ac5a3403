

<div class="flex justify-between">
<h2 class="font-semibold text-secondary text-2xl mb-4">Reports</h2>
      <div class="form-input w-1/3">
        <select id="purpose" placeholder="" (change)="onSelectChange($event)">
          <option value="none" [selected]="selectedType === 'none'">
            Choose report type
          </option>
          <option value="course" [selected]="selectedType === 'course'">
            By Course
          </option>
          <option value="kc" [selected]="selectedType === 'kc'">
            By Knowledge Check
          </option>
          <option value="user" [selected]="selectedType === 'user'">
            By User Cumulative
          </option>
        </select>
      </div>
</div>

<div class="w-full pt-5">
  <router-outlet></router-outlet>
</div>


<div class="w-full flex flex-col items-center bg-white p-10" *ngIf="selectedType === 'none'">
  <img
    src="../../../../assets/images/svg/report_welcome.svg"
    width="225px"
  />
  <p class="text-10xs text-secondary mt-3 mb-3">
    Please select a report type to start!
  </p>
</div>
