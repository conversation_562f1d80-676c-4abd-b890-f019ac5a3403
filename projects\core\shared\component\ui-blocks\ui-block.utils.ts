import { UIBlockCardComponent } from './card.component';
import { UIBlockCheckComponent } from './quiz/check.component';
import { UIBlockFlashcardComponent } from './flashcard/flashcard.component';
import { UIBlockGalleryComponent } from './gallery.component';
import { UIBlockImageComponent } from './image.component';
import { UIBlockListComponent } from './list.component';
import { UIBlockQuoteComponent } from './quote.component';
import { UIBlockResourceComponent } from './resource.component';
import { UIBlockTextComponent } from './text.component';
import { UIBlockDividerComponent } from './divider.component';
import { UIBlockQuizComponent } from './quiz/quiz.component';
import { UIBlockFlashcardsComponent } from './flashcard/flashcards.component';

export type UIBlockData = {
  widgetType: WidgetType;
  content: { [key: string]: any };
  userAnswers?: { [key: string]: any };
};

export enum WidgetType {
  // AITEXT = 'AITEXT',
  // AIIMAGE = 'AIIMAGE',
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  PROCESS = 'PROCESS',
  FLASHCARD = 'FLASHCARD',
  SORTING = 'SORTING',
  QUIZ = 'QUIZ',
  // STATEMENT = 'STATEMENT',
  CARD = 'CARD',
  QUOTE = 'QUOTE',
  LIST = 'LIST',
  GALLERY = 'GALLERY',
  RESOURCE = 'RESOURCE',
  INTERACTIVE = 'INTERACTIVE',
  CHART = 'CHART',
  DIVIDER = 'DIVIDER',
}

export const LessonWidgetTypes = [
  // {
  //   id: WidgetType.AITEXT,
  //   label: 'AI Text Generator',
  //   shortName: 'AI Text',
  //   icon: 'fa-solid fa-wand-magic-sparkles',
  //   component: UIBlockTextComponent,
  // },
  // {
  //   id: WidgetType.AIIMAGE,
  //   label: 'AI Image Generator',
  //   shortName: 'AI Image',
  //   icon: 'fas fa-image',
  //   component: UIBlockImageComponent,
  // },
  {
    id: WidgetType.TEXT,
    label: 'Text Content',
    shortName: 'Text',
    icon: 'fas fa-font',
    component: UIBlockTextComponent,
  },
  {
    id: WidgetType.IMAGE,
    label: 'Image Content',
    shortName: 'Image',
    icon: 'fas fa-image',
    component: UIBlockImageComponent,
  },
  {
    id: WidgetType.VIDEO,
    label: 'Video Content',
    shortName: 'Video',
    icon: 'fas fa-video',
    component: UIBlockResourceComponent,
  },
  // {
  //   id: WidgetType.PROCESS,
  //   label: 'Process Flow',
  //   shortName: 'Process',
  //   icon: 'fa-solid fa-layer-group',
  //   component: UIBlockTextComponent,
  // },
  {
    id: WidgetType.FLASHCARD,
    label: 'Card View',
    shortName: 'Flashcards',
    icon: 'fa-regular fa-note-sticky',
    component: UIBlockFlashcardsComponent,
    // component: UIBlockFlashcardComponent,
  },
  // {
  //   id: WidgetType.SORTING,
  //   label: 'Sorting Exercise',
  //   shortName: 'Sorting',
  //   icon: 'fa-solid fa-arrow-up-short-wide',
  //   component: UIBlockTextComponent,
  // },
  {
    id: WidgetType.QUIZ,
    label: 'Quiz Section',
    shortName: 'Quiz',
    icon: 'fa-solid fa-file-circle-question',
    component: UIBlockQuizComponent,
  },
  {
    id: WidgetType.QUOTE,
    label: 'Quote & Statement',
    shortName: 'Quote',
    icon: 'fas fa-quote-left',
    component: UIBlockQuoteComponent,
  },
  {
    id: WidgetType.LIST,
    label: 'List Content',
    shortName: 'List',
    icon: 'fas fa-list-ul',
    component: UIBlockListComponent,
  },
  {
    id: WidgetType.GALLERY,
    label: 'Image Gallery',
    shortName: 'Gallery',
    icon: 'fa-regular fa-images',
    component: UIBlockGalleryComponent,
  },
  {
    id: WidgetType.RESOURCE,
    label: 'Resource Link',
    shortName: 'Resource',
    icon: 'fas fa-link',
    component: UIBlockResourceComponent,
  },
  // {
  //   id: WidgetType.INTERACTIVE,
  //   label: 'Interactive Content',
  //   shortName: 'Interactive',
  //   icon: 'fas fa-hand-pointer',
  //   component: UIBlockTextComponent,
  // },
  // {
  //   id: WidgetType.CHART,
  //   label: 'Chart Display',
  //   shortName: 'Chart',
  //   icon: 'fas fa-chart-bar',
  //   component: UIBlockTextComponent,
  // },
  {
    id: WidgetType.DIVIDER,
    label: 'Section Divider',
    shortName: 'Divider',
    icon: 'fas fa-grip-lines',
    component: UIBlockDividerComponent,
  },
];
