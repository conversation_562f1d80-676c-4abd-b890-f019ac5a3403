import { Routes } from '@angular/router';
import { SettingsComponent } from './settings.component';

export const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./org/org.component').then((c) => c.SettingsOrgComponent),
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('./profile/profile.component').then((c) => c.ProfileComponent),
      },
      {
        path: 'application',
        loadComponent: () =>
          import('./application/application.component').then(
            (c) => c.SettingApplicationComponent
          ),
      },
      {
        path: 'notification',
        loadComponent: () =>
          import('./application/application.component').then(
            (c) => c.SettingApplicationComponent
          ),
      },
      {
        path: 'media',
        loadComponent: () =>
          import('../../core/components/media/media.component').then(
            (c) => c.MediaCenterComponent
          ),
      },
    ],
  },
];
