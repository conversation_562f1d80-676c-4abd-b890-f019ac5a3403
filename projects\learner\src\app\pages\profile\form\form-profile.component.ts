import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, TemplateRef, viewChild } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import {
  AccountStatus,
  AuthService,
  GlobalStateService,
  GroupItem,
  ITeamGroupItem,
  markControlsDirty,
  MediaBucket,
  tryPromise,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { PasswordData, PasswordFormComponent } from '@lms/shared';
import { MediaCenterService } from '@lms/core';

@Component({
  selector: 'app-form-profile',
  templateUrl: './form-profile.component.html',
  imports: [
    MatChipsModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    NgTemplateOutlet,
    PasswordFormComponent,
    MatProgressBarModule,
  ],
  styles: [
    `
      :host {
        width: 100%;
      }
    `,
  ],
})
export class ProfileFormComponent {
  readonly state = inject(GlobalStateService);
  private authService = inject(AuthService);
  readonly mediaService = inject(MediaCenterService);
  service = inject(UsersCoreService);
  readonly dialog = inject(MatDialog);

  passwordRef = viewChild<TemplateRef<any>>('password');
  profileEditRef = viewChild<TemplateRef<any>>('profileEdit');

  get user() {
    return this.state.user();
  }

  tab = 1;

  imageUrl = this.user.avatar ?? 'assets/images/user-profile.jpg';

  modalRef?: MatDialogRef<any>;

  userTeams: ITeamGroupItem[] = [];
  userGroups: ITeamGroupItem[] = [];
  groupOptions: GroupItem[] = [];

  isLoading = false;
  error?: string;
  userItem: UserItem;

  userForm = new FormGroup({
    id: new FormControl(''),
    email: new FormControl('', Validators.required),
    phone: new FormControl('', Validators.required),
    title: new FormControl('', Validators.required),
    firstname: new FormControl('', Validators.required),
    lastname: new FormControl('', Validators.required),
  });

  // isLoading = false;
  // error?: string;
  // userTeams: ITeamGroupItem[] = [];
  // userGroups: ITeamGroupItem[] = [];

  // groupOptions: GroupItem[] = [];

  async ngOnInit(): Promise<void> {
    // this.userItem = (
    //   await this.service.getUsers({
    //     payload: {
    //       role: AccountStatus.LEARNER,
    //       query: this.service.user.email,
    //     },
    //     paging: {
    //       page: 1,
    //       size: 10,
    //     },
    //   })
    // ).data[0];
    if (this.user) {
      // this.userTeams = this.userItem.teams || [];
      // this.userGroups = this.userItem.groups || [];
      // this.state.user.update((u) => ({
      //   ...u,
      //   teams: this.userTeams,
      //   groups: this.userGroups,
      // }));
      this.userForm.patchValue({
        id: this.user.id,
        email: this.user.email,
        phone: this.user.phone,
        title: this.user.title,
        firstname: this.user.firstname,
        lastname: this.user.lastname,
      });
    }
  }

  editUser() {
    this.modalRef = this.dialog.open(this.profileEditRef()!, {
      minWidth: '800px',
      data: {
        type: 'EDIT',
        item: this.user,
      },
    });
  }

  changePassword() {
    this.modalRef = this.dialog.open(this.passwordRef()!, {
      disableClose: true,
      minWidth: '500px',
    });
  }

  async onSubmitPassword(data: PasswordData) {
    if (data.type === 'CANCEL') {
      this.modalRef?.close();
    }
    if (data.type === 'SUBMIT' && data.data?.newPassword) {
      this.executePassword(data.data?.newPassword);
    }
  }

  async executePassword(password: string) {
    this.authService.isLoading = true;
    const res = await this.authService.resetPassword(password);
    this.authService.isLoading = false;
    if (res?.error) {
      return;
    }

    await this.authService.signOutAndRedirect();
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;
    const file = input.files[0];
    if (file.type !== 'image/*') {
      this.error = 'Please upload a image file.';
      return;
    }
    this.saveLogo(file);
  }

  async saveLogo(file: File) {
    const folder = (this.user?.username ?? '_orgo_logo_')
      .replace(/\s/g, '-')
      .toLowerCase()
      .trim();
    this.isLoading = true;
    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.isLoading = false;
      this.error = error;
      return;
    }

    if (!data) {
      this.isLoading = false;
      return;
    }

    await tryPromise(
      this.service.update([
        {
          ...this.user,
          avatar: data.fullPath,
        },
      ])
    );
    this.isLoading = false;
  }

  async onSubmitEdit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.userForm.invalid) {
      markControlsDirty(this.userForm);
      return;
    }
    const formData = this.userForm.getRawValue();
    this.isLoading = true;
    const payload = {
      id: formData.id,
      email: formData.email,
      phone: formData.phone?.replace(/\D/g, ''),
      title: formData.title,
      firstname: formData.firstname,
      lastname: formData.lastname,
    } as any;
    const [error, res] = await tryPromise(this.service.update([payload]));
    this.isLoading = false;

    if (error || res?.error) {
      this.error = res?.error ?? error?.message;
      return;
    }
    this.state.user.update((u) => ({
      ...u,
      ...payload,
    }));
    this.modalRef?.close();
  }
}
