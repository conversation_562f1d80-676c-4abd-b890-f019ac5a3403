{"name": "lms", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve shell", "build": "ng build shell --configuration production", "ci": "npm run build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/cdk": "^19.2.11", "@angular/common": "^19.2.8", "@angular/compiler": "^19.2.8", "@angular/core": "^19.2.8", "@angular/elements": "^19.2.8", "@angular/forms": "^19.2.8", "@angular/material": "^19.2.11", "@angular/platform-browser": "^19.2.8", "@angular/platform-browser-dynamic": "^19.2.8", "@angular/platform-server": "^19.2.8", "@angular/router": "^19.2.8", "@angular/ssr": "^19.2.9", "@fortawesome/fontawesome-free": "^6.6.0", "@stripe/stripe-js": "^6.1.0", "@supabase/supabase-js": "^2.45.4", "@swimlane/ngx-charts": "^22.0.0", "angular-csv-ext": "^1.0.5", "dayjs": "^1.11.13", "express": "^4.18.2", "graphql": "^16.9.0", "graphql-request": "^7.1.0", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "mammoth": "^1.8.0", "ngx-dropzone": "^3.1.0", "ngx-mask": "^19.0.6", "ngx-quill": "27.0.2", "papaparse": "^5.5.2", "quill": "^2.0.3", "rxjs": "~7.8.0", "scormcloud-client": "^0.2.6", "tailwindcss-animated": "^2.0.0", "tslib": "^2.3.0", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.8", "@types/aos": "^3.0.7", "@types/express": "^4.17.17", "@types/node": "^18.18.0", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "autoprefixer": "^10.4.20", "ng-packagr": "^19.2.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "~5.5.2"}}