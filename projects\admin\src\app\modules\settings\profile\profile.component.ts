import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, TemplateRef, viewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import {
  AuthService,
  GlobalStateService,
  GroupItem,
  ITeamGroupItem,
  MediaBucket,
  ToastMessageType,
  tryPromise,
  UsersCoreService,
} from '@lms/core';
import { UserFormComponent } from '../../user/users/form/user-form.component';
import { PasswordData, PasswordFormComponent } from '@lms/shared';
import { MediaCenterService } from '@lms/core';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  imports: [
    MatChipsModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    NgTemplateOutlet,
    PasswordFormComponent,
    MatProgressBarModule,
  ],
  styles: [
    `
      :host {
        width: 100%;
      }
    `,
  ],
})
export class ProfileComponent {
  readonly state = inject(GlobalStateService);
  private authService = inject(AuthService);
  readonly mediaService = inject(MediaCenterService);
  service = inject(UsersCoreService);
  readonly dialog = inject(MatDialog);

  passwordRef = viewChild<TemplateRef<any>>('password');

  get user() {
    return this.state.user();
  }

  tab = 1;

  imageUrl = this.user.avatar ?? 'assets/images/user-profile.jpg';

  modalRef?: MatDialogRef<any>;

  userTeams: ITeamGroupItem[] = [
    ...(this.user.teams || []),
    ...(this.user.groups || []),
  ].filter((v, i, _) => _.findIndex((x) => x.teamId === v.teamId) === i);
  userGroups: ITeamGroupItem[] = this.user.groups || [];

  courseInApp = new FormControl<boolean | null>(null);
  courseEmail = new FormControl<boolean | null>(null);
  courseSMS = new FormControl<boolean | null>(null);

  isLoading = false;
  error?: string;

  onSubmit() {}

  editUser() {
    this.dialog.open(UserFormComponent, {
      minWidth: '800px',
      data: {
        type: 'EDIT',
        item: this.user,
      },
    });
  }

  changePassword() {
    this.modalRef = this.dialog.open(this.passwordRef()!, {
      disableClose: true,
      minWidth: '500px',
    });
  }

  async onSubmitPassword(data: PasswordData) {
    if (data.type === 'CANCEL') {
      this.modalRef?.close();
    }
    if (data.type === 'SUBMIT' && data.data?.newPassword) {
      this.executePassword(data.data?.newPassword);
    }
  }

  async executePassword(password: string) {
    this.authService.isLoading = true;
    const res = await this.authService.resetPassword(password);
    this.authService.isLoading = false;
    if (res?.error) {
      return;
    }

    await this.authService.signOutAndRedirect();
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;
    const file = input.files[0];
    if (!file.type.includes('image/')) {
      this.error = 'Please upload a image file.';
      return;
    }
    this.saveLogo(file);
  }

  async saveLogo(file: File) {
    const folder = (this.user?.username ?? '_orgo_logo_')
      .replace(/\s/g, '-')
      .toLowerCase()
      .trim();
    this.isLoading = true;
    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.isLoading = false;
      this.error = error;
      return;
    }

    if (!data) {
      this.isLoading = false;
      return;
    }

    this.imageUrl = data.url ?? 'assets/images/logo-placeholder.jpg';
    await tryPromise(
      this.service.update([
        {
          ...this.user,
          avatar: data.url,
        },
      ])
    );
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Profile saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.isLoading = false;
  }
}
