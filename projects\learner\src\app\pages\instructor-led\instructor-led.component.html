<div class="flex flex-col">
  <app-resource-loader [source]="source" />
  @if (source.value(); as item) {
  <div class="grid grid-cols-2 items-center gap-5 p-12 bg-lot-dark text-white">
    <div class="flex flex-col gap-6">
      <div class="flex gap-12">
        <div class="flex flex-col">
          <h1 class="text-3xl font-bold">{{ item.instructor.name }}</h1>
          <span class="text-xs">Instructor Led Course</span>
        </div>
        <a
          [routerLink]="['/myTraining/course-library']"
          class="bg-transparent hover:bg-lot-blue text-white font-semibold px-3 py-1 rounded-md border-2 border-white text-center h-fit"
        >
          <span class="material-symbols-outlined">reply</span>
        </a>
      </div>
      <div class="flex gap-5 max-w-3xl text-wrapper">
        <app-ui-html-wrapper 
          [content]="(item.instructor.short) + '...'"
        />
      </div>
    </div>

    <div class="w-full flex justify-end gap-5">
      @if (progress(); as prePro) {
      <div class="relative size-32">
        <div
          class="absolute inset-0 flex flex-col items-center justify-center text-white text-center"
        >
          <span class="text-2xl font-bold leading-none">
            {{ prePro.complete }}/{{ prePro.total }}
          </span>
          <span class="leading-none text-xs max-w-20"
            >Prerequisites complete</span
          >
        </div>
        <svg class="w-full h-full">
          <circle
            cx="50%"
            cy="50%"
            r="40%"
            stroke="#e2e8f0"
            stroke-width="10"
            fill="transparent"
          />
          <circle
            cx="50%"
            cy="50%"
            r="40%"
            stroke="#3b82f6"
            stroke-width="10"
            fill="transparent"
            [attr.stroke-dasharray]="dashArray"
            [attr.stroke-dashoffset]="prePro.dashOffset"
            class="transform rotate-[-90deg] origin-center"
          >
            <animateTransform
              attributeName="stroke-dashoffset"
              from="251.33"
              to="0"
              dur="1s"
              fill="freeze"
            />
          </circle>
        </svg>
      </div>
      }
      <div class="flex flex-col gap-10 w-72">
        <div class="flex flex-col">
          <app-progress [status]="item.tracking?.status!" [progress]="item.tracking?.progress || 0" />
          <span class="text-white">{{
            item.tracking?.progress
              ? item.tracking?.progress + "% complete - Keep Going"
              : "0% complete - Time to Start!"
          }}</span>
        </div>
        @if (!item.tracking?.progress) {
        <button class="button-primary w-fit py-2">Start Learning Path</button>
        }
      </div>
    </div>
  </div>

  <div
    class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 mt-4"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="tab = item.id"
          class="inline-block p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === item.id"
          [class.font-bold]="tab === item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>

  <div class="mb-20 w-full">
    <div class="flex mb-20 mt-8 w-full">
      @if (tab === 1) {
      <div class="flex flex-col gap-5 w-full">
        <h3 class="text-lot-blue text-lg">Prerequisites</h3>
        <p>
          You must complete the following prerequisites before you can start the
          course and/or enrol in a session.
        </p>
        <div class="flex gap-9 items-center">
          @for (course of item.instructor.prerequisites; track $index; let i=$index) {
          <div class="flex flex-col gap-5 group w-[300px]">
            <a
              href="javascript:void(0);"
              (click)="goToCourse(course.id)"
              class="relative w-full h-40"
            >
              <img
                src="{{ course.cover }}"
                alt=""
                class="w-full h-full object-cover rounded-3xl"
              />
              <div
                class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
              >
                <div class="hidden group-hover:flex text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="size-12"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </a>
            <div class="flex-1">
              <div class="flex flex-col gap-3 h-full">
                <h1 class="font-semibold text-xl">
                  {{ course.name }}
                </h1>
                <div class="flex-grow text-base mb-2 max-w-xs">
                  <p class="truncate">{{ course.short.slice(0, 90) }}</p>
                </div>
                <div class="flex flex-col gap-3">
                  <!-- <app-progress
                    [status]="course.status"
                    [progress]="course.progress"
                  />

                  <div class="flex gap-1 items-center text-base text-lot-blue">
                    <span class="material-symbols-outlined">book_5</span>
                    @if (course.status === 'COMPLETED') {
                    <span class="text-lot-ai">Complete</span>
                    } @else {
                    <button class="button-ghost-outline w-fit px-2">
                      {{ course.progress ? "Continue" : "Start Course" }}
                    </button>
                    }
                  </div> -->
                </div>
              </div>
            </div>
          </div>
          }
        </div>

        <div class="flex flex-col gap-3">
          <h3 class="text-lot-blue text-lg">
            My Session{{ item.instructor.sessions.length > 1 ? "s" : "" }}
          </h3>
          <div class="flex flex-col gap-3">
            @for (session of item.tracking?.sessions || []; track $index) { } @empty { }
          </div>

          <p>
            Choose a live session to enrol in this course. Completion of a
            session is mandatory for course completion.
          </p>
          @if(item.instructor.sessions.length) {
          <ng-container
            *ngTemplateOutlet="
              sessionTmp;
              context: {
                items: item.tracking?.sessions || [],
                author: item.instructor.creator.name,
                isEnrolled: true
              }
            "
          />
          } @if(!item.tracking?.sessions?.length) {
          <div
            class="flex flex-col items-center justify-center gap-3 bg-lot-light-blue/20 w-full h-40"
          >
            <p>Not enrolled in any sessions yet.</p>
            <button class="button-primary" (click)="tab = 4">
              Choose Sessions
            </button>
          </div>
          }
        </div>
      </div>
      } @if (tab === 2) {
      <div class="flex flex-col gap-5">
        <div class="flex gap-5">
          <div
            class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
          >
            <svg
              class="absolute size-14 text-gray-400 pl-2"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                clip-rule="evenodd"
              ></path>
            </svg>
          </div>
          <div>
            <h3 class="font-semibold text-lot-blue text-xl">
              {{ item.instructor.creator.name }}
            </h3>
            <p class="text-xs text-lot-dark">{{ item.instructor.creator.email }}</p>
          </div>
        </div>
        <div class="max-w-[440px]">
          <p>
            {{ item.instructor.creator.bio }}
          </p>
        </div>
      </div>
      } @if (tab === 3) {
      <div class="flex flex-col gap-1 w-[389px] md:w-[440px]">
        @for (item of item.instructor.resources; track $index) {
        <div class="flex items-center justify-between p-4 border-b rounded-md">
          <div class="flex items-center">
            <span class="material-symbols-outlined mr-4 border p-3 rounded-lg"
              >description</span
            >
            <div>
              <p class="font-semibold">{{ item.name }}</p>
              <p class="text-sm text-gray-500">PDF</p>
            </div>
          </div>
          <button class="button-primary">View</button>
        </div>
        }
      </div>
      } @if (tab === 4) {
      <div class="flex flex-col gap-3 w-full">
        <h3 class="text-lot-blue text-lg">
          Upcoming Session{{ item.instructor.sessions.length > 1 ? "s" : "" }}
        </h3>
        @if (progress().remain) {
        <div
          class="flex items-center gap-3 py-3 px-6 bg-lot-danger/10 rounded-md w-fit"
        >
          <span class="material-symbols-outlined text-3xl text-lot-danger">
            warning
          </span>
          <p>
            You cannot sign up for a session before you complete the
            prerequisites.
          </p>
        </div>
        }
        <ng-container
          *ngTemplateOutlet="
            sessionTmp;
            context: {
              items: item.instructor.sessions || [],
              author: item.instructor.creator.name,
              isEnrolled: false
            }
          "
        />
      </div>
      }
    </div>
  </div>

  }@else {
  <p class="text-center py-4">No active loads currently.</p>
  }
</div>

<ng-template
  #sessionTmp
  let-items="items"
  let-author="author"
  let-isEnrolled="isEnrolled"
>
  <div class="flex flex-col gap-10">
    @for (session of items; track $index) {
    <div
      class="flex justify-between items-start gap-3 bg-lot-light-blue/20 w-full pt-8 pb-14 px-8 text-lot-dark rounded-md"
    >
      <div class="flex flex-col gap-2">
        <h2 class="font-bold text-lot-dark text-3xl">{{ session.name }}</h2>
        <div class="flex gap-3">
          <span>Instructor:</span>
          <span>{{ author }}</span>
        </div>
        <div class="flex gap-3">
          <span>Location:</span>
          <span>{{ session.location }}</span>
        </div>
        @if (isEnrolled) {
        <div
          class="bg-lot-ai text-white font-semibold py-1 px-4 rounded-md border-0 text-center w-fit my-5"
        >
          Enrolled
        </div>
        }@else {
        <button class="button-primary w-fit my-5">Enroll for Session</button>
        }

        <span class="text-lot-dark-gray">More Details</span>

        <div class="grid grid-cols-2 gap-3">
          <span class="w-fit">Class Size:</span>
          <span>{{ session.classSize }}</span>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <span class="w-fit">Seats Remaining:</span>
          <span>{{ session.classSize - session.booked }}</span>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <span class="w-fit">Starts at:</span>
          <span>{{ session.startDate | date : "MMMM d, y hh::mm a z" }}</span>
        </div>
        <div class="grid grid-cols-2 gap-3">
          <span class="w-fit">Ends at:</span>
          <span>{{ session.endDate | date : "MMMM d, y hh:mm a z" }}</span>
        </div>
      </div>
      <div class="flex items-center gap-3">
        <span class="material-symbols-outlined text-6xl"> event </span>
        <div class="flex flex-col justify-end text-right">
          <span class="text-lot-blue font-semibold">{{
            session.endDate | date : "longDate"
          }}</span>
          <span>
            {{ session.startDate | date : "hh.mm a" }} -
            {{ session.endDate | date : "hh.mm a" }}
          </span>
        </div>
      </div>
    </div>

    }
  </div>
</ng-template>
