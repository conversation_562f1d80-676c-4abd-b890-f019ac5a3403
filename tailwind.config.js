/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./projects/**/*.{html,ts}"],
  theme: {
    extend: {
      colors: {
        primary: {
          100: "#006dff",
          200: "#202020",
          300: "#151617",
          400: "#7D8793",
          700: "#DCE0E3",
        },
        dark: {
          100: "#F3F4F5",
        },
        secondary: "#16395b",
        accent: "#d0415a",
        tertiary: "#e29270",
        "tertiary-light": "#faece5",
        lot: {
          dark: "#002747",
          blue: "#2E7DDB",
          gray: "#D9D9D9",
          "light-gray": "#F9F9F9",
          white: "#ECF6FF",
          ai: "#18CC78",
          "ai-dark": "#0EA1AB",
          "dark-gray": "#686A6D",
          "light-blue": "#9ECAFF",
          gold: "#FFAC33",
          warning: "#DD6F01",
          danger: "#F95757",
          disabled: "#BAC2CC",
        },
      },
      fontFamily: {
        // display: ['"Inter-Regular"', "Hanken Grotesk"],
        // body: ['"Inter-Regular"', "Hanken Grotesk"],
        display: ["<PERSON>en Grotesk"],
        body: ["Hanken Grotesk"],
      },
      fontSize: {
        "40xs": "2.5rem", //40px
        "10xs": "0.875rem", //14px
        "9xs": "0.625rem", // 10px
      },
      keyframes: {
        shimmer: {
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        shimmer: "shimmer 1.5s infinite linear",
      },
    },
  },
  plugins: [require("tailwindcss-animated")],
};
