import { inject } from '@angular/core';
import { CanActivateFn, CanActivateChildFn, Router } from '@angular/router';
import {
  AccountStatus,
  AuthService,
  GlobalStateService,
  NotificationService,
  OrgSubscription,
} from '@lms/core';

export const canActivateOneAuth:
  | CanActivateFn
  | CanActivateChildFn = async () => {
  const router = inject(Router);
  const state = inject(GlobalStateService);
  const auth = inject(AuthService);

  if (!state.user().id) {
    await auth.refreshSession();
  }
  if (!state.user()?.id) {
    router.navigate(['/sign-in']);
    return false;
  }

  if ([AccountStatus.SUPER].includes(state.user().role)) {
    return true;
  }

  router.navigate(['/sign-in']);
  return false;
};

export const canActivateAdminAuth:
  | CanActivateFn
  | CanActivateChildFn = async () => {
  const router = inject(Router);
  const notif = inject(NotificationService);
  const state = inject(GlobalStateService);
  const auth = inject(AuthService);
  let subscription: OrgSubscription | undefined = undefined;

  if (!state.user().id) {
    await auth.refreshSession();
  }
  subscription = state.mySubscription();
  checkExpiredLink(notif);

  if (!reRoutingHandler(router)) {
    return false;
  }

  if (!state.user()?.id) {
    router.navigate(['/sign-in']);
    return false;
  }

  if (
    !subscription ||
    new Date().getTime() > new Date(subscription.expired_at).getTime() ||
    subscription?.status !== 'ACTIVE'
  ) {
    router.navigate(['/subscription']);
    return false;
  }

  if (state.user().role === AccountStatus.LEARNER) {
    router.navigate(['/myTraining']);
    return false;
  }

  if ([AccountStatus.NONE, AccountStatus.SUPER].includes(state.user().role)) {
    router.navigate(['/sign-in']);
    return false;
  }

  return true;
};

export const canActivateAuth: CanActivateFn | CanActivateChildFn = async () => {
  const router = inject(Router);
  const notif = inject(NotificationService);
  const state = inject(GlobalStateService);
  const auth = inject(AuthService);
  let subscription: OrgSubscription | undefined = undefined;

  if (!state.user().id) {
    await auth.refreshSession();
  }
  subscription = state.mySubscription();
  checkExpiredLink(notif);

  if (!reRoutingHandler(router)) {
    return false;
  }

  if (!state.user()?.id) {
    router.navigate(['/sign-in']);
    return false;
  }

  if (
    !subscription ||
    new Date().getTime() > new Date(subscription.expired_at).getTime() ||
    subscription?.status !== 'ACTIVE'
  ) {
    router.navigate(['/subscription']);
    return false;
  }
  return true;
};

export const canActivateSubscription: CanActivateFn = async () => {
  const router = inject(Router);
  const state = inject(GlobalStateService);
  const auth = inject(AuthService);

  if (!state.user().id) {
    await auth.refreshSession();
  }

  if (!state.user()?.id) {
    router.navigate(['/sign-in']);
    return false;
  }

  if (state.user().role === AccountStatus.LEARNER) {
    window.location.assign(state.envConfig.env.learnerAppUrl);
    return false;
  }

  return true;
};

export const canActivateAuthForLearner:
  | CanActivateFn
  | CanActivateChildFn = async () => {
  const router = inject(Router);
  const notif = inject(NotificationService);
  const state = inject(GlobalStateService);
  const auth = inject(AuthService);
  let subscription: OrgSubscription | undefined = undefined;

  if (!state.user().id) {
    await auth.refreshSession();
  }
  subscription = state.mySubscription();
  checkExpiredLink(notif);

  if (!reRoutingHandler(router)) {
    return false;
  }

  if (!state.user()?.id) {
    router.navigate(['/sign-in']);
    return false;
  }

  if (
    !subscription ||
    new Date().getTime() > new Date(subscription.expired_at).getTime() ||
    subscription?.status !== 'ACTIVE'
  ) {
    router.navigate(['/subscription']);
    return false;
  }

  return true;
};

export const canActivateSuper: CanActivateFn = async () => {
  const state = inject(GlobalStateService);
  if (!state.user()?.id) {
    return false;
  }
  return state.user().role === AccountStatus.SUPER;
};

const checkExpiredLink = (notif: NotificationService) => {
  const url = window.location.hash;
  if (url.indexOf('#error') >= 0 && url.indexOf('error_description') >= 0) {
    const error = url
      .split('&')
      .find((x) => x.indexOf('error_description') >= 0)
      ?.replace('error_description=', '');
    notif.error(`Unauthorized request: ${error?.replaceAll('+', ' ')}`);
  }
};

const reRoutingHandler = (router: Router) => {
  const s = window.location.hash;
  if (
    s.indexOf('#access_token') >= 0 &&
    (s.indexOf('type=recovery') >= 0 || s.indexOf('type=invite') >= 0)
  ) {
    // TODO update the store with the url data, to be use when the user submit the new password
    let a = s.split('access_token=');
    if (a.length < 1) {
      return;
    }
    a = a[1].split('&');
    if (a.length < 2) {
      return;
    }

    router.navigate(['/sign-in', a[0]]);
    return false;
  }
  const urlRedirect = window.location.search.split('confirmation_url=').pop();
  if (window.location.pathname.indexOf('confirmation') >= 0 && urlRedirect) {
    window.location.href = decodeURIComponent(urlRedirect);
    return false;
  }
  const recovery = sessionStorage.getItem('ui_recovery_token');
  if (recovery) {
    router.navigate(['/sign-in', recovery]);
    return false;
  }
  return true;
};
