<div class="flex flex-col gap-10 w-full min-h-full pb-20">
  <div class="flex gap-10 items-start w-full">
    <div class="flex-grow">
      <div class="flex flex-col gap-5">
        <div class="grid grid-cols-2 gap-10">
          <div class="flex flex-col gap-2">
            <span class="text-lot-dark-gray/70">Company Name</span>
            <span class="text-lot-dark">{{ organization.name }}</span>
          </div>
          <div class="form-input">
            <span class="text-lot-dark-gray/70">Company No Of Employees</span>
            <span class="text-lot-dark">{{
              organization.about!["nbEmployees"] || "-"
            }}</span>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-10">
          <div class="form-input">
            <span class="text-lot-dark-gray/70">Business Email Address</span>
            <span class="text-lot-dark">{{
              (organization.contact?.email ?? organization.email) || "-"
            }}</span>
          </div>
          <div class="form-input">
            <span class="text-lot-dark-gray/70">Business Phone Number</span>
            <span class="text-lot-dark">{{
              (organization.contact?.phone ?? organization.phone) || "-"
            }}</span>
          </div>
        </div>
        <div class="form-input">
          <span class="text-lot-dark-gray/70">Company Address</span>
          <span class="text-lot-dark">{{
            organization.about!["address"] || "-"
          }}</span>
        </div>
        <div class="form-input">
          <span class="text-lot-dark-gray/70">Company Website</span>
          <span class="text-lot-dark">{{
            organization.about!["website"] || "-"
          }}</span>
        </div>
        <div class="form-input">
          <span class="text-lot-dark-gray/70">Contact Info</span>
          <span class="text-lot-dark">{{
            organization.contact?.name + " / " + organization.contact?.title
          }}</span>
        </div>
      </div>
    </div>
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-blue underline italic"
      (click)="editOrg()"
    >
      <i class="fa-solid fa-pen-to-square"></i>
    </a>
  </div>

  <div class="flex gap-10 items-start border-t pt-10">
    <div class="flex flex-col gap-2">
      <span class="text-lot-dark-gray/70">Company Logo</span>
      <div class="w-full h-44 focus:outline-none relative">
        <img
          [src]="orgUrl"
          alt=""
          srcset=""
          class="w-full h-full object-cover rounded-xl border"
        />

        <div class="relative">
          @if (isLoading) {
          <mat-progress-bar mode="indeterminate" />
          } @if (error) {
          <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
          }
          @if (!isLoading) {
          <a
            href="javascript:void(0)"
            class="absolute bottom-0 -right-44 button-primary-outline items-center w-ft"
          >
            Upload Logo
            <i class="fa-solid fa-arrow-up-from-bracket"></i>
            <input
              type="file"
              accept="image/*"
              (change)="onFileChange($event)"
              class="absolute inset-0 opacity-0 cursor-pointer"
            />
          </a>
          }
        </div>
      </div>
    </div>
  </div>

  <div class="flex gap-10 items-start border-t pt-10 w-full">
    <div class="flex justify-between w-full">
      <div class="flex flex-col gap-2">
        <span class="text-lot-dark-gray/70">
          Single sign-on (SSO) authentication
        </span>
        <span class="text-lot-dark">
          Authenticate Users with your own identity provider
        </span>
      </div>
      <button class="button-primary-outline w-fit py-0">Configure SSO</button>
    </div>
  </div>
</div>
