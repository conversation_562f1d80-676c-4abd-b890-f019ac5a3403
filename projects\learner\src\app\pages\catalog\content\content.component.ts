import { Component, inject, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { LearningType, LearningService, TrainingService, UserTrackingItem, Course } from '@lms/core';
import { ResourceHeaderComponent, ProgressComponent } from '@lms/shared';

@Component({
  selector: 'lms-catalog-content',
  templateUrl: './content.component.html',
  imports: [ProgressComponent, ResourceHeaderComponent],
})
export class CatalogContentComponent implements OnInit {
  router = inject(Router);
  learningService = inject(LearningService);
  trainingService = inject(TrainingService);

  @Input() view = 'grid';

  dataSource = this.learningService.catalogSource;
  cover = 'assets/images/new/card-3.jpeg';

  ngOnInit(): void {
    this.learningService.catalogFilter.update((x) => ({ ...x, load: 1 }));
  }

  async goTo(item: UserTrackingItem) {
    if (item.type === LearningType.COURSE) {
      if (!item.id) {
        await this.trainingService.addTracking(item.item as any, item);
      }
      this.router.navigate(['/myTraining/training', item.item.id]);
    }
    if (item.type === LearningType.LEARNINGPATH) {
      if (!item.id) {
        await this.trainingService.addTrackingCombo(item.item as any, item);
      }
      this.router.navigate(['/myTraining/learning-path', item.item.id]);
    }
    if (item.type === LearningType.INSTRUCTORLED) {
      if (!item.id) {
        await this.trainingService.addTrackingCombo(item.item as any, item);
      }
      this.router.navigate(['/myTraining/instructor-led', item.item.id]);
    }
  }
}
