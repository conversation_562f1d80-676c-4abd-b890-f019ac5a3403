@if(view === 'SIGNUP' || view === 'PASSWORD'){
<section class="flex flex-col md:flex-row h-screen items-center">
  <div
    class="hidden lg:block w-full md:w-1/2 xl:w-2/4 h-screen bg-lot-light-gray"
  >
    <div class="flex flex-col gap-20 p-20 max-w-xl">
      <div class="h-10 w-56">
        <img src="assets/images/new/logo-light.png" alt="" srcset="" />
      </div>

      <div class="flex flex-col">
        <h1 class="text-lot-blue text-5xl font-bold">Sign-up for a</h1>
        <h1 class="text-lot-blue text-5xl font-bold">
          <span class="italic underline">7-Day</span> Free Trial!
        </h1>
        <p class="text-5xl mt-3">No Card required.</p>
      </div>
      <p>
        Quick-win game centric as ask email create done churning. Dunder good
        shark while deploy pollination. Standup great field hurting after
        feature. Three your high investigation work monday anyway. Dunder hits
        eager you hours. Organic floor strategy wider algorithm previous caught.
      </p>
    </div>

    <a
      href="/sign-in"
      class="ml-20 border rounded-lg border-lot-blue text-lot-blue p-5 font-semibold hover:bg-lot-blue hover:text-white"
    >
      Already a user? Login here
    </a>
  </div>

  <div
    class="bg-white w-full md:max-w-md lg:max-w-full md:mx-auto lg:w-1/2 xl:w-2/4 h-screen sm:px-6 lg:px-16 xl:px-24 flex items-center justify-center relative"
  >
    <div class="w-full">
      @if (isLoading) {
      <mat-progress-bar color="accent" class="my-3 ml-6" mode="indeterminate" />
      } @if (message) {
      <p class="text-sm font-semibold text-accent my-3 ml-6">
        {{ message }}
      </p>
      } @if (view === 'SIGNUP') {
      <ng-container *ngTemplateOutlet="signUpFlow" />
      } @if(view === 'PASSWORD'){
      <ng-container *ngTemplateOutlet="passwordFlow" />
      }
      <p class="text-sm text-center text-gray-500 mt-12 ml-6">
        &copy; {{ year }} Lean Or Teach - All Rights Reserved.
      </p>
    </div>
  </div>
</section>
} @if(view === 'SUBMIT'){

<div
  class="relative h-screen bg-lot-dark-gray/50 flex justify-center items-center"
>
  <img
    src="assets/images/new/coffee.png"
    alt=""
    srcset=""
    class="absolute top-0 left-0"
  />
  <section
    class="relative w-full lg:w-[773px] h-[739px] bg-white rounded-lg flex flex-col gap-5 items-center px-20 py-16"
  >
    <div class="h-10 w-56">
      <img src="assets/images/new/logo-light.png" alt="" srcset="" />
    </div>

    @if (isLoading) {
    <div class="h-80 w-full">
      <img
        src="assets/images/new/sign-up.png"
        alt=""
        srcset=""
        class="w-full h-full object-cover rounded-2xl"
      />
    </div>

    <div class="flex flex-col gap-5">
      <h1 class="text-lot-blue text-3xl font-bold">
        Creating Learn or Teach for {{ form.value.company }}
      </h1>
      <p class="text-center">Hang tight! You're almost there!</p>
    </div>
    } @if (!isLoading) {
    <div class="flex flex-col items-center gap-5">
      <h1 class="text-lot-blue text-3xl font-bold">Verification Email Sent!</h1>
      <div class="flex flex-col w-full max-w-sm">
        <img
          src="assets/images/new/Illustration-strolling.png"
          alt=""
          srcset=""
          class="object-cover w-full h-full"
        />
      </div>
      <p class="text-center">
        Thank you for signing up! To complete your registration, please confirm
        your email address by clicking the link we have sent to your inbox.
        <br />This step is essential to activate your account and ensure secure
        access to our platform. If you didn't receive the email, please check
        your spam folder or contact our support team for assistance.
      </p>
      <p class="text-center">
        Haven't received an email?
        <a
          href="javascript:void(0);"
          (click)="reverify()"
          class="text-lot-blue underline italic"
          >Resend it</a
        >
      </p>
    </div>
    }
  </section>
  <img
    src="assets/images/new/sitting-reading.png"
    alt=""
    srcset=""
    class="absolute bottom-0 right-0"
  />
  <button
    class="absolute bg-lot-ai-dark from-lot-ai-dark to-lot-ai bottom-10 right-20 z-30 rounded-full border border-lot-ai text-white flex items-center p-3"
  >
    <span class="material-symbols-outlined"> question_mark </span>
  </button>
</div>

}

<ng-template #signUpFlow>
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="px-10">
    <div class="grid grid-cols-1 gap-6">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="email"
            type="email"
            formControlName="email"
            autocomplete="new-password"
          />
          <label
            for="email"
            [class.error]="
              form.controls['email'].invalid && form.controls['email'].dirty
            "
            >Business Email Address</label
          >
        </div>
        <app-validation-text controlName="email" />
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="company"
            type="text"
            formControlName="company"
            autocomplete="new-password"
          />
          <label
            for="company"
            [class.error]="
              form.controls['company'].invalid && form.controls['company'].dirty
            "
            >Business Name</label
          >
        </div>
        <app-validation-text controlName="company" />
      </div>

      <div class="grid grid-cols-2 gap-5">
        <div class="form-lot-input">
          <div class="field">
            <input
              id="phone"
              type="tel"
              formControlName="phone"
              autocomplete="new-password"
              mask="(*************"
            />
            <label
              for="phone"
              [class.error]="
                form.controls['phone'].invalid && form.controls['phone'].dirty
              "
              >Business Phone</label
            >
          </div>
          <app-validation-text controlName="phone" />
        </div>

        <div class="form-lot-input">
          <div class="field">
            <input
              id="nbEmployees"
              type="tel"
              formControlName="nbEmployees"
              autocomplete="new-password"
              mask="separator.0"
              maxlength="5"
            />
            <label
              for="nbEmployees"
              [class.error]="
                form.controls['nbEmployees'].invalid &&
                form.controls['nbEmployees'].dirty
              "
              >No Of Employees</label
            >
          </div>
          <app-validation-text controlName="nbEmployees" />
        </div>
      </div>

      <div class="grid grid-cols-2 gap-5">
        <div class="form-lot-input">
          <div class="field">
            <input
              id="firstname"
              type="text"
              formControlName="firstname"
              autocomplete="new-password"
            />
            <label
              for="firstname"
              [class.error]="
                form.controls['firstname'].invalid &&
                form.controls['firstname'].dirty
              "
              >First Name</label
            >
          </div>
          <app-validation-text controlName="firstname" />
        </div>
        <div class="form-lot-input">
          <div class="field">
            <input
              id="lastname"
              type="text"
              formControlName="lastname"
              autocomplete="new-password"
            />
            <label
              for="lastname"
              [class.error]="
                form.controls['lastname'].invalid &&
                form.controls['lastname'].dirty
              "
              >Last Name</label
            >
          </div>
          <app-validation-text controlName="lastname" />
        </div>
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="title"
            type="text"
            formControlName="title"
            autocomplete="new-password"
          />
          <label
            for="title"
            [class.error]="
              form.controls['title'].invalid && form.controls['title'].dirty
            "
          >
            Your Title
          </label>
        </div>
        <app-validation-text controlName="title" />
      </div>
      <div class="form-lot-input">
        <div class="field">
          <textarea
            name="about"
            formControlName="about"
            id="about"
            rows="3"
          ></textarea>
          <label for="about">Tell us more (optional)</label>
        </div>
      </div>
    </div>

    <div class="flex flex-col justify-center items-center mt-5 mx-auto">
      <button class="button-primary w-48 py-5 px-3" type="submit">
        Start Free Trial
      </button>
    </div>
  </form>
</ng-template>
<ng-template #passwordFlow>
  <a
    href="javascript:void(0)"
    (click)="view = 'SIGNUP'"
    class="absolute top-5 left-5 text-lot-blue text-lg flex items-center gap-2"
  >
    <span class="material-symbols-outlined"> arrow_back_ios_new </span>
    Back to Login
  </a>
  <div class="text-center my-2">
    <h1 class="font-bold text-center text-2xl text-lot-blue">
      Create Your Password
    </h1>
  </div>

  <div class="w-full max-w-md mx-auto my-12">
    <app-password-form (data)="onSubmitPassword($event)" />
  </div>
</ng-template>
