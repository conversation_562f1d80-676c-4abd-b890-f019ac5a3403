import { Component, inject, OnInit } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'app-terms-conditions',
  imports: [MatDialogModule],
  templateUrl: './terms.component.html',
})
export class TermsAndConditionsComponent {
  public dialogRef: MatDialogRef<TermsAndConditionsComponent> =
    inject(MatDialogRef);

  terms = {
    header: {
      title: 'Subscription Auto-Payment Terms & Conditions',
      content:
        'Please read these terms and conditions carefully before using our subscription service.',
    },
    sections: [
      {
        id: 1,
        title: 'Subscription Agreement',
        content: {
          description:
            'By enrolling in our auto-payment subscription service, you authorize <b>Learn Or Teach</b> to charge your designated payment method automatically on a recurring basis according to your selected plan (monthly/annual).',
        },
      },
      {
        id: 2,
        title: 'Payment Authorization',
        content: {
          description: 'You agree to:',
          items: [
            'Maintain valid, up-to-date payment information',
            'Provide accurate billing details',
            'Authorize recurring charges until cancellation',
            'Pay all applicable taxes and fees',
          ],
        },
      },
      {
        id: 3,
        title: 'Billing Cycle',
        content: {
          items: [
            'Charges will occur every [period] on the original signup date or the next business day',
            'Your subscription will automatically renew unless canceled',
            "We'll notify you via email 5/3 days before each payment",
          ],
        },
      },
      {
        id: 4,
        title: 'Payment Methods',
        content: {
          description: 'We accept:',
          items: [
            'Visa, Mastercard, American Express',
            'Other Stripe-supported payment methods',
            'Digital wallets (Apple Pay, Google Pay)',
          ],
        },
      },
      {
        id: 5,
        title: 'Failed Payments',
        content: {
          description: 'If a payment fails:',
          items: [
            "We'll retry up to 2 times over 2 days",
            'Service may be suspended after 2 failed attempts',
            'You must update payment details to restore access',
          ],
        },
      },
      {
        id: 6,
        title: 'Cancellation Policy',
        content: {
          description: 'You may cancel:',
          items: [
            'Anytime before the next billing cycle',
            'Through your account dashboard',
            'By contacting customer support at [email/phone]',
          ],
          note: 'Cancellations take effect at the end of current billing period',
        },
      },
      {
        id: 7,
        title: 'Refunds',
        content: {
          items: [
            'No refunds for partial periods',
            'Exceptions considered within 7 days of charge',
            'Chargebacks may result in service termination',
          ],
        },
      },
      {
        id: 8,
        title: 'Price Changes',
        content: {
          description: 'We may modify prices with:',
          items: [
            '7/5 days advance notice',
            'Email notification',
            'Option to cancel before changes take effect',
          ],
        },
      },
      {
        id: 9,
        title: 'Data Security',
        content: {
          description: 'We use:',
          items: [
            'Stripe-certified PCI-compliant systems',
            'Tokenization for payment data',
            'Encryption for all transactions',
          ],
        },
      },
      {
        id: 10,
        title: 'Dispute Resolution',
        content: {
          items: [
            'Contact support first at <a href="https://learnorteach.com/contact-us" target="_blank" rel="noopener noreferrer">https://learnorteach.com/contact-us</a>',
            'Arbitration preferred over litigation',
            'Governing law: Texas',
          ],
        },
      },
      {
        id: 11,
        title: 'Acknowledgment',
        content: {
          description: 'By checking this box, you:',
          items: [
            'Agree to these terms',
            "Confirm you're authorized to use the payment method",
            'Accept responsibility for all charges',
          ],
        },
      } as {
        id: number;
        title: string;
        content: {
          description?: string;
          items?: string[];
        };
      },
    ],
  };
}
