import dayjs from 'dayjs';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { fromFetch } from 'rxjs/fetch';
import { firstValueFrom, isObservable, Observable, switchMap } from 'rxjs';
import { BillFrequency, SubscriptionStatus } from '../models';

export const CorporateName = 'LearnOrTeach Corp';

export const countries = [
  { code: 'US', label: 'United States' },
  { code: 'AD', label: 'Andorra' },
  { code: 'AF', label: 'Afghanistan' },
  { code: 'AG', label: 'Antigua and Barbuda' },
  { code: 'AI', label: 'Anguilla' },
  { code: 'AL', label: 'Albania' },
  { code: 'AM', label: 'Armenia' },
  { code: 'AO', label: 'Angola' },
  { code: 'AQ', label: 'Antarctica' },
  { code: 'AR', label: 'Argentina' },
  { code: 'AS', label: 'American Samoa' },
  { code: 'AT', label: 'Austria' },
  { code: 'AU', label: 'Australia' },
  { code: 'AW', label: 'Aruba' },
  { code: 'AX', label: 'Alland Islands' },
  { code: 'AZ', label: 'Azerbaijan' },
  { code: 'BA', label: 'Bosnia and Herzegovina' },
  { code: 'BB', label: 'Barbados' },
  { code: 'BD', label: 'Bangladesh' },
  { code: 'BE', label: 'Belgium' },
  { code: 'BF', label: 'Burkina Faso' },
  { code: 'BG', label: 'Bulgaria' },
  { code: 'BH', label: 'Bahrain' },
  { code: 'BI', label: 'Burundi' },
  { code: 'BJ', label: 'Benin' },
  { code: 'BL', label: 'Saint Barthelemy' },
  { code: 'BM', label: 'Bermuda' },
  { code: 'BN', label: 'Brunei Darussalam' },
  { code: 'BO', label: 'Bolivia' },
  { code: 'BR', label: 'Brazil' },
  { code: 'BS', label: 'Bahamas' },
  { code: 'BT', label: 'Bhutan' },
  { code: 'BV', label: 'Bouvet Island' },
  { code: 'BW', label: 'Botswana' },
  { code: 'BY', label: 'Belarus' },
  { code: 'BZ', label: 'Belize' },
  { code: 'CA', label: 'Canada' },
  { code: 'CC', label: 'Cocos (Keeling) Islands' },
  { code: 'CD', label: 'Congo, Democratic Republic of the' },
  { code: 'CF', label: 'Central African Republic' },
  { code: 'CG', label: 'Congo, Republic of the' },
  { code: 'CH', label: 'Switzerland' },
  { code: 'CI', label: "Cote d'Ivoire" },
  { code: 'CK', label: 'Cook Islands' },
  { code: 'CL', label: 'Chile' },
  { code: 'CM', label: 'Cameroon' },
  { code: 'CN', label: 'China' },
  { code: 'CO', label: 'Colombia' },
  { code: 'CR', label: 'Costa Rica' },
  { code: 'CU', label: 'Cuba' },
  { code: 'CV', label: 'Cape Verde' },
  { code: 'CW', label: 'Curacao' },
  { code: 'CX', label: 'Christmas Island' },
  { code: 'CY', label: 'Cyprus' },
  { code: 'CZ', label: 'Czech Republic' },
  { code: 'DE', label: 'Germany' },
  { code: 'DJ', label: 'Djibouti' },
  { code: 'DK', label: 'Denmark' },
  { code: 'DM', label: 'Dominica' },
  { code: 'DO', label: 'Dominican Republic' },
  { code: 'DZ', label: 'Algeria' },
  { code: 'EC', label: 'Ecuador' },
  { code: 'EE', label: 'Estonia' },
  { code: 'EG', label: 'Egypt' },
  { code: 'EH', label: 'Western Sahara' },
  { code: 'ER', label: 'Eritrea' },
  { code: 'ES', label: 'Spain' },
  { code: 'ET', label: 'Ethiopia' },
  { code: 'FI', label: 'Finland' },
  { code: 'FJ', label: 'Fiji' },
  { code: 'FK', label: 'Falkland Islands (Malvinas)' },
  { code: 'FM', label: 'Micronesia, Federated States of' },
  { code: 'FO', label: 'Faroe Islands' },
  { code: 'FR', label: 'France' },
  { code: 'GA', label: 'Gabon' },
  { code: 'GB', label: 'United Kingdom' },
  { code: 'GD', label: 'Grenada' },
  { code: 'GE', label: 'Georgia' },
  { code: 'GF', label: 'French Guiana' },
  { code: 'GG', label: 'Guernsey' },
  { code: 'GH', label: 'Ghana' },
  { code: 'GI', label: 'Gibraltar' },
  { code: 'GL', label: 'Greenland' },
  { code: 'GM', label: 'Gambia' },
  { code: 'GN', label: 'Guinea' },
  { code: 'GP', label: 'Guadeloupe' },
  { code: 'GQ', label: 'Equatorial Guinea' },
  { code: 'GR', label: 'Greece' },
  { code: 'GS', label: 'South Georgia and the South Sandwich Islands' },
  { code: 'GT', label: 'Guatemala' },
  { code: 'GU', label: 'Guam' },
  { code: 'GW', label: 'Guinea-Bissau' },
  { code: 'GY', label: 'Guyana' },
  { code: 'HK', label: 'Hong Kong' },
  { code: 'HM', label: 'Heard Island and McDonald Islands' },
  { code: 'HN', label: 'Honduras' },
  { code: 'HR', label: 'Croatia' },
  { code: 'HT', label: 'Haiti' },
  { code: 'HU', label: 'Hungary' },
  { code: 'ID', label: 'Indonesia' },
  { code: 'IE', label: 'Ireland' },
  { code: 'IL', label: 'Israel' },
  { code: 'IM', label: 'Isle of Man' },
  { code: 'IN', label: 'India' },
  { code: 'IO', label: 'British Indian Ocean Territory' },
  { code: 'IQ', label: 'Iraq' },
  { code: 'IR', label: 'Iran, Islamic Republic of' },
  { code: 'IS', label: 'Iceland' },
  { code: 'IT', label: 'Italy' },
  { code: 'JE', label: 'Jersey' },
  { code: 'JM', label: 'Jamaica' },
  { code: 'JO', label: 'Jordan' },
  { code: 'JP', label: 'Japan' },
  { code: 'KE', label: 'Kenya' },
  { code: 'KG', label: 'Kyrgyzstan' },
  { code: 'KH', label: 'Cambodia' },
  { code: 'KI', label: 'Kiribati' },
  { code: 'KM', label: 'Comoros' },
  { code: 'KN', label: 'Saint Kitts and Nevis' },
  { code: 'KP', label: "Korea, Democratic People's Republic of" },
  { code: 'KR', label: 'Korea, Republic of' },
  { code: 'KW', label: 'Kuwait' },
  { code: 'KY', label: 'Cayman Islands' },
  { code: 'KZ', label: 'Kazakhstan' },
  { code: 'LA', label: "Lao People's Democratic Republic" },
  { code: 'LB', label: 'Lebanon' },
  { code: 'LC', label: 'Saint Lucia' },
  { code: 'LI', label: 'Liechtenstein' },
  { code: 'LK', label: 'Sri Lanka' },
  { code: 'LR', label: 'Liberia' },
  { code: 'LS', label: 'Lesotho' },
  { code: 'LT', label: 'Lithuania' },
  { code: 'LU', label: 'Luxembourg' },
  { code: 'LV', label: 'Latvia' },
  { code: 'LY', label: 'Libya' },
  { code: 'MA', label: 'Morocco' },
  { code: 'MC', label: 'Monaco' },
  { code: 'MD', label: 'Moldova, Republic of' },
  { code: 'ME', label: 'Montenegro' },
  { code: 'MF', label: 'Saint Martin (French part)' },
  { code: 'MG', label: 'Madagascar' },
  { code: 'MH', label: 'Marshall Islands' },
  { code: 'MK', label: 'Macedonia, the Former Yugoslav Republic of' },
  { code: 'ML', label: 'Mali' },
  { code: 'MM', label: 'Myanmar' },
  { code: 'MN', label: 'Mongolia' },
  { code: 'MO', label: 'Macao' },
  { code: 'MP', label: 'Northern Mariana Islands' },
  { code: 'MQ', label: 'Martinique' },
  { code: 'MR', label: 'Mauritania' },
  { code: 'MS', label: 'Montserrat' },
  { code: 'MT', label: 'Malta' },
  { code: 'MU', label: 'Mauritius' },
  { code: 'MV', label: 'Maldives' },
  { code: 'MW', label: 'Malawi' },
  { code: 'MX', label: 'Mexico' },
  { code: 'MY', label: 'Malaysia' },
  { code: 'MZ', label: 'Mozambique' },
  { code: 'NA', label: 'Namibia' },
  { code: 'NC', label: 'New Caledonia' },
  { code: 'NE', label: 'Niger' },
  { code: 'NF', label: 'Norfolk Island' },
  { code: 'NG', label: 'Nigeria' },
  { code: 'NI', label: 'Nicaragua' },
  { code: 'NL', label: 'Netherlands' },
  { code: 'NO', label: 'Norway' },
  { code: 'NP', label: 'Nepal' },
  { code: 'NR', label: 'Nauru' },
  { code: 'NU', label: 'Niue' },
  { code: 'NZ', label: 'New Zealand' },
  { code: 'OM', label: 'Oman' },
  { code: 'PA', label: 'Panama' },
  { code: 'PE', label: 'Peru' },
  { code: 'PF', label: 'French Polynesia' },
  { code: 'PG', label: 'Papua New Guinea' },
  { code: 'PH', label: 'Philippines' },
  { code: 'PK', label: 'Pakistan' },
  { code: 'PL', label: 'Poland' },
  { code: 'PM', label: 'Saint Pierre and Miquelon' },
  { code: 'PN', label: 'Pitcairn' },
  { code: 'PR', label: 'Puerto Rico' },
  { code: 'PS', label: 'Palestine, State of' },
  { code: 'PT', label: 'Portugal' },
  { code: 'PW', label: 'Palau' },
  { code: 'PY', label: 'Paraguay' },
  { code: 'QA', label: 'Qatar' },
  { code: 'RE', label: 'Reunion' },
  { code: 'RO', label: 'Romania' },
  { code: 'RS', label: 'Serbia' },
  { code: 'RU', label: 'Russian Federation' },
  { code: 'RW', label: 'Rwanda' },
  { code: 'SA', label: 'Saudi Arabia' },
  { code: 'SB', label: 'Solomon Islands' },
  { code: 'SC', label: 'Seychelles' },
  { code: 'SD', label: 'Sudan' },
  { code: 'SE', label: 'Sweden' },
  { code: 'SG', label: 'Singapore' },
  { code: 'SH', label: 'Saint Helena' },
  { code: 'SI', label: 'Slovenia' },
  { code: 'SJ', label: 'Svalbard and Jan Mayen' },
  { code: 'SK', label: 'Slovakia' },
  { code: 'SL', label: 'Sierra Leone' },
  { code: 'SM', label: 'San Marino' },
  { code: 'SN', label: 'Senegal' },
  { code: 'SO', label: 'Somalia' },
  { code: 'SR', label: 'Suriname' },
  { code: 'SS', label: 'South Sudan' },
  { code: 'ST', label: 'Sao Tome and Principe' },
  { code: 'SV', label: 'El Salvador' },
  { code: 'SX', label: 'Sint Maarten (Dutch part)' },
  { code: 'SY', label: 'Syrian Arab Republic' },
  { code: 'SZ', label: 'Swaziland' },
  { code: 'TC', label: 'Turks and Caicos Islands' },
  { code: 'TD', label: 'Chad' },
  { code: 'TF', label: 'French Southern Territories' },
  { code: 'TG', label: 'Togo' },
  { code: 'TH', label: 'Thailand' },
  { code: 'TJ', label: 'Tajikistan' },
  { code: 'TK', label: 'Tokelau' },
  { code: 'TL', label: 'Timor-Leste' },
  { code: 'TM', label: 'Turkmenistan' },
  { code: 'TN', label: 'Tunisia' },
  { code: 'TO', label: 'Tonga' },
  { code: 'TR', label: 'Turkey' },
  { code: 'TT', label: 'Trinidad and Tobago' },
  { code: 'TV', label: 'Tuvalu' },
  { code: 'TW', label: 'Taiwan, Province of China' },
  { code: 'AE', label: 'United Arab Emirates' },
  { code: 'TZ', label: 'United Republic of Tanzania' },
  { code: 'UA', label: 'Ukraine' },
  { code: 'UG', label: 'Uganda' },
  { code: 'UY', label: 'Uruguay' },
  { code: 'UZ', label: 'Uzbekistan' },
  { code: 'VA', label: 'Holy See (Vatican City State)' },
  { code: 'VC', label: 'Saint Vincent and the Grenadines' },
  { code: 'VE', label: 'Venezuela' },
  { code: 'VG', label: 'British Virgin Islands' },
  { code: 'VI', label: 'US Virgin Islands' },
  { code: 'VN', label: 'Vietnam' },
  { code: 'VU', label: 'Vanuatu' },
  { code: 'WF', label: 'Wallis and Futuna' },
  { code: 'WS', label: 'Samoa' },
  { code: 'XK', label: 'Kosovo' },
  { code: 'YE', label: 'Yemen' },
  { code: 'YT', label: 'Mayotte' },
  { code: 'ZA', label: 'South Africa' },
  { code: 'ZM', label: 'Zambia' },
  { code: 'ZW', label: 'Zimbabwe' },
];

export const setCache = (key: string, data: any) => {
  const objData = {
    data,
    ttl: new Date().getTime() * 30 * 60 * 1000,
  };
  sessionStorage.removeItem(key);
  sessionStorage.setItem(key, JSON.stringify(objData));
};

export const getFromCache = (key: string) => {
  const obj = sessionStorage.getItem(key);
  if (!obj) {
    return;
  }
  const { data, ttl } = JSON.parse(obj);

  if (new Date().getTime() > ttl) {
    sessionStorage.removeItem(key);
    return;
  }

  return data;
};

export const getExpiredAt = (type: BillFrequency) => {
  const now = Date.now();
  const oneDay = 24 * 60 * 60 * 1000;

  const duration =
    type === BillFrequency.WEEKLY
      ? 7 * oneDay
      : type === BillFrequency.MONTHLY
      ? 30 * oneDay
      : type === BillFrequency.YEARLY
      ? 365 * oneDay
      : type === BillFrequency.YEARLY3
      ? 3 * 365 * oneDay
      : 1 * oneDay;

  return new Date(now + duration).toISOString();
};

// export const verifyAuthToken = (): UserItem | null => {
//   const data = getFromCache(GlobalKeys.IDM_TOKEN);
//   const UserItem = getFromCache(GlobalKeys.ID_INFO);
//   if (!data || !UserItem) {
//     return null;
//   }

//   const decodedToken = jwt_decode(data) as any;
//   const expiresAt = dayjs().add(decodedToken.exp, 'second');

//   if (dayjs().isAfter(dayjs(expiresAt))) {
//     sessionStorage.removeItem(GlobalKeys.IDM_TOKEN);
//     sessionStorage.removeItem(GlobalKeys.ID_INFO);
//     return null;
//   }

//   if (UserItem) {
//     return UserItem as UserItem;
//   }
//   return {
//     uid: null,
//     email: decodedToken.email,
//   } as any & UserItem;
// };

export const DialogType = [
  {
    type: 'ERROR',
  },
];

export const isEmberedLink = (url: string) => {
  if (!url) {
    return;
  }
  const ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
  return !['mp4', 'mpeg', 'mov', 'mp3', 'aac', 'ogg', 'wav', 'm4a', 'wma'].some(
    (format) => format === ext
  );
};

export const isAudioLink = (url: string) => {
  if (!url) {
    return;
  }
  const ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
  return ['mp3', 'aac', 'ogg', 'wav', 'm4a', 'wma'].some(
    (format) => format === ext
  );
};

export const getFileName = (url: string) => {
  if (!url) {
    return '';
  }
  return url
    ?.substring(url?.lastIndexOf('/') + 1)
    ?.split('_')
    ?.slice(1)
    ?.join(' ')
    .replaceAll('+', ' ');
};

export const isVideoLink = (url: string) => {
  if (!url) {
    return;
  }
  const ext = url.substring(url.lastIndexOf('.') + 1).toLowerCase();
  return ['mp4', 'mpeg', 'mov'].some((format) => format === ext);
};

export function getId(item: string | any): string {
  if (typeof item === 'string') {
    return item;
  }
  return item?.id;
}

export function setsAreEqual(a: Set<any>, b: Set<any>) {
  if (a.size !== b.size) {
    return false;
  }

  return Array.from(a).every((element) => {
    return b.has(element);
  });
}

export const stripHtml = (content: string) => {
  const elContent = new DOMParser().parseFromString(content ?? '', 'text/html');
  return elContent.body.textContent;
};

export const mapCollection = (data: any, prop: string) => {
  return data[prop].edges.map((edge: any) => edge.node) as Record<
    string,
    any
  >[];
};

export const exportToPDF = async (
  htmlEl: HTMLElement,
  fileName = 'certificate'
) => {
  const canvas = await html2canvas(htmlEl);
  const docWidth = 208;
  const docHeight = (canvas.height * docWidth) / canvas.width;
  const contentDataURL = canvas.toDataURL('image/png');
  const doc = new jsPDF('p', 'mm', 'a4');
  doc.addImage(contentDataURL, 'PNG', 0, 0, docWidth, docHeight);
  doc.save(`${fileName}_${dayjs().format('yyyy-mm-dd')}.pdf`);
};

export const makePDF = (htmlEl: HTMLElement, fileName = 'certificate') => {
  const pWidth = 910;
  const pHeight = 930;

  html2canvas(htmlEl).then((canvas) => {
    //! MAKE YOUR PDF
    const pdf = new jsPDF('p', 'pt', 'a4');

    for (var i = 0; i <= htmlEl.clientHeight / pHeight; i++) {
      //! This is all just html2canvas stuff
      var srcImg = canvas;
      var sX = 0;
      var sY = pHeight * i; // start 980 pixels down for every new page
      var sWidth = pWidth;
      var sHeight = pHeight;
      var dX = 0;
      var dY = 0;
      var dWidth = pWidth;
      var dHeight = pHeight;

      const onePageCanvas = document.createElement('canvas');
      onePageCanvas.setAttribute('width', pWidth.toString());
      onePageCanvas.setAttribute('height', '980');
      var ctx = onePageCanvas.getContext('2d');
      // details on this usage of this function:
      // https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API/Tutorial/Using_images#Slicing
      ctx?.drawImage(srcImg, sX, sY, sWidth, sHeight, dX, dY, dWidth, dHeight);

      // document.body.appendChild(canvas);
      var canvasDataURL = onePageCanvas.toDataURL('image/png', 1.0);

      var width = onePageCanvas.width;
      var height = onePageCanvas.clientHeight;

      //! If we're on anything other than the first page,
      // add another page
      if (i > 0) {
        pdf.addPage([612, 791]); //8.5" x 11" in pts (in*72)
      }
      //! now we declare that we're working on that page
      pdf.setPage(i + 1);
      //! now we add content to that page!
      // pdf.addImage(canvasDataURL, 'PNG', 0, 0, width, height);
      pdf.addImage(canvasDataURL, 'PNG', 20, 40, width * 0.62, height * 0.62);
    }
    //! after the for loop is finished running, we save the pdf.
    pdf.save(`${fileName}_${dayjs().format('yyyy-mm-dd')}.pdf`);
  });
};

export const exportToFile = async (htmlEl: HTMLElement, fileName: string) => {
  const canvas = await html2canvas(htmlEl);
  const contentDataURL = canvas.toDataURL('image/png');
  const fileImage = await firstValueFrom(
    fromFetch(contentDataURL).pipe(switchMap((res) => res.blob()))
  );
  return new File([fileImage], fileName + '.png', { type: 'image/png' });
};

export const isMobileDevice = () => {
  if (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  ) {
    return true;
  } else {
    return false;
  }
};

export const calculateScore = (current: number): number => {
  const modulo = current % 5;
  return modulo > 0 ? current - modulo + 5 : current;
};

export const isAnyElementChangedItsPosition = (
  arr1: any[],
  arr2: any[]
): boolean => {
  for (let i = 0; i < arr1.length; ++i) {
    if (arr1[i] !== arr2[i]) {
      return true;
    }
  }
  return false;
};

export const stripTag = (tag: string) => {
  return new RegExp(`<[//]{0,1}(${tag})[^><]*>`, 'gi');
};

// export const getDayCount = (startDate: string, endDate: string) => {
//   return Math.abs(
//     dayjs(startDate.split('T').at(0)).diff(
//       dayjs(endDate.split('T').at(0)),
//       'days'
//     )
//   );
// };

export const dateDiff = (date1: string, date2: string) => {
  if (!date1 || !date2) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      label: `${0}d ${0}h ${0}m`,
      labelFull: `${0}d ${0}h ${0}m ${0}s`,
      labelPartial: `${0}m ${0}s`,
    };
  }

  // Calculate the difference in milliseconds
  const diff = Math.abs(new Date(date2).getTime() - new Date(date1).getTime());

  // Get the number of days
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  // Get the number of hours
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

  // Get the number of minutes
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  // Get the number of seconds
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);

  // Return the results
  return {
    days: days,
    hours: hours,
    minutes: minutes,
    label: `${days}d ${hours}h ${minutes}m`,
    labelFull: `${days}d ${hours}h ${minutes}m ${seconds}s`,
    display: `${days} Day${days > 1 ? 's' : ''} - ${hours} Hour${
      hours > 1 ? 's' : ''
    } - ${minutes} Minute${minutes > 1 ? 's' : ''} - ${seconds} Second${
      seconds > 1 ? 's' : ''
    }`,
    labelPartial:
      days === 0 && hours > 0
        ? `${days}d ${hours}h`
        : days === 0 && hours === 0 && minutes > 0
        ? `${hours}h ${minutes}m`
        : `${minutes}m ${seconds}s`,
  };
};

export const getPagination = (page: number, size: number) => {
  const limit = size ? +size : 3;
  const from = page ? page * limit : 0;
  const to = page ? from + size : size;

  return { from, to };
};

export const tryPromise = <T>(input: Promise<T> | Observable<T>) => {
  const req = isObservable(input) ? firstValueFrom(input) : input;
  return req
    .then((res) => [undefined, res] as [undefined, T])
    .catch((err) => [err, undefined] as [Error, undefined]);
};

export const getFileFromUrl = async (url: string) => {
  if (!/^(ftp|http|https):\/\/[^ "]+$/.test(url)) {
    return null;
  }
  const fileName = url.split('/').pop() ?? '_fileName_' + Date.now();
  const [_, data] = await tryPromise(fetch(url).then((res) => res.blob()));
  return data ? new File([data], fileName) : null;
};
export const getFileFromBase64 = async (data: string) => {
  const arr = data.split(',');
  const mime = arr[0].match(/:(.*?);/)?.at(1) ?? 'image/jpeg';
  const bstr = atob(arr[arr.length - 1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], '_fileName_' + Date.now(), { type: mime });
};
