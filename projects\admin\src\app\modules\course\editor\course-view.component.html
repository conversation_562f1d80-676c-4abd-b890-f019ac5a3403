<div class="flex flex-col h-full">
  @if (source.value(); as item) {
    <div class="flex justify-between items-end">
      <div class="flex flex-col text-lot-dark">
        <div class="flex items-center gap-5">
          @if (view !== 'LESSON') { @if (!isPreview) {
          <a
            href="javascript:void(0)"
            (click)="back()"
            class="text-lot-blue text-sm flex items-center gap-2"
          >
            <span class="material-symbols-outlined"> arrow_back_ios_new </span>
            Back to Courses
          </a>
          <span>|</span>
          <span>Creating Course</span>
          } }
        </div>
        <h1 class="text-lot-dark text-2xl font-bold flex items-center gap-2"
          [class.mt-10]="view === 'LESSON'">
          {{ item.name }}
          @if (view === 'LESSON' && content) {
          <i class="fa-solid fa-chevron-right text-lot-dark text-sm"></i>
          <span class="text-lot-blue text-2xl">{{ content.lesson.name }}</span>
          }
        </h1>
      </div>
  
      @if (!isPreview) {
      <div class="flex gap-5">
        @if (isSettings) {
        <button
          type="button"
          (click)="delete(item)"
          class="button-danger w-fit py-2 px-6"
        >
          Delete
        </button>
        <button
          type="button"
          (click)="preview(item.id)"
          class="button-primary-dark w-fit py-2 px-6"
        >
          Save Settings
        </button>
        } @else {
        <button
          type="button"
          (click)="preview(item.id)"
          class="button-primary w-fit py-2 px-6"
        >
          {{ tab === 3 ? "Save Enrollment" : "Preview" }}
        </button>
        }
      </div>
      }
    </div>
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (!isPreview) {
  <div
    class="flex-grow mt-4 mb-10 bg-white py-8 px-10 rounded-xl shadow-md w-full"
  >
    @if (view === 'COURSE') {
    <ng-container *ngTemplateOutlet="viewCourse; context: { item: item }" />
    }<!--  @if (view === 'LESSON' && content) {
    <app-lesson-builder [data]="content" />
    } -->
  </div>
  } @else {
  <div class="flex flex-col gap-8 justify-center items-center py-20">
    <div class="flex flex-col justify-center items-center gap-3 mt-20">
      <span class="text-lot-dark text-center text-2xl"
        >Course Preview in progress ...</span
      >
      <div class="flex gap-5">
        @for (item of [1,2,3,4]; track $index) {
        <span class="size-6 bg-lot-blue rounded-full animate-pulse"></span>
        }
      </div>
    </div>
    <button
      type="button"
      (click)="exit()"
      class="button-primary-outline w-fit px-6"
    >
      EXIT
    </button>
  </div>
  } } @else {
  <p class="text-center py-20">Course Preview in progress ...</p>
  <div class="flex justify-center items-center gap-5">
    @for (item of [1,2,3,4]; track $index) {
    <span class="size-6 bg-lot-blue rounded-full animate-pulse"></span>
    }
  </div>
  }
</div>

<ng-template #viewCourse let-item="item">
  <div
    class="text-sm font-medium text-center flex justify-between items-center mb-10"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="setTab(item.id)"
          class="inline-block font-bold text-sm p-2 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
          [class.text-lot-dark]="tab === item.id"
          [class.border-transparent]="tab !== item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>

  <app-resource-loader [source]="source" />
  @if (tab === 1) {
  <app-course-builder [data]="item" (view)="onView($event)" />
  } @if (tab === 2) {
  <app-course-settings [data]="item" />
  } @if (tab === 3) {
  <app-course-assignment [courseId]="item.id" />
  }
</ng-template>
