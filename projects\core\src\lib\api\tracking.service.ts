import { Injectable } from '@angular/core';
import { dateDiff } from '../utils';

@Injectable({ providedIn: 'root' })
export class TrackingService {


  breakDownByQuater(current: number, fraction: number): number[] {
    // Check for edge case where n is 0
    if (current === 0) {
      return [0];
    }
    if (fraction === 0) {
      throw new Error("The denominator 'f' cannot be zero.");
    }
    const fractions = [];
    let currentN = current;

    while (currentN > 0) {
      if (currentN >= fraction) {
        fractions.push(fraction);
        currentN -= fraction;
      } else {
        fractions.push(currentN);
        currentN = 0;
      }
    }

    return fractions;
  }

  calculateDurationByFraction(modules: string[], lastChange: string) {
    const rangeDate = this.getMinAndMaxDates(modules);
    if (!rangeDate) {
      return {
        lastChangedDate: undefined,
        countDay: '-',
      };
    }

    if (modules.length === 1) {
      return {
        lastChangedDate: lastChange,
        countDay: dateDiff(rangeDate.min.toISOString(), lastChange)
          .labelPartial,
      };
    }

    return {
      lastChangedDate: rangeDate.min.toISOString(),
      countDay: dateDiff(
        rangeDate.min.toISOString(),
        rangeDate.max.toISOString()
      ).labelPartial,
    };
  }

  getMinAndMaxDates(dateArray: string[]): {
    min: Date;
    max: Date;
  } | null {
    if (dateArray.length === 0) {
      return null; // Return null or handle this case as needed
    }

    const dateObjects = dateArray.map((date) => new Date(date));

    const minDate = new Date(
      Math.min(...dateObjects.map((date) => date.getTime()))
    );
    const maxDate = new Date(
      Math.max(...dateObjects.map((date) => date.getTime()))
    );

    return { min: minDate, max: maxDate };
  }
}
