import { Injectable, inject, signal } from '@angular/core';
import { GlobalStateService } from '../services';
import {
  EnrollmentView,
  InstructorEnrollment,
  LearningInstructor,
  UserItem,
} from '../models';
import { getId, getPagination } from '../utils';
import { CourseCoreService } from './course.core';
import { Subject } from 'rxjs';

export type InstructorFilter = {
  query?: string;
  type: 'INSTRUCTORLED' | 'LEARNINGPATH';
  paging: {
    page: number;
    size: number;
  };
};

@Injectable({
  providedIn: 'root',
})
export class LearningInstructorService {
  state = inject(GlobalStateService);
  courseService = inject(CourseCoreService);

  viewType = signal<'LEARNINGPATH' | 'SETTINGS'>('LEARNINGPATH');
  actionTrigger = new Subject<{
    type: 'SETTINGS' | 'LEARNINGPATH' | 'PREVIEW';
    id: string;
  }>();

  get user(): UserItem {
    return this.state.user();
  }

  async get(filter: InstructorFilter) {
    const orgId = getId(this.user?.organization);
    const { from, to } = getPagination(
      filter.paging.page || 1,
      filter.paging.size || 10
    );
    const req = this.state.supabase
      .from('learning_instructors')
      .select('*', { count: 'exact' })
      .eq('organization', orgId)
      .eq('type', filter.type);
    //.range(from, to);
    if (filter.query?.trim()?.length) {
      req.or(
        `name.ilike.%${filter.query}%,description.ilike.%${filter.query}%`
      );
    }
    const { data, error, count } = await req;
    return {
      data: (data ?? []) as LearningInstructor[],
      count: count ?? 0,
      error: error?.message,
    };
  }

  async getById(id: string) {
    const { data, error } = await this.state.supabase
      .from('learning_instructors')
      .select('*')
      .eq('id', id);
    return {
      data: data?.at(0) as LearningInstructor,
      error: error?.message,
    };
  }

  async add(payload: LearningInstructor) {
    const user = this.user;
    payload = {
      ...payload,
      created_by: user.id,
      creator: {
        name: this.user.firstname + ' ' + this.user.lastname,
        email: this.user.email,
        bio: 'Author',
        avatar: this.user.avatar ?? 'assets/images/user-profile.jpg',
      },
      organization: getId(user?.organization),
    };

    const { data, error } = await this.state.supabase
      .from('learning_instructors')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async update(item: LearningInstructor) {
    const user = this.user;

    const { id, ...payload } = {
      ...item,
      updated_by: user.id,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.state.supabase
      .from('learning_instructors')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async delete(id: string) {
    const { error } = await this.state.supabase
      .from('learning_instructors')
      .delete()
      .eq('id', id);
    return {
      data: !error,
      error: error?.message,
    };
  }

  async getEnrollment(id: string) {
    const { data, error } = await this.state.supabase
      .from('instructor_enrollments')
      .select(
        '*, team(id, name), group(id, name), user(id, firstname, lastname,email)'
      )
      .eq('instructor', id);

    return {
      data: {
        users:
          data
            ?.filter((x) => !!x.user)
            .map((x) => ({
              ...x.user,
              enrollId: x.id,
              name: `${x.user.firstname} ${x.user.lastname}`,
              username: `${x.user.firstname} ${x.user.lastname}`,
            })) ?? [],
        teams:
          data
            ?.filter((x) => !!x.team)
            .map((x) => ({ ...x.team, enrollId: x.id, teamOnly: !x.group })) ?? [],
        groups:
          data
            ?.filter((x) => !!x.group)
            .map((x) => ({ ...x.group, team: x.team, enrollId: x.id })) ?? [],
      } as EnrollmentView,
      error: error?.message,
    };
  }

  async saveEnrollment(payload: {
    items: InstructorEnrollment[];
    toDelete: string[];
  }) {
    const errors: string[] = [];
    if (payload.toDelete.length) {
      const resDel = await Promise.all(
        payload.toDelete.map(
          async (id) =>
            await this.state.supabase
              .from('instructor_enrollments')
              .delete()
              .eq('id', id)
        )
      );
      errors.push(...resDel.map((r) => r.error?.message!));
    }

    const news = payload.items
      .filter((x) => !x.id && (!!x.instructor || !!x.user || !!x.group))
      .map(({ id, ...x }) => ({
        ...x,
        created_by: this.user.id,
      }));

    if (news.length) {
      const res = await this.state.supabase
        .from('instructor_enrollments')
        .insert(news);
      errors.push(res.error?.message!);
    }

    return errors.filter(Boolean);
  }
}
