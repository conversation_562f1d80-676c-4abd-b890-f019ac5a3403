import { HttpClient } from '@angular/common/http';
import { Injectable, inject, resource, signal } from '@angular/core';
// import { StripeService } from 'ngx-stripe';
import { lastValueFrom, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import {
  ApiRespone,
  BillingPlan,
  Invoices,
  mapResponse,
  OrgSubscription,
  UserItem,
} from '../models';
import { GlobalStateService } from '../services';
import { getId } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class BillingService {
  state = inject(GlobalStateService);
  http = inject(HttpClient);

  get user(): UserItem {
    return this.state.user();
  }

  get organization() {
    return this.user.organization;
  }

  // get subscription() {
  //   return billingSubscriptionsMockData.filter(
  //     (s) =>
  //       s.organization.id === getId(this.organization) && s.status === 'ACTIVE'
  //   )[0];
  // }

  userId = signal<string | undefined>(undefined);

  historySource = resource({
    request: () => this.userId(),
    loader: async ({ request }) => {
      if (!request) {
        return [];
      }
      const data = await this.getMySubscriptions();
      return data.data;
    },
  });

  async getMySubscriptions(orgId?: string) {
    const req = this.state.supabase
      .from('org_subscription')
      .select('*, plan(id, name)')
      .eq('organization', orgId ?? getId(this.organization?.id));
    const { data, error } = await req;
    return {
      data: (data ?? []) as OrgSubscription[],
      error: error?.message,
    };
  }

  async getPlans() {
    const { data, error } = await this.state.supabase
      .from('billing_plans')
      .select('*')
      .eq('status', 'ACTIVE')
      .neq('frequency', 'NONE');
    return {
      data: (data ?? []) as BillingPlan[],
      error: error?.message,
    };
  }

  // async getPriceConfiguration(): Promise<ApiRespone<PriceConfigurator>> {
  //   const { data, error } = await this.state.supabase
  //     .from('price_configurator')
  //     .select('*');
  //   return {
  //     data: (data ?? []) as PriceConfigurator[],
  //     error: error?.message,
  //   };
  // }

  async savePlans(payload: BillingPlan[]) {
    const { data, error } = await this.state.supabase
      .from('billing_plans')
      .insert(
        payload.map(({ id, ...q }) => ({ ...q, created_by: this.user.id }))
      )
      .select('*');
    return {
      data: (data ?? []) as BillingPlan[],
      error: error?.message,
    };
  }

  async savePriceConfiguration(payload: Partial<BillingPlan>) {
    const user = this.user;
    if (!user) {
      return { data: [], error: 'User data not found.' };
    }

    payload = {
      ...payload,
      created_by: user.id,
      // discount: payload.discount ?? 1,
      // organization: getId(user?.organization),
    };

    const req = this.state.supabase.from('price_configurator');

    if (payload.id) {
      payload = {
        ...payload,
        updated_by: user.id,
        updated_at: new Date().toISOString(),
        updatedName: payload.id
          ? user.firstname + ' ' + user.lastname
          : undefined,
      };
      const { data, error } = await req
        .update(payload)
        .eq('id', payload.id)
        .select();
      return {
        data: (data ?? []) as BillingPlan[],
        error: error?.message,
      };
    }
    payload = {
      ...payload,
      createdName: user.firstname + ' ' + user.lastname,
    };

    const { data, error } = await req.insert(payload).select('*');
    return {
      data: (data ?? []) as BillingPlan[],
      error: error?.message,
    };
  }

  async getInvoices(): Promise<ApiRespone<Invoices>> {
    const { data, error } = await this.state.supabase.from('invoices').select();
    return {
      data: <Invoices[]>data ?? [],
      error: error?.message,
    };
  }

  async saveInvoice(payload: Partial<Invoices>) {
    const user = this.user;
    if (!user) {
      return { data: [], error: 'User data not found.' };
    }

    payload = {
      ...payload,
      created_by: user.id,
      // organization: getId(user?.organization),
    };

    const req = this.state.supabase.from('invoices');

    if (payload.id) {
      payload = {
        ...payload,
        updated_by: user.id,
        updated_at: new Date().toISOString(),
        updatedName: payload.id
          ? user.firstname + ' ' + user.lastname
          : undefined,
      };
      const { data, error } = await req
        .update(payload)
        .eq('id', payload.id)
        .select('*');
      return {
        data: <Invoices[]>data ?? [],
        error: error?.message,
      };
    }
    payload = {
      ...payload,
      createdName: user.firstname + ' ' + user.lastname,
    };

    const { data, error } = await req.insert(payload).select('*');
    return {
      data: <Invoices[]>data ?? [],
      error: error?.message,
    };
  }
}
