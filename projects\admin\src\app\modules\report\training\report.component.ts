import { Component, OnInit, resource } from '@angular/core';
import { ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-report-training',
  imports: [ResourceHeaderComponent],
  templateUrl: './report.component.html',
})
export class TrainingReportComponent implements OnInit {
  source = resource({
    loader: async ({ request }) => {
      return Promise.resolve([]);
    },
  });

  ngOnInit() {}
}
