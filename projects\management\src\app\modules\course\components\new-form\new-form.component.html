<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col gap-5 px-12 py-10"
>
  <h1 class="text-2xl font-semibold">Create a New Course</h1>
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  }
@if (error) {
    <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
    }
  <div class="form-lot-input">
    <div class="field">
      <input
        type="text"
        id="title"
        formControlName="title"
        placeholder="How to Make a Good Sale"
      />
      <label for="title" [class.error]="f['title'].invalid && f['title'].dirty">
        Name your Course
      </label>
    </div>
    @if(f['title'].errors && f['title'].invalid && f['title'].dirty){
    <div class="error">
      @if (f['title'].errors['required']) {
      <span>Course name is required</span>
      } @if (f['title'].errors['minlength']) {
      <span>Course name must be at least 3 characters</span>
      }
    </div>
    }
  </div>

  <div class="form-lot-input mt-2">
    <div class="field">
      <quill-editor
        formControlName="description"
        [styles]="editorStyle"
        formControlName="description"
        placeholder="Enter your description"
      />
      <label for="description" [class.error]="f['description'].invalid && f['description'].dirty"> Give your course a description </label>
    </div>

    @if(f['description'].errors && f['description'].invalid &&
    (f['description'].dirty || f['description'].touched)){
    <div class="error">
      @if (f['description'].errors['required']) {
      <span>Course description is required</span>
      }
    </div>
    }
  </div>

  <div class="flex justify-end items-center gap-4">
    <button class="button-primary-outline w-fit" type="submit">Cancel</button>
    <button class="button-primary w-fit py-1.5" type="submit">
      Start Course Creation
    </button>
  </div>
</form>
