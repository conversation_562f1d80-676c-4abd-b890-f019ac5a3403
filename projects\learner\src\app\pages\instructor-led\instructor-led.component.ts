import {
  Component,
  computed,
  inject,
  input,
  OnInit,
  resource,
  signal,
} from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { LearningInstructor, LearningInstructorService, TrainingService, UserTrackingItem } from '@lms/core';
import {
  HtmlWrapperComponent,
  ProgressComponent,
  ResourceHeaderComponent,
} from '@lms/shared';
import { DatePipe, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-instructor-led',
  imports: [
    ResourceHeaderComponent,
    HtmlWrapperComponent,
    RouterLink,
    ProgressComponent,
    DatePipe,
    NgTemplateOutlet,
  ],
  templateUrl: './instructor-led.component.html',
})
export class InstructorLedComponent implements OnInit {
  router = inject(Router);
  trainingService = inject(TrainingService);

  id = input<string>('');

  rating = signal<number>(0);
  comments = signal<string>('');

  source = resource<
    { instructor: LearningInstructor; tracking?: UserTrackingItem },
    string
  >({
    request: () => this.id(),
    loader: async ({ request }) => {
      const res = await this.trainingService.queryInstructorOne(request);
      if (!res) {
        throw new Error('Instructor Led not found');
      }
      return {
        instructor: res.instructor!,
        tracking: res,
      };
    },
  });

  allPrerequisites = false;

  tab = 1;

  tabs = [
    {
      id: 1,
      name: 'Course Content',
    },
    {
      id: 2,
      name: 'Instructor Bio',
    },
    {
      id: 3,
      name: 'Resource',
    },
    {
      id: 4,
      name: 'Sessions',
    },
  ];

  progress = computed(() => {
    const items = this.source.value()?.instructor.prerequisites || [];
    const completedPre = items.filter(
      (x: any) => x.status === 'COMPLETED'
    ).length;
    const count = items.length;
    const progress = Math.ceil((completedPre / count) * 100);

    const circumference = 2 * Math.PI * 60;
    const dashOffset = circumference - (progress / 100) * circumference;
    return {
      progress,
      dashOffset,
      total: count,
      complete: completedPre,
      remain: count - completedPre,
    };
  });

  get dashArray() {
    return 2 * Math.PI * 55;
  }

  ngOnInit(): void {
    // this.trainingService.learningPathId.set(this.id());
  }

  goToCourse(id: string) {
    this.router.navigate(['/myTraining/training', id]);
  }
  
  bookEvent(item: any) {
    this.allPrerequisites = item.prerequisites.some(
      (x: any) => x.status !== 'COMPLETED'
    );
    if (this.allPrerequisites) {
      return;
    }
  }
}
