<div class="flex flex-col w-full">
  <div class="flex items-center gap-3 w-full">
    <span class="flex-1 ml-2 mb-1 font-semibold text-lot-dark">
      {{ title() }}
    </span>
    <div class=" relative inline-block text-left dropdown group">
      <div class="flex w-full items-center">
        <button
          [class]="
            'inline-flex justify-between px-10 py-3 text-lg font-normal leading-5 text-lot-dark transition duration-150 ease-in-out border border-lot-gray hover:text-gray-500 focus:outline-none focus:border-main focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 rounded-md w-' +
            with()
          "
          type="button"
          aria-haspopup="true"
          aria-expanded="true"
          aria-controls="headlessui-menu-items-117"
        >
          @if (icon()) {
          <span class="material-symbols-outlined mr-2">{{ icon() }}</span>
          }
          <span>{{ current || options()[0].name }}</span>
          <svg
            class="w-5 h-5 ml-2 -mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </button>
      </div>
      <div class="hidden dropdown-menu group-hover:block">
        <div
          class="absolute right-0 w-full min-w-28 max-h-64 overflow-y-auto origin-top-right bg-white border border-gray-200 divide-y divide-gray-100 rounded-xl p-3 shadow-lg outline-none z-40 scrollbar"
          aria-labelledby="headlessui-menu-button-1"
          role="menu"
        >
          <div class="py-1">
            @for (item of options(); track $index) {
            <a
              href="javascript:void(0)"
              (click)="select(item)"
              class="text-gray-700 flex justify-between w-full px-4 py-2 text-lg leading-5 text-left hover:bg-gray-200"
              role="menuitem"
              >{{ item.name }}
            </a>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
