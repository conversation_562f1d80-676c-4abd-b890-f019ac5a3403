import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  TemplateRef,
  viewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Course,
  CourseCoreService,
  Lesson,
  markControlsDirty,
  Module,
  ToastMessageType,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  CdkDragDrop,
  CdkDropList,
  CdkDrag,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import { firstValueFrom } from 'rxjs';
import { DialogComponent, SortByOrder } from '@lms/shared';

@Component({
  selector: 'app-course-builder',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatProgressBarModule,
    SortByOrder,
    Cdk<PERSON>ropList,
    CdkDrag,
  ],
  templateUrl: 'builder.component.html',
})
export class CourseBuilderComponent {
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  formRef = viewChild<TemplateRef<any>>('formTemplate');

  @Input({ required: true }) data: Course;
  @Output() view = new EventEmitter<{ module: Module; lesson: Lesson }>();

  isLoading = false;
  error?: string;
  type: 'MODULE' | 'LESSON' = 'MODULE';

  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    id: new FormControl(''),
  });
  module?: Module;
  dialogRef?: MatDialogRef<any>;

  get f() {
    return this.form.controls;
  }

  get modules() {
    return this.data.modules || [];
  }

  add(module?: Module, type: 'MODULE' | 'LESSON' = 'MODULE') {
    this.type = type;
    this.module = module;
    this.form.reset();
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  editModule(module: Module) {
    this.type = 'MODULE';
    this.form.patchValue({
      id: module.id,
      name: module.name,
    });
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  editLesson(lesson: Lesson) {
    this.type = 'LESSON';
    this.form.patchValue({
      id: lesson.id,
      name: lesson.name,
    });
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  async deleteModule(data: Module) {
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${data.name} module. All lessons associated to it will be deleted.`,
            title: 'Are you sure to remove this Module?',
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.deleteModule(data.id);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
      }
      if (result.data) {
        this.service.courseSource.reload();
      }
    }
  }

  async deleteLesson(data: Lesson) {
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${data.name} lesson. All contents associated to it will be deleted.`,
            title: 'Are you sure to remove this Lesson?',
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.deleteLesson(data.id);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
      }
      if (result.data) {
        this.service.courseSource.reload();
      }
    }
  }

  goToLesson(module: Module, lesson: Lesson) {
    this.view.emit({ module, lesson });
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    const maxOrder =
      this.type === 'MODULE'
        ? Math.max(...this.modules.map((m) => m.order))
        : Math.max(...(this.module?.lessons ?? []).map((l) => l.order));

    const payload = {
      name: this.form.value.name,
      order: maxOrder + 1,
    } as any;

    let res: {
      data: Lesson | Module | string;
      error: string | undefined;
    } = {
      data: '',
      error: undefined,
    };

    if (this.form.value.id) {
      payload['id'] = this.form.value.id;
      res =
        this.type === 'MODULE'
          ? await this.service.saveModule(payload)
          : await this.service.saveLesson(payload);
    } else {
      if (this.type === 'MODULE') {
        payload['course'] = this.data.id;
      } else {
        payload['module'] = this.module?.id;
      }

      res =
        this.type === 'MODULE'
          ? await this.service.addModule(payload)
          : await this.service.addLesson(payload);
    }
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: `${this.type === 'MODULE' ? 'Module' : 'Lesson'} ${
        this.form.value.id ? 'updated' : 'created'
      } successfully`,
      type: ToastMessageType.SUCCESS,
    });
    this.dialogRef?.close();
    this.service.courseSource.reload();
  }

  async dropModule(event: CdkDragDrop<Module[]>) {
    moveItemInArray(
      this.data.modules || [],
      event.previousIndex,
      event.currentIndex
    );
    this.isLoading = true;
    await Promise.all(
      this.data.modules?.map((m, i) => {
        return this.service.saveModule({
          id: m.id,
          order: i + 1,
        } as any);
      }) ?? []
    );
    this.service.courseSource.reload();
    this.isLoading = false;
  }

  async dropLesson(event: CdkDragDrop<Lesson[]>, lessons: Lesson[]) {
    moveItemInArray(lessons, event.previousIndex, event.currentIndex);
    this.isLoading = true;
    await Promise.all(
      lessons.map((m, i) => {
        return this.service.saveLesson({
          id: m.id,
          order: i + 1,
        } as any);
      }) ?? []
    );
    this.service.courseSource.reload();
    this.isLoading = false;
  }
}
