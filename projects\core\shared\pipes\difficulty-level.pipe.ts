import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'difficultyLevel',
  standalone: true
})
export class DifficultyLevelPipe implements PipeTransform {
  transform(value: unknown, ...args: unknown[]): unknown {
    let level = '';
    switch (value) {
      case 0:
        level = 'Beginner';
        break;
      case 1:
        level = 'Intermediate';
        break;
      case 2:
        level = 'Advanced';
        break;
    }
    return level;
  }
}
