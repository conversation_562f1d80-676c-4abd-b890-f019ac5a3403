import { Component, inject } from '@angular/core';
import { AuthService, GlobalStateService, LoadingService } from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  Router,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import { AsyncPipe, NgIf } from '@angular/common';
import { MenuItem } from '../services';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'app-layout',
  imports: [
    MatProgressBarModule,
    RouterOutlet,
    NgIf,
    AsyncPipe,
    RouterLink,
    MatMenuModule,
  ],
  templateUrl: './layout.component.html',
})
export class LayoutComponent {
  state = inject(GlobalStateService);
  authService = inject(AuthService);
  loader = inject(LoadingService);
  router = inject(Router);

  menus = mainMenu;

  subMenu = [
    // {
    //   icon: 'settings',
    //   name: 'Settings',
    //   link: '/settings',
    // },
    {
      icon: 'help',
      name: 'FAQ',
      link: '/faq',
    },
    {
      icon: 'input',
      name: '<PERSON>gout',
      link: '/',
    },
  ];

  get isPath() {
    return window.location.pathname;
  }

  get hideSideMenu() {
    return ['LESSON'].includes(this.state.viewType());
  }
  // get isPreview() {
  //   return ['PREVIEW'].includes(this.state.viewType());
  // }

  gotTo(link: string) {
    if (link === '/') {
      this.authService.signOutAndRedirect();
      return;
    }
    this.router.navigate([link]);
  }
}

const mainMenu = [
  {
    title: 'Home',
    path: '/lms',
    select: '/lms/dashboard',
    icon: 'fas fa-columns',
    active: true,
  },
  {
    title: 'Learn',
    path: '/myTraining',
    select: '/myTraining',
    icon: 'fas fa-columns',
    external: true,
    active: true,
  },
  {
    title: 'Courses',
    icon: 'fas fa-book',
    active: true,
    path: '/lms/courses',
    select: '/lms/courses',
  },
  {
    title: 'Users',
    path: '/lms/users',
    select: '/lms/users',
    active: true,
  },
  {
    title: 'Reporting',
    path: '/lms/reporting',
    select: '/lms/reporting',
    icon: 'fas fa-file-medical-alt',
    active: true,
  },
  {
    title: 'Billing',
    icon: 'fas fa-file-invoice-dollar',
    active: true,
    access: 'owner',
    path: '/lms/billings',
    select: '/lms/billings',
  },
  {
    title: 'Settings',
    path: '/lms/settings',
    select: '/lms/settings',
    icon: 'fas fa-cog',
    active: true,
  },
  // {
  //   title: 'Management',
  //   path: '/management',
  //   select: '/management',
  //   icon: 'fas fa-cog',
  //   external: true,
  //   active: true,
  // },
] as MenuItem[];
