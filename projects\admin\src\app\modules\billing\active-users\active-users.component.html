<app-resource-loader [source]="source" />
<div class="w-full flex flex-col gap-5 bg-white rounded-xl px-10 pb-10 pt-6">
  <section class="flex justify-between items-center">
    <ul class="flex flex-wrap -mb-px">
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="onTab(1)"
          class="inline-block font-bold text-lg p-4 hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === 1"
          [class.border-b-2]="tab === 1"
          [class.border-lot-blue]="tab === 1"
        >
          Active in Last 30 Days ({{total()}} Seats)
        </a>
      </li>
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="onTab(2)"
          class="inline-block font-bold text-lg p-4 hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === 2"
          [class.border-b-2]="tab === 2"
          [class.border-lot-blue]="tab === 2"
        >
          Inactive
        </a>
      </li>
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="onTab(3)"
          class="inline-block font-bold text-lg p-4 hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === 3"
          [class.border-b-2]="tab === 3"
          [class.border-lot-blue]="tab === 3"
        >
          Never Signed In
        </a>
      </li>
    </ul>
    <div class="flex items-center gap-4">
      <app-floating
        [with]="'fit'"
        [title]="'Sort By'"
        [options]="sortBy"
        (selected)="sort.set($event.id)"
      />
      <div class="flex-1">
        <div class="rounded-md border border-lot-blue flex gap-2 px-5 py-2.5">
          <span class="material-symbols-outlined text-lot-blue">search</span>
          <input
            type="search"
            name="search"
            id="search"
            (input)="search.set($any($event.target).value)"
            class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
            placeholder="Search users"
          />
        </div>
      </div>
    </div>
  </section>
  <app-sub-users [data]="users()" />
</div>
