import { Component, EventEmitter, Output } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { markControlsDirty, samePasswordValidator } from '@lms/core';

export type PasswordData = {
  type: 'CANCEL' | 'SUBMIT';
  data?: {
    // currentPassword: string | null;
    newPassword: string | null;
    confirmPassword: string | null;
  };
};

@Component({
  selector: 'app-password-form',
  imports: [FormsModule, ReactiveFormsModule],
  templateUrl: './password.component.html',
})
export class PasswordFormComponent {
  @Output() data = new EventEmitter<PasswordData>();

  form = new FormGroup({
    // currentPassword: new FormControl('', Validators.required),
    newPassword: new FormControl('', [
      Validators.required,
      Validators.minLength(8),
      this.passwordValidator(),
    ]),
    confirmPassword: new FormControl('', [
      Validators.required,
      samePasswordValidator('newPassword'),
    ]),
  });

  submitted = false;
  showCurrentPassword = false;
  showNewPassword = false;
  showConfirmPassword = false;

  passwordValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      if (!value) {
        return null;
      }

      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const hasSpecialChar = /[@$!%*?&+.]/.test(value);
      const isValidLength = value.length >= 8;

      const errors: ValidationErrors = {};
      if (!hasUpperCase) errors['upperCase'] = true;
      if (!hasLowerCase) errors['lowerCase'] = true;
      if (!hasNumber) errors['number'] = true;
      if (!hasSpecialChar) errors['specialChar'] = true;
      if (!isValidLength) errors['minlength'] = true;

      return Object.keys(errors).length ? errors : null;
    };
  }

  get f() {
    return this.form.controls;
  }

  cancel() {
    this.data.emit({ type: 'CANCEL' });
  }

  submit() {
    this.submitted = true;

    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.data.emit({
      type: 'SUBMIT',
      data: this.form.getRawValue(),
    });
  }

  togglePasswordVisibility(field: string) {
    switch (field) {
      case 'new':
        this.showNewPassword = !this.showNewPassword;
        break;
      case 'confirm':
        this.showConfirmPassword = !this.showConfirmPassword;
        break;
    }
  }

  getPasswordRequirementClass(errorKey: string): string {
    const errors = this.f['newPassword'].errors;

    if(!errors && this.f['newPassword'].valid){
      return 'text-green-500';
    }
    if (!errors || !this.f['newPassword'].dirty) {
      return 'text-gray-400';
    }
    if (errors['required']) {
      return 'text-lot-danger';
    }
    return errors[errorKey] ? 'text-lot-danger' : 'text-green-500';
  }
}
