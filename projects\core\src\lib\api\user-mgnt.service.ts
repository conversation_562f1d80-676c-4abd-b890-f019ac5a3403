// import { HttpHeaders } from '@angular/common/http';
// import { Injectable, inject } from '@angular/core';
// import { lastValueFrom, Observable } from 'rxjs';
// import { map } from 'rxjs/operators';
// import {
//   AccountStatus,
//   ApiRespone,
//   Email,
//   mapResponse,
//   Organization,
//   UserItem,
// } from '../models';
// import { FileStorageService, GlobalStateService } from '../services';
// import { getId, getPagination } from '../utils';

// @Injectable({ providedIn: 'root' })
// export class UserManagementService {
//   state = inject(GlobalStateService);
//   fileService: FileStorageService = inject(FileStorageService);

//   get user(): UserItem {
//     return this.state.user();
//   }

//   get organization(): Organization {
//     return this.state.user().organization as Organization;
//   }

//   table = 'users';
//   selectedUser?: UserItem;

//   async createMany(userInfos: UserItem[]): Promise<{
//     data?: { data?: UserItem; error?: string }[];
//     error?: string;
//   }> {
//     const options = await this.state.getOptions();
//     if (!options) {
//       return {
//         error: 'Invalid Token',
//       };
//     }
//     const sUser = this.user;
//     const res = await lastValueFrom(
//       this.state.http.post<
//         Array<{
//           data?: UserItem;
//           error?: string | null;
//         }>
//       >(
//         `${this.state.envConfig.env.lmsCoreApi}UserItem/create`,
//         {
//           users: userInfos.map((x) => ({
//             ...x,
//             organization: getId(x.organization) ?? getId(sUser?.organization),
//             created_by: sUser?.id,
//             createdName: sUser?.name,
//           })),
//         },
//         options
//       )
//     );

//     return {
//       data: res.map((x) => ({
//         data: x.data,
//         error: x.error ?? undefined,
//       })),
//     };
//   }

//   async createOne(
//     UserItem: UserItem
//   ): Promise<{ UserItem?: UserItem; error?: string }> {
//     const res = await this.createMany([UserItem]);
//     return {
//       UserItem:
//         res?.data?.length && res.data[0].data ? res.data[0].data : undefined,
//       error:
//         res?.data?.length && res.data[0].error ? res.data[0].error : res.error,
//     };
//   }
//   async getAll(
//     payload?: {
//       query?: string;
//       role?: AccountStatus;
//     },
//     paging = {
//       page: 0,
//       size: 10,
//     }
//   ): Promise<ApiRespone<UserItem>> {
//     const { from, to } = getPagination(paging.page, paging.size);
//     let request = this.state.supabase
//       .from('users')
//       .select('*', { count: 'exact' });
//     if (payload?.query?.length) {
//       request.or(
//         `name.ilike.%${payload.query}%,firstname.ilike.%${payload.query}%,lastname.ilike.%${payload.query}%,email.ilike.%${payload.query}%`
//       );
//     }
//     if (payload?.role) {
//       request = request.eq('role', payload.role);
//     }
//     request.neq('id', this.user.id).range(from, to);

//     const res = await request;

//     return {
//       data: (<UserItem[]>res.data ?? []).map((x) => ({
//         ...x,
//         avatar: x.avatar ?? 'https://placebeard.it/640x360',
//         subgroupsId: x.subgroupsId ?? [],
//       })),
//       count: res.count ?? 0,
//       error: res.error?.message,
//     };
//   }

//   async getOne(id?: string): Promise<{ UserItem?: UserItem; error?: string }> {
//     const res = await this.state.supabase
//       .from('users')
//       .select('*, organization(*)')
//       .eq('id', id)
//       .single();
//     if (res.data) {
//       return {
//         UserItem: {
//           ...(<UserItem>res.data),
//           avatar:
//             (<UserItem>res.data).avatar ?? 'https://placebeard.it/640x360',
//         },
//       };
//     }
//     return {
//       error: res.error?.message,
//     };
//   }

//   async save(user: UserItem, file: File | null = null) {
//     if (file) {
//       const url = await this.fileService.uploadFile(file, 'avatars');
//       user.avatar = url;
//     }
//   }

//   sendStudentEmail(
//     payload: Email
//   ): Observable<{ success?: any; error?: any } | undefined> {
//     return this.state.http.post<{ success?: any; error?: any } | undefined>(
//       `${this.state.envConfig.env.apiEnpointConfig.EMAIL}/sendEmail`,
//       payload
//     );
//   }

//   deleteTeam(id: string) {
//     return this.state.http
//       .delete(`${this.state.envConfig.env.apiEnpointConfig.USER_TEAM}/${id}`)
//       .pipe(map((res) => mapResponse(res)));
//   }

//   async activateUser(
//     userId: string,
//     status: boolean
//   ): Promise<{
//     UserItem?: UserItem;
//     error?: string;
//   }> {
//     const options = await this.state.getOptions();
//     if (!options) {
//       return {
//         error: 'Invalid Token',
//       };
//     }
//     const res = await lastValueFrom(
//       this.state.http.put<ApiRespone<UserItem>>(
//         `${this.state.envConfig.env.lmsCoreApi}UserItem/${userId}`,
//         {
//           active: status,
//         },
//         options
//       )
//     );
//     return res.error
//       ? {
//           error: res.error,
//         }
//       : { UserItem: res.data.at(0) };
//   }

//   assignedUser(user: UserItem) {
//     return this.state.http
//       .put(
//         `${this.state.envConfig.env.apiEnpointConfig.SUBSCRIPTION}/assignedUser/${this.user.id}`,
//         user
//       )
//       .pipe(map((res) => mapResponse(res)));
//   }
// }
