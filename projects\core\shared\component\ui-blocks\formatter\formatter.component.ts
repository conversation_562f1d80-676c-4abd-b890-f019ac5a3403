import { Component, inject, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CourseCoreService } from '@lms/core';

@Component({
  selector: 'app-block-formatter',
  imports: [FormsModule, ReactiveFormsModule],
  templateUrl: './formatter.component.html',
})
export class UIFormatterComponent implements OnInit {
  readonly service = inject(CourseCoreService);

  width = {
    S: 25,
    M: 33,
    L: 50,
    XL: 100,
  } as Record<string, number>;

  padding = {
    S: 4,
    M: 10,
    L: 12,
    XL: 20,
  } as Record<string, number>;

  currentColor = '#002747';
  currentBgColor = '#f9f9f9';

  ngOnInit(): void {
    this.currentBgColor =
      this.service.formatterData?.content?.content?.meta?.background ||
      '#f9f9f9';
    this.currentColor =
      this.service.formatterData?.content?.content?.meta?.color || '#002747';
  }

  close() {
    this.service.formatterData?.overlayRef?.dispose();
  }

  update(value: string, type: 'padding' | 'width') {
    let padding = this.service.formatterData?.content?.content?.meta?.padding;
    let width = this.service.formatterData?.content?.content?.meta?.width;
    if (type === 'padding') {
      padding = this.padding[value];
    }
    if (type === 'width') {
      width = this.width[value];
    }
    const content = this.service.formatterData?.content;
    const widget = {
      ...content,
      content: {
        ...content.content,
        meta: {
          ...content.content.meta,
          width: width,
          padding: padding,
        },
      },
    };
    this.service.formatterTrigger.next({
      ...this.service.formatterData!,
      content: widget,
    });
  }

  updateColor(value: string, type: 'bg' | 'text') {

    if (type === 'bg') {
      this.currentBgColor = value;
    }
    if (type === 'text') {
      this.currentColor = value;
    }

    if (
      this.isTooDark(this.currentBgColor) &&
      this.isTooDark(this.currentColor)
    ) {
      this.currentColor = 'white';
    }

    const content = this.service.formatterData?.content;
    const widget = {
      ...content,
      content: {
        ...content.content,
        meta: {
          ...content.content.meta,
          color: this.currentColor,
          background: this.currentBgColor,
        },
      },
    };

    this.service.formatterTrigger.next({
      ...this.service.formatterData!,
      content: widget,
    });
  }

  isTooLight = (color: string) => {
    const hex = color.replace('#', '');
    const c_r = parseInt(hex.substring(0, 0 + 2), 16);
    const c_g = parseInt(hex.substring(2, 2 + 2), 16);
    const c_b = parseInt(hex.substring(4, 4 + 2), 16);
    const brightness = (c_r * 299 + c_g * 587 + c_b * 114) / 1000;
    return brightness > 155;
  };

  isTooDark = (color: string) => {
    const hex = color.replace('#', '');
    const c_r = parseInt(hex.substring(0, 0 + 2), 16);
    const c_g = parseInt(hex.substring(2, 2 + 2), 16);
    const c_b = parseInt(hex.substring(4, 4 + 2), 16);
    const brightness = (c_r * 299 + c_g * 587 + c_b * 114) / 1000;
    return brightness < 155;
  };
}
