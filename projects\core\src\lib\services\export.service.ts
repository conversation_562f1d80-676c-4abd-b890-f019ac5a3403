import { Injectable } from '@angular/core';
import { AngularCsv } from 'angular-csv-ext/dist/Angular-csv';
import { jsPDF } from "jspdf";
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';

@Injectable({providedIn: 'root'})
export class DocExportService {

  organization: { [key: string]: string } = { name: 'lms', logo: 'LMS'};

  exportCSV(fields: string[], data: any) {
    const options = {
      fieldSeparator: ',',
      quoteStrings: '"',
      decimalseparator: '.',
      showLabels: true,
      showTitle: false,
      title: `Shipments Report ${dayjs().format('MM/DD/YYYY')}`,
      useBom: true,
      noDownload: true,
      headers: fields,
      useHeader: false,
      nullToEmptyString: true,
    };

    const csv = new AngularCsv(data, `${this.organization['name']}_report${dayjs().format('YYYY_MM_DD')}.csv`, options);
    const csvData = new Blob([csv.getCsvData()], { type: 'text/csv;charset=utf-8;' });

    const csvURL = window.URL.createObjectURL(csvData);
    const tempLink = document.createElement('a');
    tempLink.href = csvURL;
    tempLink.setAttribute('download', `${this.organization['name']}_report${dayjs().format('YYYY_MM_DD')}.csv`);
    tempLink.click();

  }

  async exportToPDF(htmlEl: HTMLElement) {
    const canvas = await html2canvas(htmlEl);
    const docWidth = 208;
    const docHeight = canvas.height * docWidth / canvas.width;
    const contentDataURL = canvas.toDataURL('image/png')
    const doc = new jsPDF('p', 'mm', 'a4');
    const position = 0;
    doc.addImage(contentDataURL, 'PNG', 0, position, docWidth, docHeight)
    doc.save(`${this.organization['name']}_report${new Date()}.pdf`);
  }

  async exportToPDFWithPaging(htmlEl: HTMLElement) {
    const canvas = await html2canvas(htmlEl);
    const contentDataURL = canvas.toDataURL('image/png')
    const doc = new jsPDF('p', 'mm');
    let position = 0;
    let imgWidth = 210;
    let pageHeight = 295;
    let imgHeight = canvas.height * imgWidth / canvas.width;
    let heightLeft = imgHeight;
    doc.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      doc.addPage();
      doc.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }
    doc.save(`${this.organization['name']}_report${new Date()}.pdf`);
  }
}
