import { Component, Input, Optional } from '@angular/core';
import { AbstractControl, FormGroupDirective } from '@angular/forms';

@Component({
    selector: 'app-validation-text',
    templateUrl: './validation-text.component.html'
})
export class ValidationTextComponent {
  @Input() public controlName: string;
  @Input() public validator = 'required';

  public get control(): AbstractControl | null {
    return this.form?.control?.get(this.controlName);
  }

  public get invalid(): boolean {
    return (
      (this.control?.invalid &&
        (this.control?.dirty || this.control?.touched)) ??
      false
    );
  }

  public get hasError() {
    return this.control?.hasError(this.validator ?? 'required');
  }

  public get message() {
    return this.control?.errors ? this.control?.errors[this.validator]?.message : undefined;
  }

  constructor(@Optional() private form: FormGroupDirective) {
    if (!form) {
      throw new Error('This component should be used within a fromGroup');
    }
  }
}
