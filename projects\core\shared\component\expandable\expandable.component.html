<!-- <div class="w-full max-w-md mx-auto mt-8">
  <button
    (click)="toggle()"
    class="w-full p-4 bg-blue-500 text-white rounded-lg focus:outline-none"
  >
    {{ isExpanded ? 'Collapse' : 'Expand' }}
  </button>
  <div
    @expandCollapse
    [@expandCollapse]="isExpanded ? 'expanded' : 'collapsed'"
    class="mt-2 overflow-hidden"
  >
    <div class="p-4 bg-gray-100 rounded-lg">
      <p>This is the expandable content. It can contain any HTML or components.</p>
      <p>You can add more text, images, or even forms here.</p>
    </div>
  </div>
</div> -->

<div class="w-full">
  <ng-container
    *ngTemplateOutlet="
      headerTemplate;
      context: { $implicit: this, toggle: onToggle }
    "
  >
  </ng-container>
  <div
    @expandCollapse
    [@expandCollapse]="isExpanded ? 'expanded' : 'collapsed'"
    class="mt-2 overflow-hidden"
  >
    <ng-content></ng-content>
  </div>
</div>
