/* You can add global styles to this file, and also import other style files */
@use '@angular/material' as mat;

// Customize the entire app. Change :root to your selector if you want to scope the styles.
:root {
    --lot-dark: #002747;
    --lot-blue: #2e7ddb;
    --lot-gray: #d9d9d9;
    --lot-light-gray: #f9f9f9;
    --lot-white: #ecf6ff;
    --lot-ai: #18cc78;
    --lot-ai-dark: #0ea1ab;
    --lot-dark-gray: #686a6d;
    --lot-light-blue: #9ecaff;
    --lot-gold: #ffac33;
    --lot-warning: #dd6f01;
    --lot-danger: #f95757;
    --lot-disabled: #bac2cc;
  @include mat.tooltip-overrides((
    container-color: var(--lot-light-blue),
    supporting-text-color: var(--lot-dark),
  ));
  @include mat.datepicker-overrides((
    calendar-date-selected-state-text-color: white,
    calendar-date-selected-state-background-color: var(--lot-dark),
    calendar-date-today-selected-state-outline-color: var(--lot-blue),
    calendar-date-in-range-state-background-color: var(--lot-light-blue),
    calendar-date-hover-state-background-color: var(--lot-light-blue),
    range-input-separator-color: var(--lot-dark),
    range-input-disabled-state-text-color: var(--lot-dark-gray),
    calendar-date-disabled-state-text-color: var(--lot-dark-gray),
  ));
}