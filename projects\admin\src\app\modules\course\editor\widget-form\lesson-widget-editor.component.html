<form [formGroup]="form" (ngSubmit)="onSubmit()" class="flex flex-col h-full">
  <div mat-dialog-title>
    <h1 class="text-xl font-bold">Edit {{ widgetType }} Widget</h1>
  </div>
  <mat-dialog-content class="scrollbar flex-grow">
    @if (error) {
    <div class="text-lot-danger text-sm">{{ error }}</div>
    }
    <div class="flex flex-col gap-6 p-4 mt-3">
      @switch (widgetType) { @case ('TEXT') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('PROCESS') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('SORTING') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('INTERACTIVE') {
      <ng-container *ngTemplateOutlet="textWidget" />
      } @case ('IMAGE') {
      <ng-container *ngTemplateOutlet="imageWidget" />
      } @case ('VIDEO') {
      <ng-container *ngTemplateOutlet="videoWidget" />
      } @case ('FLASHCARD') {
      <ng-container *ngTemplateOutlet="flashcardWidget" />
      } @case ('QUIZ') {
      <ng-container *ngTemplateOutlet="quizWidget" />
      } @case ('CARD') {
      <ng-container *ngTemplateOutlet="cardWidget" />
      } @case ('QUOTE') {
      <ng-container *ngTemplateOutlet="quoteWidget" />
      } @case ('LIST') {
      <ng-container *ngTemplateOutlet="listWidget" />
      } @case ('GALLERY') {
      <ng-container *ngTemplateOutlet="galleryWidget" />
      } @case ('RESOURCE') {
      <ng-container *ngTemplateOutlet="resourceWidget" />
      } @case ('DIVIDER') {
      <ng-container *ngTemplateOutlet="dividerWidget" />
      } }
    </div>
  </mat-dialog-content>
  <mat-dialog-actions>
    <div class="flex gap-3 justify-end items-center p-4">
      <button
        (click)="dialogRef.close()"
        type="button"
        class="button-primary-outline w-fit"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="button-primary w-fit py-1.5"
        [disabled]="isLoading"
      >
        @if (isLoading) {
        <span>Saving...</span>
        } @else {
        <span>Save Content</span>
        }
      </button>
    </div>
  </mat-dialog-actions>
</form>

<ng-template #textWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if(widgetContent.heading) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="heading"
          formControlName="heading"
          placeholder="Enter heading"
        />
        <label for="heading">Heading</label>
      </div>
    </div>
    } @if(widgetContent.subHeading){
    <div class="form-lot-input">
      <div class="field">
        <input
          id="subHeading"
          formControlName="subHeading"
          placeholder="Enter sub heading"
        />
        <label for="subHeading">Sub Heading</label>
      </div>
    </div>
    } @if(widgetContent.description){
    <!-- <div class="form-lot-input">
      <div class="field">
        <quill-editor
          formControlName="description"
          [styles]="editorStyle"
          placeholder="Enter description"
        ></quill-editor>
        <label>Description</label>
      </div>
    </div> -->
    <app-rich-text [form]="form" />

    } @if ( widgetContent.columnSize) {
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>Columns</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="flex flex-col gap-4 p-2">
        <div class="form-lot-input">
          <div class="field">
            <select id="columnSize" formControlName="columnSize">
              <option [value]="1">1</option>
              <option [value]="2">2</option>
              <option [value]="3">3</option>
              <option [value]="4">4</option>
            </select>
            <label for="columnSize">Number of Columns</label>
          </div>
        </div>

        <div formArrayName="columns" class="flex flex-col gap-2">
          @for (column of getFormArray('columns').controls; track $index) {
          <div class="flex items-center gap-2 group">
            <div class="form-lot-input flex-grow">
              <div class="field">
                <input
                  [id]="'column-' + $index"
                  [formControlName]="$index"
                  placeholder="Enter column content"
                />
                <label [for]="'column-' + $index"
                  >Column {{ $index + 1 }}</label
                >
              </div>
            </div>
            <a
              href="javascript:void(0)"
              (click)="removeColumn($index)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addColumn()"
          >
            <i class="fa-solid fa-plus"></i> Add Column
          </button>
        </div>
      </div>
    </mat-expansion-panel>
    } @if (widgetContent.table) {
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>Table</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="flex flex-col gap-4 p-2" formGroupName="table">
        <div formArrayName="headers" class="flex flex-col gap-2">
          <h3 class="text-lot-dark font-medium">Headers</h3>

          @for (header of getFormArray('table.headers').controls; track $index)
          {
          <div class="flex items-center gap-2 group">
            <div class="form-lot-input flex-grow">
              <div class="field">
                <input
                  [id]="'header-' + $index"
                  [formControlName]="$index"
                  placeholder="Enter header"
                />
                <label [for]="'header-' + $index"
                  >Header {{ $index + 1 }}</label
                >
              </div>
            </div>

            <a
              href="javascript:void(0)"
              (click)="removeTableHeader($index)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addTableHeader()"
          >
            <i class="fa-solid fa-plus"></i> Add Header
          </button>
        </div>

        <!-- Table Rows -->
        <div formArrayName="rows" class="flex flex-col gap-4 mt-4">
          <h3 class="text-lot-dark font-medium">Rows</h3>

          @for (row of getFormArray('table.rows').controls; track $index; let
          i=$index) {
          <div class="flex flex-col gap-2 p-2 border rounded-md group">
            <div class="flex justify-between items-center">
              <h4 class="text-lot-dark">Row {{ i + 1 }}</h4>

              <a
                href="javascript:void(0)"
                (click)="removeTableRow($index)"
                class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
              >
                <i class="fa-solid fa-trash"></i>
              </a>
            </div>

            <div
              [formArrayName]="i"
              class="grid gap-2"
              [ngStyle]="{
                'grid-template-columns':
                  'repeat(' + getFormArray('table.headers').length + ', 1fr)'
              }"
            >
              @for (cell of getFormForChild(row); track $index; let
              cellIndex=$index) {
              <div class="form-lot-input">
                <div class="field">
                  <input
                    [id]="'cell-' + i + '-' + cellIndex"
                    [formControlName]="cellIndex"
                    placeholder="Enter cell content"
                  />
                  <label [for]="'cell-' + i + '-' + cellIndex">
                    {{
                      getFormArray("table.headers").at(cellIndex).value ||
                        "Cell"
                    }}
                  </label>
                </div>
              </div>
              }
            </div>
          </div>
          }

          <button
            type="button"
            class="button-primary w-fit py-1.5"
            (click)="addTableRow()"
            [disabled]="getFormArray('table.headers').length === 0"
          >
            <i class="fa-solid fa-plus"></i> Add Row
          </button>
        </div>
      </div>
    </mat-expansion-panel>
    }
  </div>
</ng-template>

<ng-template #imageWidget>
  <div [formGroup]="form" class="flex flex-col gap-4 h-full">
    <!-- <div class="form-lot-input">
      <div class="field">
        <select id="fileUrlType" formControlName="fileUrlType">
          <option value="1">Upload Here</option>
          <option value="2">Use Existing Url (your own)</option>
        </select>
        <label for="fileUrlType">Image Type</label>
      </div>
    </div> -->
    @if (+form.get('fileUrlType')?.value === 1) {
    <div class="flex">
      <app-rich-image
        [url]="widgetContent.url"
        (content)="form.get('url')?.setValue($event)"
      />
    </div>
    } @if (+form.get('fileUrlType')?.value === 2) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="url"
          formControlName="url"
          placeholder="Enter image URL"
          required
        />
        <label for="url">Image URL</label>
      </div>
    </div>
    }

    <div class="flex flex-col gap-3">
      <label>Position</label>
      <div class="flex items-center gap-4">
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posCenter"
            name="position"
            formControlName="position"
            value="center"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posCenter">Center</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posFull"
            name="position"
            formControlName="position"
            value="full"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posFull">Full</label>
        </div>
      </div>
    </div>

    <div class="flex flex-col gap-3 my-4">
      <label>Text Position</label>
      <div class="flex items-center gap-4">
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posBottom"
            name="textPosition"
            formControlName="textPosition"
            value="left"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posBottom">Left</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posRight"
            name="textPosition"
            formControlName="textPosition"
            value="right"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posRight">Right</label>
        </div>
        <div class="flex items-start gap-3">
          <input
            type="radio"
            id="posOverlay"
            name="textPosition"
            value="overlay"
            formControlName="textPosition"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label for="posOverlay">Overlay</label>
        </div>
      </div>
    </div>

    <div class="form-lot-input my-4">
      <div class="field">
        <input
          id="caption"
          formControlName="caption"
          placeholder="Enter image caption"
        />
        <label for="caption">Caption</label>
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          id="description"
          formControlName="description"
          placeholder="Enter description"
        />
        <label for="description">Description</label>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #videoWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <select id="fileUrlType" formControlName="fileUrlType">
          <option value="1">Upload Here</option>
          <option value="2">Use Existing Url (your own)</option>
          <option value="3">Embed video (instead of linking)</option>
        </select>
        <label for="fileUrlType">Video Type</label>
      </div>
    </div>

    @if (+form.get('fileUrlType')?.value === 1) {
    <app-file-loader
      type="video"
      [url]="form.get('url')?.value"
      (sendFile)="form.get('file')?.setValue($event[0])"
    />
    } @if ([2, 3].includes(+form.get('fileUrlType')?.value)) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="url"
          formControlName="url"
          placeholder="Enter video URL"
          required
        />
        <label for="url">Video URL</label>
      </div>
    </div>
    }

    <div class="form-lot-input">
      <div class="field">
        <input
          id="videoTitle"
          formControlName="title"
          placeholder="Enter video title"
        />
        <label for="videoTitle">Title</label>
      </div>
    </div>
    <div class="form-lot-input">
      <div class="field">
        <textarea
          name="description"
          id="description"
          formControlName="description"
          placeholder="Enter video description"
        ></textarea>
        <label>Description</label>
      </div>
    </div>
    <div class="flex flex-col gap-5">
      <mat-checkbox formControlName="preventSkip">
        Prevent skipping
      </mat-checkbox>
      <!-- <mat-checkbox formControlName="isEmbedded">
        Embed video (instead of linking)
      </mat-checkbox> -->
    </div>
  </div>
</ng-template>

<ng-template #flashcardWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="flex justify-between">
      <h3>({{ getFormArray("cards").controls.length }}) Cards</h3>
      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addFlashcard()"
      >
        <i class="fa-solid fa-plus"></i> Add Card
      </button>
    </div>

    <div class="flex items-center gap-4">
      <input
        type="checkbox"
        id="requirePass"
        formControlName="requirePass"
        class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
      />
      <label for="requirePass">Require Completion/Click</label>
    </div>
    <div formArrayName="cards">
      @for (card of getFormArray('cards').controls; track $index; let i=$index)
      {
      <app-expandable [headerTemplate]="cardHeader" [isExpanded]="false">
        <ng-template #cardHeader let-context let-toggle="toggle">
          <div class="flex justify-between items-center gap-10">
            <a
              href="javascript:void(0)"
              (click)="toggle(context)"
              class="bg-transparent border-y pt-4 pb-3 outline-none w-full h-fit flex justify-between"
            >
              <span class="text-lot-dark font-medium text-lg">
                Card {{ i + 1 }}
              </span>

              <span class="text-lot-dark font-bold text-3xl">
                <i
                  class="fa-solid fa-chevron-up"
                  [class.fa-chevron-up]="context.isExpanded"
                  [class.fa-chevron-down]="!context.isExpanded"
                ></i>
              </span>
            </a>

            <a
              href="javascript:void(0)"
              (click)="removeFlashcard(i)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
        </ng-template>
        <div class="px-10 py-3 bg-lot-light-blue/5 rounded-lg">
          <div
            [formGroupName]="i"
            class="flex flex-col gap-2 p-4 border rounded-md group"
          >
            <app-expandable [headerTemplate]="frontHeader" [isExpanded]="false">
              <ng-template #frontHeader let-context let-toggle="toggle">
                <a
                  href="javascript:void(0)"
                  (click)="toggle(context)"
                  class="bg-transparent border-y pt-4 pb-3 outline-none h-fit flex justify-between"
                >
                  <span class="text-lot-blue font-semibold text-xl">
                    Front Side
                  </span>

                  <span class="text-lot-dark font-bold text-3xl">
                    <i
                      class="fa-solid fa-chevron-up"
                      [class.fa-chevron-up]="context.isExpanded"
                      [class.fa-chevron-down]="!context.isExpanded"
                    ></i>
                  </span>
                </a>
              </ng-template>
              <div class="px-10 py-3 bg-lot-light-blue/10 rounded-lg">
                <app-widget-card-form
                  [data]="card.get('front')?.value"
                  (save)="saveFlashcardForm(card, $event, 'front')"
                />
              </div>
            </app-expandable>

            <app-expandable [headerTemplate]="backHeader" [isExpanded]="false">
              <ng-template #backHeader let-context let-toggle="toggle">
                <a
                  href="javascript:void(0)"
                  (click)="toggle(context)"
                  class="bg-transparent border-b pb-4 outline-none h-fit flex justify-between"
                >
                  <span class="text-lot-blue font-semibold text-xl">
                    Back Side
                  </span>

                  <span class="text-lot-dark font-bold text-3xl">
                    <i
                      class="fa-solid fa-chevron-up"
                      [class.fa-chevron-up]="context.isExpanded"
                      [class.fa-chevron-down]="!context.isExpanded"
                    ></i>
                  </span>
                </a>
              </ng-template>
              <div class="px-10 py-3 bg-lot-light-blue/10 rounded-lg">
                <app-widget-card-form
                  [data]="card.get('back')?.value"
                  (save)="saveFlashcardForm(card, $event, 'back')"
                />
              </div>
            </app-expandable>
          </div>
        </div>
      </app-expandable>
      }
    </div>
  </div>
</ng-template>

<ng-template #quizWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <input
          id="quizTitle"
          formControlName="title"
          placeholder="Enter quiz title"
        />
        <label for="quizTitle">Assessment Title</label>
      </div>
    </div>

    <div class="grid grid-cols-3 gap-5">
      <div class="flex items-center gap-4">
        <input
          type="checkbox"
          id="showSummary"
          formControlName="showSummary"
          class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
        />
        <label for="showSummary">Can show summary</label>
      </div>
      <div class="flex items-center gap-4">
        <input
          type="checkbox"
          id="canPrevious"
          formControlName="canPrevious"
          class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
        />
        <label for="canPrevious">Prevent Previous</label>
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="passageScore"
            formControlName="passageScore"
            placeholder="Enter Score"
          />
          <label for="passageScore">Score To Pass</label>
        </div>
      </div>
    </div>

    <div class="flex items-center gap-4">
      <input
        type="checkbox"
        id="requirePass"
        formControlName="requirePass"
        class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
      />
      <label for="requirePass">Require Completion</label>
    </div>

    <div formArrayName="questions" class="flex flex-col gap-4">
      <h3 class="text-lot-dark font-medium">Questions</h3>

      @for (question of getFormArray('questions').controls; track $index; let
      questionIndex=$index) {

      <app-expandable [headerTemplate]="fromHeader" [isExpanded]="false">
        <ng-template #fromHeader let-context let-toggle="toggle">
          <div class="flex justify-between items-center gap-10">
            <a
              href="javascript:void(0)"
              (click)="toggle(context)"
              class="bg-transparent border-y pt-4 pb-3 outline-none w-full h-fit flex justify-between"
            >
              <span class="text-lot-dark font-medium text-lg">
                Question {{ questionIndex + 1 }}
              </span>

              <span class="text-lot-dark font-bold text-3xl">
                <i
                  class="fa-solid fa-chevron-up"
                  [class.fa-chevron-up]="context.isExpanded"
                  [class.fa-chevron-down]="!context.isExpanded"
                ></i>
              </span>
            </a>

            <a
              href="javascript:void(0)"
              (click)="removeQuestion(questionIndex)"
              class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
          </div>
        </ng-template>
        <div class="px-10 py-3 bg-lot-light-blue/5 rounded-lg">
          <div
            [formGroupName]="questionIndex"
            class="flex flex-col gap-2 p-4 border rounded-md group"
          >
            <div class="form-lot-input">
              <div class="field">
                <input
                  [id]="'question-title' + questionIndex"
                  placeholder="Enter question"
                  formControlName="title"
                />
                <label [for]="'question-title' + questionIndex">Title</label>
              </div>
            </div>

            <app-rich-text [form]="question" />

            <div class="form-lot-input">
              <div class="field">
                <select
                  [id]="'question-listStyle' + questionIndex"
                  formControlName="type"
                >
                  <option value="checkbox">Multiple Choice</option>
                  <option value="radiobox">Single Choice</option>
                  <option value="blank">Fill in the blank</option>
                </select>
                <label [id]="'question-listStyle' + questionIndex"
                  >Question Type</label
                >
              </div>
            </div>

            <div formArrayName="options" class="flex flex-col gap-4 ml-4">
              <h5 class="text-lot-dark">{{question.get('type')?.value === 'blank' ? 'Answers' : 'Options'}}</h5>
              @for (option of getFormArrayForChild('options',
              question).controls; track $index; let optionIndex=$index ) {
              <div
                [formGroupName]="optionIndex"
                class="flex items-center gap-10 group"
              >
                <div class="flex-grow">
                  <div class="flex items-center gap-8">
                    <div class="form-lot-input flex-grow">
                      <div class="field">
                        <input
                          [id]="'option-' + questionIndex + '-' + optionIndex"
                          formControlName="label"
                          placeholder="Enter option"
                        />
                        <label
                          [for]="'option-' + questionIndex + '-' + optionIndex"
                          > 
                          {{question.get('type')?.value === 'blank' ? 'Answer to match' : 'Option'}} {{ optionIndex + 1 }}
                          </label
                        >
                      </div>
                    </div>
                    @if (question.get('type')?.value !== 'blank') {
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        [id]="
                          'optionCorrect-' + questionIndex + '-' + optionIndex
                        "
                        formControlName="isCorrect"
                        class="mr-2"
                      />
                      <label
                        [for]="
                          'optionCorrect-' + questionIndex + '-' + optionIndex
                        "
                        >Correct</label
                      >
                    </div>
                  }
                  </div>
                </div>
                <a
                  href="javascript:void(0)"
                  (click)="removeOption(questionIndex, optionIndex)"
                  class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
                >
                  <i class="fa-solid fa-trash"></i>
                </a>
              </div>
              }

              <button
                type="button"
                class="button-primary w-fit py-1.5"
                (click)="addOption(questionIndex)"
              >
                <i class="fa-solid fa-plus"></i>
                {{question.get('type')?.value === 'blank' ? 'Add Answer' : 'Add Option'}}                
              </button>
            </div>
          </div>
        </div>
      </app-expandable>

      }

      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addQuestion()"
      >
        <i class="fa-solid fa-plus"></i> Add Question
      </button>
    </div>
  </div>
</ng-template>

<ng-template #cardWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.name) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="name"
          formControlName="name"
          placeholder="Enter card title"
        />
        <label for="cardTitle">Title</label>
      </div>
    </div>
    } @if (widgetContent.description) {
    <div class="form-lot-input">
      <div class="field">
        <textarea
          id="description"
          formControlName="description"
          placeholder="Enter description"
        ></textarea>
        <label for="description">Description</label>
      </div>
    </div>
    }
    <div class="border rounded-lg overflow-hidden shadow-sm h-48">
      <div class="flex relative">
        <app-rich-image
          [url]="widgetContent.image"
          (content)="form.get('image')?.setValue($event)"
        />
      </div>
    </div>
  </div>
</ng-template>

<ng-template #quoteWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <textarea
          id="quoteText"
          formControlName="quote"
          placeholder="Enter quote text"
          rows="4"
          required
        ></textarea>
        <label for="quoteText">Quote</label>
      </div>
    </div>

    @if (widgetContent.author) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="quoteAuthor"
          formControlName="author"
          placeholder="Enter quote author"
        />
        <label for="quoteAuthor">Author</label>
      </div>
    </div>
    } @if (widgetContent.authorImage) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="authorImage"
          type="url"
          formControlName="authorImage"
          placeholder="Enter quote source"
        />
        <label for="authorImage">Author Image url</label>
      </div>
    </div>
    } @if (widgetContent.source) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="quoteSource"
          formControlName="source"
          placeholder="Enter quote source"
        />
        <label for="quoteSource">Source</label>
      </div>
    </div>
    }
    <div class="form-lot-input">
      <div class="field">
        <select id="listStyle" formControlName="style">
          <option value="simple">Simple</option>
          <option value="card">Card</option>
          <option value="bordered">Bordered</option>
          <option value="modern">Modern</option>
        </select>
        <label for="listStyle">View Style</label>
      </div>
    </div>
    <div class="form-lot-input">
      <div class="field">
        <select id="alignment" formControlName="alignment">
          <option value="center">Center</option>
          <option value="left">Left</option>
          <option value="right">Right</option>
        </select>
        <label for="alignment">Algignment Position</label>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #listWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.heading) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="listHeading"
          formControlName="heading"
          placeholder="Enter list heading"
        />
        <label for="listHeading">Heading</label>
      </div>
    </div>
    }

    <div class="form-lot-input">
      <div class="field">
        <select id="listStyle" formControlName="style">
          <option value="disc">Bullet (Disc)</option>
          <option value="circle">Circle</option>
          <option value="number">Numbered</option>
          <option value="checkbox">Checkbox</option>
        </select>
        <label for="listStyle">List Style</label>
      </div>
    </div>

    <div formArrayName="items" class="flex flex-col gap-2">
      <h3 class="text-lot-dark font-medium">List Items</h3>

      @for (item of getFormArray('items').controls; track $index) {
      <div [formGroupName]="$index" class="flex items-center gap-2 group">
        <div class="form-lot-input flex-grow">
          <div class="field">
            <input
              [id]="'listItem-' + $index"
              formControlName="text"
              placeholder="Enter list item"
            />
            <label [for]="'listItem-' + $index">Item {{ $index + 1 }}</label>
          </div>
        </div>

        @if (form.get('style')?.value === 'checkbox') {
        <div class="flex items-center">
          <input
            type="checkbox"
            [id]="'itemChecked-' + $index"
            formControlName="checked"
            class="mr-2"
          />
          <label [for]="'itemChecked-' + $index">Checked</label>
        </div>
        }
        <a
          href="javascript:void(0)"
          (click)="removeListItem($index)"
          class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </div>
      }

      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addListItem()"
      >
        <i class="fa-solid fa-plus"></i> Add Item
      </button>
    </div>
  </div>
</ng-template>

<ng-template #galleryWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    @if (widgetContent.title) {
    <div class="form-lot-input">
      <div class="field">
        <input
          id="galleryTitle"
          formControlName="title"
          placeholder="Enter gallery title"
        />
        <label for="galleryTitle">Title</label>
      </div>
    </div>
    }

    <div class="grid grid-cols-2 gap-10">
      <div class="form-lot-input">
        <div class="field">
          <select id="type" formControlName="type">
            <option value="grid">Grid</option>
            <option value="carousel">Carousel/Slider</option>
          </select>
          <label for="type">Type</label>
        </div>
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            type="number"
            formControlName="columnSize"
            name="columnSize"
            id="columnSize"
            min="1"
            max="4"
          />
          <label for="columnSize">Number of Columns</label>
        </div>
      </div>
    </div>

    <div class="flex items-center gap-4">
      <input
        type="checkbox"
        id="requirePass"
        formControlName="requirePass"
        class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
      />
      <label for="requirePass">Require Completion/Click</label>
    </div>

    <div formArrayName="images" class="flex flex-col gap-4">
      <h3 class="text-lot-dark font-medium">Images</h3>

      @for (image of getFormArray('images').controls; track $index) {
      <div
        [formGroupName]="$index"
        class="flex flex-col gap-2 p-4 border rounded-md group"
      >
        <div class="flex justify-between items-center">
          <h4 class="text-lot-dark">Image {{ $index + 1 }}</h4>
          <a
            href="javascript:void(0)"
            (click)="removeGalleryImage($index)"
            class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
          >
            <i class="fa-solid fa-trash"></i>
          </a>
        </div>

        <div class="flex relative">
          <app-rich-image
            [url]="image.get('url')?.value"
            (content)="image.get('url')?.setValue($event)"
          />
        </div>

        <!-- <div class="form-lot-input">
          <div class="field">
            <input
              [id]="'galleryImageUrl-' + $index"
              formControlName="url"
              placeholder="Enter image URL"
              required
            />
            <label [for]="'galleryImageUrl-' + $index">Image URL</label>
          </div>
        </div> -->

        <div class="form-lot-input">
          <div class="field">
            <input
              [id]="'galleryCaption-' + $index"
              formControlName="caption"
              placeholder="Enter image caption"
            />
            <label [for]="'galleryCaption-' + $index">Caption</label>
          </div>
        </div>
      </div>
      }

      <button
        type="button"
        class="button-primary w-fit py-1.5"
        (click)="addGalleryImage()"
      >
        <i class="fa-solid fa-plus"></i> Add Image
      </button>
    </div>
  </div>
</ng-template>

<ng-template #resourceWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <select id="resourceType" formControlName="type">
          <option value="audio">Audio File</option>
          <option value="file">Document (pdf, doc, etc.)</option>
        </select>
        <label for="resourceType">Resource Type</label>
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <select id="fileUrlType" formControlName="fileUrlType">
          <option value="1">Upload Here</option>
          <option value="2">Use Existing Url (your own)</option>
        </select>
        <label for="fileUrlType">Url Source Type</label>
      </div>
    </div>

    @if (+form.get('fileUrlType')?.value === 1) {
      <div class="form-lot-input">
        <app-file-loader
          [type]="form.get('type')?.value === 'audio' ? 'audio' : 'doc'"
          [url]="form.get('url')?.value"
          (sendFile)="form.get('url')?.setValue($event[0])"
        />
      </div>
    } @if (+form.get('fileUrlType')?.value === 2) {
      <div class="form-lot-input">
        <div class="field">
          <input
            id="url"
            formControlName="url"
            placeholder="Enter resource url"
          />
          <label for="url">File Url</label>
        </div>
      </div>
    }

    <div class="form-lot-input">
      <div class="field">
        <input
          id="resourceTitle"
          formControlName="title"
          placeholder="Enter resource title"
        />
        <label for="resourceTitle">Title</label>
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <quill-editor
          formControlName="description"
          [styles]="editorStyle"
          placeholder="Enter resource description"
        ></quill-editor>
        <label>Description</label>
      </div>
    </div>

    <div class="flex items-center gap-4">
      <input
        type="checkbox"
        id="requirePass"
        formControlName="requirePass"
        class="size-5 border-2 border-lot-dark rounded-md accent-lot-blue cursor-pointer"
      />
      <label for="requirePass">Require Completion/Click</label>
    </div>
  </div>
</ng-template>

<ng-template #dividerWidget>
  <div [formGroup]="form" class="flex flex-col gap-4">
    <div class="form-lot-input">
      <div class="field">
        <select id="dividerStyle" formControlName="style">
          <option value="solid">Solid</option>
          <option value="dashed">Dashed</option>
          <option value="dotted">Dotted</option>
        </select>
        <label for="dividerStyle">Divider Style</label>
      </div>
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          id="dividerThickness"
          type="number"
          formControlName="thickness"
          min="1"
          max="10"
          placeholder="Enter thickness in pixels"
        />
        <label for="dividerThickness">Thickness (px)</label>
      </div>
    </div>
  </div>
</ng-template>
