import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'sortBy',
  standalone: true,
})
export class SortByPipe implements PipeTransform {
  transform(value: any[], by: string): any[] {
    const items =
      by === 'desc'
        ? value.sort((a, b) =>
            new Date(a.created_at ?? '') > new Date(b.created_at ?? '') ? -1 : 1
          )
        : value.sort((a, b) =>
            new Date(a.created_at ?? '') > new Date(b.created_at ?? '') ? 1 : -1
          );
    return items;
  }
}
