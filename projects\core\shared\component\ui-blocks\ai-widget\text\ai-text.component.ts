import { OverlayModule } from '@angular/cdk/overlay';
import { Component, EventEmitter, inject, Input, Output, signal } from '@angular/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { AIService } from '@lms/core';

@Component({
  selector: 'app-text-ai',
  imports: [OverlayModule,MatTooltipModule, MatProgressBarModule],
  templateUrl: './ai-text.component.html',
  styles: [],
})
export class TextAIComponent {
  aiService = inject(AIService);

  @Input() isRichText = false;
  @Output() content = new EventEmitter<string>();

  isOpen = false;
  isLoading = false;
  prompt = signal('');

  async generateAI() {
    let prompt = this.prompt();
    this.isLoading = true;
    prompt = this.isRichText
      ? `Generate only paragraphs about: ${prompt} and respond in plain HTML rich with beautiful styling`
      : `Generate only paragraphs about: ${prompt}`;
    const res = await this.aiService.generateText(prompt, 'html');
    this.isLoading = false;
    this.content.emit(res.data);
    this.isOpen = false;
  }
}
