<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col gap-5 px-12 py-10"
>
  <h1 class="text-2xl font-semibold">Create a New {{ type }}</h1>
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (error) {
  <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
  }
  <div class="form-lot-input">
    <div class="field">
      <select id="type" formControlName="type">
        <option [value]="1">Learning Path</option>
        <option [value]="2">Instructor Led</option>
      </select>
      <label for="type">Select Type</label>
    </div>
  </div>
  <div class="form-lot-input">
    <div class="field">
      <input
        type="text"
        id="title"
        formControlName="title"
        placeholder="How to Make a Good Sale?"
      />
      <label for="title" [class.error]="f['title'].invalid && f['title'].dirty">
        Name your {{ type }}
      </label>
    </div>
    @if(f['title'].errors && f['title'].invalid && f['title'].dirty){
    <div class="error">
      @if (f['title'].errors['required']) {
      <span>{{ type }} name is required</span>
      } @if (f['title'].errors['minlength']) {
      <span>{{ type }} name must be at least 3 characters</span>
      }
    </div>
    }
  </div>

  <app-rich-text [form]="form" label="Give a description" />

  <div class="flex justify-end items-center gap-4">
    <button
      class="button-primary-outline w-fit"
      type="button"
      (click)="dialogRef.close()"
    >
      Cancel
    </button>
    <button class="button-primary w-fit py-1.5" type="submit">
      Start {{ type }} Creation
    </button>
  </div>
</form>
