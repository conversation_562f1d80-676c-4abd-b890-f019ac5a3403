import { Injectable } from '@angular/core';
import { Environment } from './environment';

@Injectable({
  providedIn: 'root',
})
export class ConfigurationLoader {
  private _configuration: Environment;

  get env() {
    return {
      ...this._configuration,
      apiEnpointConfig: {
        USER_INFO: this._configuration.lmsCore<PERSON>pi + 'UserItem',
        ORGANIZATION: this._configuration.lmsCoreApi + 'organization',
        COURSE: this._configuration.lmsCoreApi + 'course',
        MODULE: this._configuration.lmsCoreApi + 'module',
        ROLE_MANAGEMENT: this._configuration.lmsCoreApi + 'role-management',
        LEARNING_PATH: this._configuration.lmsCoreApi + 'learning-path',
        CATEGORY: this._configuration.lmsCoreApi + 'category',
        CURRICULUM: this._configuration.lmsCoreApi + 'curriculum',
        EVENT: this._configuration.lmsCoreApi + 'event',
        USER_ENROLLMENT: this._configuration.lmsCoreApi + 'userEnrollment',
        TOPIC: this._configuration.lmsCoreApi + 'topic',
        QUIZ: this._configuration.lmsCoreApi + 'quizManagement',
        USER_ANSWER: this._configuration.lmsCoreApi + 'user-answer',
        QUESTION: this._configuration.lmsCoreApi + 'quizQuestionOption',
        SUBJECT: this._configuration.lmsCoreApi + 'subject',
        UPLOAD: this._configuration.lmsCoreApi + 'storage',
        USER_TEAM: this._configuration.lmsCoreApi + 'userTeam',
        NOTIFICATION: this._configuration.lmsCoreApi + 'notification',
        ORDER: this._configuration.lmsCoreApi + 'order',
        SUBSCRIPTION: this._configuration.lmsCoreApi + 'subscription',
        REQUESTDEMO: this._configuration.lmsCoreApi + 'requestDemo',
        COURSE_TRACKER: this._configuration.lmsCoreApi + 'course-tracker',
        INVENTORY_ITEMS: this._configuration.lmsCoreApi + 'inventoryItems',
        CERTIFICATE: this._configuration.lmsCoreApi + 'certificate',
        EMAIL: this._configuration.lmsCoreApi + 'emailService',
        IDM: this._configuration.lmsCoreApi + 'idAccessManagement',
        USER_MANAGEMENT: this._configuration.lmsCoreApi + 'userManagement',
        DASHBOARD_MANAGEMENT:
          this._configuration.lmsCoreApi + 'dashboardManagement',
        TRACKING_MANAGEMENT:
          this._configuration.lmsCoreApi + 'trackingManagment',
        POST_MESSAGE: this._configuration.lmsCoreApi + 'postMessage',
      },
    };
  }

  public loadConfiguration() {
    return fetch('env.json')
      .then((response) => {
        return response.json();
      })
      .then((configuration: Environment) => {
        this._configuration = configuration;
        return configuration;
      })
      .catch((error: any) => {
        console.error(error);
      });
  }
}
