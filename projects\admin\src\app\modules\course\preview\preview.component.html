<div class="flex flex-col h-full">
  <app-resource-loader [source]="source" />
  @if (courseSource(); as item) {

  <header class="py-3 md:py-5 bg-lot-dark">
    <div class="mx-auto px-10">
      <nav class="flex items-center justify-between h-14">
        <a href="/" class="w-64 h-8">
          <img
            alt="Company Logo"
            class="w-auto h-full"
            src="assets/images/new/logo-dark.png"
          />
        </a>
        <div class="flex items-center">
          <div class="flex justify-between w-full">
            <div class="flex items-center gap-8">
              <span class="h-6 w-[1px] bg-white"></span>
              @if (source.isLoading()) {
              <span class="text-white">Loading...</span>
              } @if (source.value(); as course) {
              <div class="flex flex-col">
                <h2 class="text-white">
                  {{ item.name || "How to Make a Good Sale!" }}
                </h2>
              </div>
              <a
                href="javascript:void(0)"
                (click)="back()"
                class="bg-transparent hover:bg-lot-light-gray/20 font-semibold py-1 px-2 rounded-md border-2 border-white text-center"
              >
                <img src="assets/images/new/mdi_share.svg" alt="" srcset="" />
              </a>
              <span class="h-6 w-[1px] bg-white"></span>
              <div class="flex flex-col">
                <button (click)="launch(item!)" class="button-primary py-3 px-5">
                  Start Course!
                </button>
                <!-- <app-progress
                  [status]="item.status"
                  [progress]="item.progress"
                />
                <span class="text-white">{{
                  item.progress
                    ? item.progress + "% complete - Keep Going"
                    : "0% complete - Time to Start!"
                }}</span> -->
              </div>
              }
            </div>
          </div>
        </div>
        <div class="flex items-center gap-10">
          <ng-container *ngTemplateOutlet="menu" />
        </div>
      </nav>
    </div>
  </header>

  <div class="w-full flex flex-col h-screen overflow-y-hidden">
    <div
      class="w-full overflow-x-hidden border-t flex flex-col h-full scrollbar"
    >
      <main class="w-full h-full flex-grow px-2">
        <div class="flex justify-between gap-6 mt-2">
          <div class="flex-grow {{ viewChat ? 'w-3/4' : 'w-full' }}">
            @if (view === 'COURSE') {
            <ng-container
              *ngTemplateOutlet="courseView; context: { item: item }"
            />} @if (view === 'MODULE') {
            <ng-container *ngTemplateOutlet="courseModule" />
            } @if (view === 'LESSON') {
            <ng-container *ngTemplateOutlet="courseLecture" />
            } @if (view === 'END') {
            <ng-container *ngTemplateOutlet="courseEnd" />
            }
          </div>
          @if (viewChat) {
          <div class="w-1/4 pr-4">
            <div
              class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
            >
              <ul class="flex flex-wrap">
                @for (item of [1,2]; track $index) {
                <li class="me-2">
                  <a
                    href="javascript:void(0)"
                    (click)="extraTab = item"
                    class="inline-block font-bold text-lg p-2 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
                    [class.text-lot-dark]="extraTab === item"
                    [class.border-lot-blue]="extraTab === item"
                  >
                    {{ item === 1 ? "LearnMate" : "SkillQuest" }}
                  </a>
                </li>
                }
              </ul>
            </div>

            @if (extraTab === 1) {
            <div
              class="bg-gradient-to-r from-lot-ai-dark to-lot-ai rounded-xl p-[2px] mt-6"
            >
              <div
                class="flex flex-col gap-3 h-[554px] px-4 py-5 rounded-[9px] bg-lot-light-gray"
              >
                <div class="flex-grow">
                  <div class="flex flex-col justify-center items-center gap-3">
                    <p class="text-center">Any questions about this course?</p>
                    <div class="flex flex-col items-center">
                      <span class="italic font-semibold">Simply ask the</span>
                      <span class="text-lot-ai-dark text-2xl font-bold"
                        >LearnMate</span
                      >
                      <span class="text-lot-ai-dark text-2xl font-bold"
                        >AI Assistant
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5"
                >
                  <input
                    type="search"
                    name="search"
                    id="search"
                    class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
                    placeholder="Search by Name"
                  />
                  <a
                    href="javascript:void(0)"
                    class="bg-lot-ai-dark text-white p-2 rounded-md"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      class="size-6 -rotate-45"
                    >
                      <path
                        d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            } @if (extraTab === 2) {}
          </div>
          }
        </div>
      </main>
    </div>
  </div>

  } @else {
  <p class="text-center py-20">Unable to preview course</p>
  <a
    href="javascript:void(0)"
    class="text-lot-blue text-center"
    routerLink="/courses"
  >
    Back to Courses
  </a>
  }
</div>

<ng-template #authorView let-author>
  <div class="flex flex-col gap-5">
    <div class="flex gap-5">
      <div
        class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
      >
        <img
          [src]="author.avatar"
          alt=""
          srcset=""
          class="w-full h-full object-cover"
        />
      </div>
      <div>
        <h3 class="font-semibold text-lot-blue text-xl">{{ author.name }}</h3>
        <p class="text-xs text-lot-dark">{{ author.email }}</p>
      </div>
    </div>
    <div>
      <p>
        {{ author.bio }}
      </p>
    </div>
  </div>
</ng-template>

<ng-template #menu>
  <div class="relative">
    <button
      [matMenuTriggerFor]="menu"
      type="button"
      class="p-2 text-white focus:outline-none"
    >
      <span class="material-symbols-outlined text-4xl"> menu </span>
    </button>
    <mat-menu #menu="matMenu" class="max-h-[70vh] overflow-y-auto scrollbar">
      @for (item of navigations(); track $index; let last=$last) {
      <div
        class="border-gray-200 w-[400px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark flex flex-col"
        [class.border-b]="!last"
      >
        @if (item.type === 'EXIT') {
        <a
          class="text-lot-danger font-semibold hover:text-lot-blue flex justify-between items-center gap-2"
          href="javascript:void(0)"
          (click)="gotTo(item)"
        >
          {{ item.name }}
          <i class="fa-solid fa-xmark text-3xl"></i>
        </a>
        } @else {
        <a
          class="flex items-center gap-2"
          href="javascript:void(0)"
          [class.text-lot-ai-dark]="item.type === 'TOP'"
          [class.hover:text-lot-ai-dark]="item.type === 'TOP'"
          [class.hover:text-lot-blue]="item.type !== 'TOP'"
          (click)="gotTo(item)"
        >
          <span class="material-symbols-outlined">{{ item.icon }}</span>
          {{ item.name }}
        </a>
        } @if (item.items?.length) {
        <div
          class="flex flex-col gap-2 ml-2 py-2 px-5 font-[500] text-base leading-10 text-lot-dark"
        >
          @for (subItem of item.items; track $index) {
          <a
            class="hover:text-lot-blue flex items-center gap-2"
            href="javascript:void(0)"
            (click)="gotTo(subItem)"
          >
            <span class="material-symbols-outlined">{{ subItem.icon }}</span>
            {{ subItem.name }}
          </a>
          }
        </div>
        }
      </div>
      }
    </mat-menu>
  </div>
</ng-template>

<ng-template #courseView let-item="item">
  <div class="w-full bg-white rounded-3xl px-7 py-14">
    <div class="flex flex-col gap-5">
      <div
        class="relative w-full h-[480px] opacity-85 transition-all hover:opacity-100 text-white rounded-3xl flex justify-center items-center"
      >
        <img
          src="{{ item.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <!-- <a
          href="javascript:void(0)"
          (click)="launch(item!)"
          class="absolute rounded-3xl inset-0 bg-lot-dark/80 bg-opacity-50 flex flex-col justify-center items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="size-16"
          >
            <path
              fill-rule="evenodd"
              d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
              clip-rule="evenodd"
            />
          </svg>
          Play Video
        </a> -->
      </div>

      <div
        class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
      >
        <ul class="flex flex-wrap -mb-px">
          @for (item of tabs(); track $index) {
          <li class="me-2">
            <a
              href="javascript:void(0)"
              (click)="tab = item.id"
              class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
              [class.text-lot-dark]="tab === item.id"
              [class.border-lot-blue]="tab === item.id"
            >
              {{ item.name }}
            </a>
          </li>
          }
        </ul>
      </div>

      <div class="flex mb-20 px-4">
        @if (tab === 1) {
        <app-ui-html-wrapper
          class="text-justify whitespace-nowrap text-wrap break-all w-full max-w-screen-2xl"
          [content]="item.description!"
        />

        <div class="w-full ml-8 border-l border-lot-gray pl-4">
          @for (item of courseNav(); track $index; let last=$last) {
          <div
            class="border-gray-200 w-[400px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark flex flex-col"
            [class.border-b]="!last"
          >
            <a
              class="flex items-center gap-2 uppercase"
              href="javascript:void(0)"
              [class.text-lot-ai-dark]="item.type === 'TOP'"
              [class.hover:text-lot-ai-dark]="item.type === 'TOP'"
              [class.hover:text-lot-blue]="item.type !== 'TOP'"
              (click)="gotTo(item)"
            >
              <span class="material-symbols-outlined">{{ item.icon }}</span>
              {{ item.name }}
            </a>

            @if (item.items?.length) {
            <div
              class="flex flex-col gap-2 ml-2 py-2 px-5 font-[500] text-base leading-10 text-lot-dark"
            >
              @for (subItem of item.items; track $index) {
              <a
                class="hover:text-lot-blue flex items-center gap-2 border-t"
                href="javascript:void(0)"
                (click)="gotTo(subItem)"
              >
                <span class="material-symbols-outlined">{{
                  subItem.icon
                }}</span>
                {{ subItem.name }}
              </a>
              }
            </div>
            }
          </div>
          }
        </div>

        } @if (tab === 2 ) {
        <ng-container
          *ngTemplateOutlet="authorView; context: { $implicit: item.author }"
        />
        } @if (tab === 3) {
        <div class="flex flex-col gap-1 w-[440px]">
          @for (item of [1,2]; track $index) {
          <div
            class="flex items-center justify-between p-4 border-b border-lot-gray"
          >
            <div class="flex items-center">
              <span
                class="material-symbols-outlined mr-4 border border-lot-dark-gray p-3 rounded-lg"
                >description</span
              >
              <div>
                <p class="font-semibold">File Title {{ item }}</p>
                <p class="text-sm text-gray-500">PDF</p>
              </div>
            </div>
            <button class="button-primary">View</button>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>
</ng-template>

<ng-template #courseLecture>
  <div class="flex flex-col justify-between gap-10 items-center w-full h-full">
    <div class="flex-grow w-full max-w-7xl">
      <div class="flex flex-col justify-center items-center gap-2 w-full p-10">
        @if (lessons.length) {
        <div class="flex border-b py-8">
          <h2 class="text-lot-dark text-4xl font-bold">
            {{ currentLessonIndex + 1 }} - {{ lessons[0].name }}
          </h2>
        </div>
        } @for (item of lectures; track item; let i = $index) {
        <app-dynamic-content
          [data]="item"
          (track)="getTrack($event)"
          class="w-full animate-fade-up animate-delay-900 my-12"
        />
        }
      </div>
    </div>
    <button (click)="next()" class="button-primary w-full py-5 text-lg">
      <span
        >{{ currentLessonIndex + 1 }}/{{ currentModule?.lessons?.length }}</span
      >
      -- Continue
    </button>
  </div>
</ng-template>

<ng-template #courseModule>
  <div class="flex flex-col gap-2 w-full p-10">
    @if (view === 'MODULE' && course && currentModule) {
    <div
      class="flex flex-col justify-center items-center gap-5 max-w-screen-2xl h-[calc(100vh-180px)] animate-fade-up"
    >
      <a
        href="javascript:void(0)"
        class="relative w-full h-full rounded-3xl flex justify-center items-center"
      >
        <img
          src="{{ course.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <div
          class="absolute rounded-3xl inset-0 bg-white/60 bg-opacity-75 transition-opacity hover:bg-transparent hover:opacity-100 flex justify-center items-end px-10"
        >
          <div
            class="flex flex-col gap-3 justify-start items-start bg-lot-dark text-white rounded-md w-full p-10 h-fit mb-10 animate-fade-up animate-delay-900"
          >
            <h2 class="text-4xl font-bold">
              {{ currentModuleIndex + 1 }} - {{ currentModule.name }}
            </h2>
            <div class="w-full border-b pb-2">
              <span class="text-lg font-bold">
                {{ course.name }}
              </span>
            </div>

            <button
              (click)="continue(currentModule)"
              class="button-primary py-3 px-10"
            >
              Continue
            </button>
          </div>
        </div>
      </a>
    </div>
    }
  </div>
</ng-template>

<ng-template #courseEnd>
  <div class="flex flex-col">
    <div class="flex flex-col gap-5">
      <h1>Course Completed</h1>
    </div>
  </div>
</ng-template>
