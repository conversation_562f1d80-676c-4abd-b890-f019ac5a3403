import { Common } from './common.model';

export enum BillFrequency {
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  YEARLY = 'YEARLY',
  YEARLY3 = '3YEAR',
  NONE = 'NONE',
}


export type BillingPlan = Common & {
  name: string;
  description: string;
  frequency: BillFrequency;
  price: number;
  countMin: number;
  countMax: number;
  icon: string;
  status: string;
  priceId?: string;
  pricingNote: string;
  type: 'PLAN' | 'ADDON';
};

export type Promotion = Common & {
  expire_at?: string;
  code?: string;
  content: string;
  button_label: string;
};

export type Invoices = Common & {
  expire_at?: string;
  code?: string;
  content: string;
  button_label: string;
};