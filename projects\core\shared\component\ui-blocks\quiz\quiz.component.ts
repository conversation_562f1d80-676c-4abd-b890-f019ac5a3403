import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { UIBlockCheckComponent } from './check.component';
import { UIBlockData, WidgetType } from '../ui-block.utils';

@Component({
  selector: 'app-block-quiz',
  imports: [UIBlockCheckComponent],
  template: `
    <div class="flex flex-col gap-4 {{ metaClass }}" (click)="selectBlock()">
      @if (!showSummary) { @if (content.title) {
      <div class="flex flex-col gap-2">
        <h3 class="text-xl font-semibold text-lot-dark">{{ content.title }}</h3>
      </div>
      }

      <div class="relative min-h-[300px]">
        @if (currentQuestion) {
        <div class="w-full animate-fade-left animate-delay-100">
          <div class="flex flex-col gap-6">
            <div
              class="flex items-center justify-between text-sm text-lot-dark-gray"
            >
              <span
                >Question {{ currentIndex + 1 }} of
                {{ content.questions.length }}</span
              >
              <div class="flex gap-2">
                @for (_ of content.questions; track i; let i=$index) {
                <div
                  class="w-2 h-2 rounded-full"
                  [class]="
                    i === currentIndex ? 'bg-lot-blue' : 'bg-lot-gray/30'
                  "
                ></div>
                }
              </div>
            </div>

            <app-block-check
              [content]="mapQuestionToCheckContent(currentQuestion)"
              (view)="onQuestionAnswer($event)"
            />
          </div>
        </div>
        }
      </div>

      <div class="flex justify-between mt-4">
        @if (currentIndex > 0 && content.canPrevious) {
        <button
          (click)="previousQuestion()"
          type="button"
          class="px-6 py-2 text-lot-blue hover:bg-lot-blue/10 rounded-lg transition-colors"
        >
          Previous
        </button>
        } @if (hasCurrentQuestionAnswer && !isLastQuestion) {
        <button
          (click)="nextQuestion()"
          type="button"
          class="ml-auto px-6 py-2 bg-lot-blue text-white rounded-lg hover:bg-lot-blue/90 transition-colors"
        >
          Next
        </button>
        } @if (isLastQuestion && isQuizComplete) {
        <button
          (click)="submitQuiz()"
          [disabled]="!isQuizComplete"
          type="button"
          class="ml-auto px-6 py-2 bg-lot-blue text-white rounded-lg hover:bg-lot-blue/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Submit
        </button>
        }
      </div>
      } @else { @if(content.showSummary){
      <div class="flex flex-col gap-6 p-6 bg-white rounded-xl">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold text-lot-dark">Quiz Summary</h3>
          <div class="flex items-center gap-2">
            <span class="text-sm text-lot-dark-gray">Score:</span>
            <span
              class="text-2xl font-bold {{
                scorePercentage >= (content.passageScore || 70)
                  ? 'text-green-600'
                  : 'text-red-600'
              }}"
            >
              {{ scorePercentage }}%
            </span>
          </div>
        </div>

        <div class="flex flex-col gap-4">
          @if (scorePercentage >= (content.passageScore || 70)) {
          <div class="flex items-center gap-2 text-green-600">
            <span class="material-symbols-outlined">check_circle</span>
            <span class="font-semibold"
              >Congratulations! You've passed the quiz.</span
            >
          </div>
          } @else {
          <div class="flex items-center gap-2 text-red-600">
            <span class="material-symbols-outlined">cancel</span>
            <span class="font-semibold"
              >You didn't meet the minimum score requirement.</span
            >
          </div>
          }

          <div class="mt-4">
            <h4 class="font-semibold mb-2">Quiz Statistics:</h4>
            <ul class="flex flex-col gap-2 text-sm text-lot-dark-gray">
              <li class="flex items-center justify-between">
                <span>Total Questions:</span>
                <span class="font-semibold">{{
                  content.questions.length
                }}</span>
              </li>
              <li class="flex items-center justify-between">
                <span>Correct Answers:</span>
                <span class="font-semibold">{{ correctAnswers }}</span>
              </li>
              <li class="flex items-center justify-between">
                <span>Passing Score:</span>
                <span class="font-semibold"
                  >{{ content.passageScore || 70 }}%</span
                >
              </li>
            </ul>
          </div>
        </div>

        <div class="flex justify-end gap-4 mt-4">
          @if (scorePercentage >= (content.passageScore || 70)) {
          <!-- <button
            (click)="submitQuiz()"
            type="button"
            class="px-6 py-2 bg-lot-blue text-white rounded-lg hover:bg-lot-blue/90 transition-colors"
          >
            Exit
          </button> -->
          } @else {
          <button
            (click)="retryQuiz()"
            type="button"
            class="px-6 py-2 border border-lot-blue text-lot-blue rounded-lg hover:bg-lot-blue/10 transition-colors"
          >
            Try Again
          </button>
          }
        </div>
      </div>
      }@else {
      <div class="flex justify-end gap-4 mt-4">
        @if (scorePercentage >= (content.passageScore || 70)) {
        <button
          (click)="submitQuiz()"
          type="button"
          class="px-6 py-2 bg-lot-blue text-white rounded-lg hover:bg-lot-blue/90 transition-colors"
        >
          Continue
        </button>
        }
      </div>
      } }
    </div>
  `,
  // animations: [
  //   trigger('slideAnimation', [
  //     transition('void => next', [
  //       style({ transform: 'translateX(100%)', opacity: 0 }),
  //       animate(
  //         '800ms ease-out',
  //         style({ transform: 'translateX(0)', opacity: 1 })
  //       ),
  //     ]),
  //     transition('void => prev', [
  //       style({ transform: 'translateX(-100%)', opacity: 0 }),
  //       animate(
  //         '800ms ease-out',
  //         style({ transform: 'translateX(0)', opacity: 1 })
  //       ),
  //     ]),
  //   ]),
  // ],
})
export class UIBlockQuizComponent implements OnInit {
  @Input() content!: {
    readyForLecture: boolean;
    title: string;
    questions: {
      id: string;
      title: string;
      description: string;
      type: 'checkbox' | 'radiobox' | 'blank';
      options: {
        id: string;
        label: string;
      }[];
    }[];
    answers: {
      questionId: string;
      answer: string;
    }[];
    canPrevious?: boolean;
    passageScore?: number; // percentage
    requirePass?: boolean; // percentage
    showSummary?: boolean;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  @Output() view = new EventEmitter<UIBlockData>();

  currentIndex = 0;
  slideDirection: 'next' | 'prev' = 'next';
  userAnswers: { [key: string]: any } = {};

  scorePercentage = 0;
  correctAnswers = 0;
  showSummary = false;

  get currentQuestion() {
    return (this.content?.questions || [])[this.currentIndex];
  }

  get isQuizComplete() {
    return (
      //this.action === 'submit' &&
      (this.content?.questions || []).every((q) => !!this.userAnswers[q.id])
    );
  }

  get hasCurrentQuestionAnswer(): boolean {
    return (
      !!this.currentQuestion && !!this.userAnswers[this.currentQuestion.id]
    );
  }

  get isLastQuestion(): boolean {
    return this.currentIndex === this.content.questions.length - 1;
  }

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  ngOnInit(): void {
    // console.log('[QUIZ]', this.content);
    // this.currentQuestion = this.content.questions[this.currentIndex];
    // this.showSummary = this.content.showSummary ?? false;
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit({
      widgetType: WidgetType.QUIZ,
      content: this.content,
      userAnswers: [],
    });
  }

  mapQuestionToCheckContent(question: (typeof this.content.questions)[0]) {
    return {
      questionId: question.id,
      title: question.title,
      description: question.description,
      type: question.type,
      options: question.options,
      meta: this.content.meta,
    };
  }

  onQuestionAnswer(event: { content: any; userAnswers: any }) {
    this.userAnswers[this.currentQuestion!.id] = event.userAnswers;
    // this.nextQuestion();
  }

  nextQuestion() {
    //if (this.currentIndex >= this.content.questions.length - 1) return;
    this.slideDirection = 'next';
    this.currentIndex++;
  }

  previousQuestion() {
    if (this.currentIndex === 0) return;
    this.slideDirection = 'prev';
    this.currentIndex--;
  }

  submitQuiz() {
    if (!this.isQuizComplete) return;

    this.correctAnswers = this.calculateCorrectAnswers();
    this.scorePercentage = Math.round(
      (this.correctAnswers / this.content.questions.length) * 100
    );
    this.userAnswers['summary'] = {
      score: this.scorePercentage,
      passed: this.scorePercentage >= (this.content.passageScore || 70),
    };
    this.showSummary = true;
    this.view.emit({
      widgetType: WidgetType.QUIZ,
      content: this.content,
      userAnswers: this.userAnswers,
    });
  }

  calculateCorrectAnswers(): number {
    let correct = 0;
    for (const question of this.content.questions) {
      const userAnswer = this.userAnswers[question.id];
      const correctAnswer = this.content.answers.find(
        (a) => a.questionId === question.id
      )?.answer;

      const userAnswers = (
        !userAnswer ? [] : Array.isArray(userAnswer) ? userAnswer : [userAnswer]
      ).map((x) => x.toString().toLowerCase().trim());

      const correctAnswers = (
        !correctAnswer
          ? []
          : Array.isArray(correctAnswer)
          ? correctAnswer
          : [correctAnswer]
      ).map((x) => x.toString().toLowerCase().trim());

      const correctSelections = userAnswers.filter((a) =>
        correctAnswers.includes(a)
      );

      const incorrectSelections = userAnswers.filter(
        (a) => !correctAnswers.includes(a)
      );

      const questionScore = Math.max(
        0,
        (correctSelections.length - incorrectSelections.length) /
          correctAnswers.length
      );

      correct += questionScore;
    }
    return Math.round(correct * 10) / 10; // Round to 1 decimal place
  }

  retryQuiz() {
    this.showSummary = false;
    this.currentIndex = 0;
    this.userAnswers = {};
    this.scorePercentage = 0;
    this.correctAnswers = 0;
  }
}

export const getQuizTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Quiz Block',
        title: 'Question with fill in the Blanks',
        questions: [
          {
            id: 'q1',
            title: '🗓️ Vacation & Personal Time',
            description:
              'You should request vacation or personal time off at least ______ business days in advance.',
            type: 'blank',
            options: [{ id: '1', label: '3' }],
          },
        ],
        answers: [
          { questionId: 'q1', answer: ['3'] }, // String, Boolean, Number are valid
        ],
        passageScore: 70,
        showSummary: true,
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      };

    case 1:
      return {
        uiLabel: 'Quiz Block sample 2',
        title: 'Multiple Choice Quiz',
        questions: [
          {
            id: 'q2',
            title: `Which of the following is true about sick leave at Learn Or Teach?`,
            type: 'checkbox',
            options: [
              {
                id: '1',
                label:
                  'You must notify your supervisor at least a week in advance.',
              },
              {
                id: '2',
                label:
                  'You should notify your supervisor as soon as you are sick and then submit your request.',
              },
              {
                id: '3',
                label: 'Sick leave requests are not required to be submitted.',
              },
              {
                id: '4',
                label:
                  'Sick leave must be requested three days before the absence.',
              },
            ],
          },
        ],
        answers: [{ questionId: 'q2', answer: ['2'] }],
        meta: {
          width: 100,
          padding: 8,
          background: 'lot-blue/10',
          color: 'lot-dark',
        },
      };

    case 2:
      return {
        uiLabel: 'Quiz Block sample 2',
        title: 'True or False Question',
        questions: [
          {
            id: 'q0',
            // title:
            //   'You can submit a sick day request on the same day you are absent.',
            description:
              'You can submit a sick day request on the same day you are absent.',
            type: 'radiobox',
            options: [
              { id: '1', label: 'true' },
              { id: '2', label: 'false' },
            ],
          },
        ],
        answers: [
          { questionId: 'q0', answer: '1' }, // false
        ],
        meta: {
          width: 100,
          padding: 8,
          background: 'lot-blue/10',
          color: 'lot-dark',
        },
      };

    default:
      return {
        uiLabel: 'Quiz Block',
        title: 'Multiple Choice Quiz Plus',
        questions: [
          {
            id: 'q2',
            title:
              'Select all the steps you must follow when requesting time off:',
            type: 'checkbox',
            options: [
              {
                id: '1',
                label: 'Notify your manager as soon as possible if you’re sick',
              },
              { id: '2', label: 'Submit your request through the HR Portal' },
              {
                id: '3',
                label: 'Request vacation time at least 3 days in advance',
              },
              { id: '4', label: 'Take time off without any approval' },
              {
                id: '5',
                label:
                  'Discuss unpaid leave with your manager before submitting',
              },
            ],
          },
        ],
        answers: [
          { questionId: 'q2', answer: ['1', '2', '3', '5'] }, 
        ],
        passageScore: 70,
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };
  }
};
