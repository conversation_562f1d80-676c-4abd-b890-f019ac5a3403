DROP VIEW onboarding_progress_view
CREATE VIEW onboarding_progress_view AS
SELECT id, min("invitedDate") as "invitedDate", min("confirmDate") as "confirmDate", min("signedUpDate") as "firstSignedInDate" 
FROM user_audits
GROUP BY id

SELECT * FROM onboarding_progress_view


CREATE VIEW signedIn_users_view AS
SELECT CAST(payload::json->>'actor_id' as varchar)::uuid as id, MIN(created_at) 
FROM auth.audit_log_entries lg inner join public.user_audits au on au.id = CAST(payload::json->>'actor_id' as varchar)::uuid
WHERE 
   CAST(payload::json->>'action' as varchar) LIKE 'login' and au."signedUpDate" is null
GROUP BY CAST(payload::json->>'actor_id' as varchar)::uuid

## ------- NEW VIEW

DROP VIEW onboarding_progress_view
CREATE VIEW onboarding_progress_view AS
SELECT u.id, u.email, u.company, min("invitedDate") as "invitedDate", min("confirmDate") as "confirmDate", min("signedUpDate") as "firstSignedInDate" 
FROM user_audits au inner join public.users u on u.id = au.id
GROUP BY u.id, u.email, u.company