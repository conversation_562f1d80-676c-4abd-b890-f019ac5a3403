<div class="flex flex-col gap-5 px-10 py-10">
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  }

  <div class="flex flex-col">
    <h1 class="text-2xl font-semibold">
      Edit Company: <span class="text-lot-blue italic">{{ data.name }}</span>
    </h1>
  </div>
  <ng-container *ngTemplateOutlet="editUser" />
</div>

<ng-template #editUser>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 w-full"
  >
    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="name"
            type="text"
            formControlName="name"
            name="name"
            autocomplete="new-password"
          />
          <label for="name">Company Name</label>
        </div>
        <app-validation-text controlName="name" />
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="email"
            type="email"
            formControlName="email"
            name="email"
            autocomplete="new-password"
          />
          <label for="email">Business Email Address</label>
        </div>
        <app-validation-text controlName="email" />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="pocName"
            type="text"
            formControlName="pocName"
            name="pocName"
            autocomplete="new-password"
          />
          <label for="pocName">Contact Name</label>
        </div>
        <app-validation-text controlName="pocName" />
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="phone"
            type="tel"
            formControlName="phone"
            name="phone"
            mask="(*************"
            autocomplete="new-password"
          />
          <label for="phone">Contact Phone Number</label>
        </div>
        <app-validation-text controlName="phone" />
      </div>
    </div>

    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="pocTitle"
            type="text"
            formControlName="pocTitle"
            name="pocTitle"
            autocomplete="new-password"
          />
          <label for="pocTitle">Contact Title</label>
        </div>
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            id="nbEmployees"
            type="text"
            formControlName="nbEmployees"
            name="nbEmployees"
            mask="separator.0"
            maxlength="5"
            autocomplete="new-password"
          />
          <label for="nbEmployees">Company No Of Employees</label>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-3 items-center">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="description"
            type="text"
            formControlName="description"
            name="description"
            placeholder="Enter description"
            autocomplete="new-password"
          />
          <label for="description">Company description</label>
        </div>
        <app-validation-text controlName="description" />
      </div>
    </div>
    <div class="form-lot-input">
      <div class="field">
        <input
          id="address"
          type="text"
          formControlName="address"
          name="address"
          placeholder="Enter address"
          autocomplete="new-password"
        />
        <label for="address">Company Address</label>
      </div>
    </div>
    <div class="form-lot-input">
      <div class="field">
        <input
          id="website"
          type="text"
          formControlName="website"
          name="website"
          placeholder="Enter Website"
          autocomplete="new-password"
        />
        <label for="website">Company website</label>
      </div>
    </div>

    <div class="flex justify-end gap-4 mt-10">
      <button class="button-primary-outline w-fit" (click)="dialogRef.close()" type="button">
        Cancel and Close
      </button>
      <button class="button-primary w-32 py-1.5" type="submit">Save</button>
    </div>
  </form>
</ng-template>
