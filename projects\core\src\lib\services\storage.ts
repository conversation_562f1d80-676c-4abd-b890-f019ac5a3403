import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';

export class AppStorage {
  private static ttl = 1000;

  public static remove(key: string) {
    key = `__cache_${key}`;
    sessionStorage.removeItem(key);
  }

  public static set(key: string, value: any) {
    key = `__cache_${key}`;
    AppStorage.remove(key);
    const data = {
      value: value,
      ttl: Date.now() + 20 * 60 * this.ttl,
    };
    sessionStorage.setItem(key, JSON.stringify(data));
  }

  public static get<T>(key: string) {
    key = `__cache_${key}`;
    const content = sessionStorage.getItem(key);
    if (!content) {
      return null;
    }
    const data = JSON.parse(content);
    // if (Date.now() > data.ttl) {
    //   AppStorage.remove(key);
    //   return null;
    // }
    return data.value as T;
  }

  public static cache(
    key: string,
    observer: Observable<any>,
    force: boolean = true
  ): Observable<any> {
    return force || AppStorage.get(key) === null
      ? observer.pipe(map((res) => (AppStorage.set(key, res), res)))
      : of(AppStorage.get(key));
  }

  public static flush() {
    // Object.keys(AppStorage.data).forEach(key => {
    //   key = `__cache_${key}`;
    //   AppStorage.remove(key);
    // });
  }
}
