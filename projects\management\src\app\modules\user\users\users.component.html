<div class="flex flex-col w-full">
  <app-resource-loader [source]="source" />
  <div class="text-sm font-medium text-center text-gray-500">
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="setTab(item)"
          class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
          [class.text-lot-dark]="tab === item.id"
          [class.border-transparent]="tab !== item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>
  <div class="w-full mt-3">
    @if (isLoading) {
    <mat-progress-bar mode="indeterminate" />
    } @if (error) {
    <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
    }
    <app-user-list [total]="totalCount()" [data]="users()" (onItem)="action($event)" />
  </div>
</div>
