import { Routes } from '@angular/router';
import { canActivateOneAuth } from '@lms/core';
import { LayoutComponent } from './core';

export const routes: Routes = [];
export const MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    component: LayoutComponent,
    // canActivate: [canActivateOneAuth],
    // canActivateChild: [canActivateOneAuth],
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./modules/home/<USER>').then((c) => c.HomeComponent),
      },
      {
        path: 'billings',
        loadChildren: () =>
          import('./modules/billing/billing.routes').then((m) => m.routes),
      },
      {
        path: 'reports',
        loadChildren: () =>
          import('./modules/report/report.routes').then((m) => m.routes),
      },
      {
        path: 'users',
        loadChildren: () =>
          import('./modules/user/user.routes').then((m) => m.routes),
      },
      {
        path: 'help',
        loadComponent: () =>
          import('./modules/help/help.component').then((c) => c.HelpComponent),
      },
      {
        path: 'courses',
        loadChildren: () =>
          import('./modules/course/course.routes').then((m) => m.routes),
      },
    ],
  },
  {
    path: '**',
    redirectTo: '/management',
    pathMatch: 'full',
  },
];
