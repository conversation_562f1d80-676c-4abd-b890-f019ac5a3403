import { F } from '@angular/cdk/keycodes';
import { Component, output, Output, signal } from '@angular/core';
import { CatalogFilterEvent, LearningType, TraceStatus } from '@lms/core';

@Component({
  selector: 'lms-catalog-filter',
  templateUrl: './filter.component.html',
  imports: [],
})
export class CatalogFilterComponent {
  selectView = output<CatalogFilterEvent>();

  selectedView: CatalogFilterEvent = {
    view: 'ALL',
    type: LearningType.COURSE,
    venu: 'online',
    mandatory: 'true',
    dueStatus: 'ALL',
  };

  type = signal([
    {
      id: LearningType.COURSE,
      name: 'Courses',
      checked: true,
    },
    {
      id: LearningType.LEARNINGPATH,
      name: 'Learning Paths',
      checked: false,
    },
    {
      id: LearningType.INSTRUCTORLED,
      name: 'Instructor Led',
      checked: false,
    },
    {
      id: LearningType.WEBINAR,
      name: 'Webinars',
      checked: false,
    },
  ]);
  venu = signal([
    {
      id: 'online',
      name: 'Online',
      checked: true,
    },
    {
      id: 'physical',
      name: 'Physical',
      checked: false,
    },
    {
      id: 'hybrid',
      name: 'Hybrid',
      checked: false,
    },
  ]);
  status = signal([
    {
      id: 'mandatory',
      name: 'Mandatory',
      checked: true,
    },
    {
      id: 'optional',
      name: 'Optional',
      checked: true,
    },
  ]);
  refresh = true;

  onView(view: 'ALL' | 'DUE' | 'ASSIGNED' | 'COMPLETED') {
    this.selectedView = { ...this.selectedView, view };
    this.selectView.emit(this.selectedView);
  }

  checkType(type: string, checked: boolean) {
    if (this.type().some((x) => x.id === type)) {
      this.selectedView = {
        ...this.selectedView,
        type: checked ? (type as LearningType) : undefined,
      };
      this.selectView.emit(this.selectedView);
    }
    if (this.venu().some((x) => x.id === type)) {
      this.selectedView = {
        ...this.selectedView,
        venu: checked ? (type as 'online' | 'physical' | 'hybrid') : undefined,
      };
      this.selectView.emit(this.selectedView);
    }
    if (this.status().some((x) => x.id === type)) {
      this.selectedView = {
        ...this.selectedView,
        dueStatus: checked
          ? (type as 'ALL' | 'mandatory' | 'optional')
          : undefined,
      };
      this.selectView.emit(this.selectedView);
    }
  }

  reset() {
    this.selectedView = {
      view: 'ALL',
      type: LearningType.COURSE,
      venu: 'online',
      mandatory: 'true',
      dueStatus: 'ALL',
    };
    this.refresh = false;
    setTimeout(() => {
      this.refresh = !this.refresh;
    }, 10);
  }
}
