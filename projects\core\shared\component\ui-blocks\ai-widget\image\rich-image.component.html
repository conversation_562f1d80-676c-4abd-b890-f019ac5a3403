@if (!currentUrl) {
<div class="flex justify-between items-center">
  <span class="text-lot-dark-gray/50 italic">No image</span>
  <a
    href="javascript:void(0)"
    (click)="openImagePicker()"
    class="text-lot-blue text-xl"
  >
    <i class="fa-solid fa-arrow-up-from-bracket"></i>
  </a>
</div>
} @else {
<div class="flex flex-col gap-5 relative">
  <div class="flex justify-between items-center">
    <span class="text-lot-dark-gray/50 italic">Preview</span>
    <a
      href="javascript:void(0)"
      (click)="onRemove()"
      class="text-lot-danger/70 text-xl"
    >
      <i class="fa-solid fa-trash"></i>
    </a>
  </div>
  <div
    class="rounded-2xl shadow-md border border-gray-200 relative h-full w-full"
  >
    @if (currentUrl) {
    <img
      class="object-contain w-full h-full rounded-2xl"
      [src]="currentUrl"
      alt=""
    />
    }
  </div>
</div>
}

<ng-template #imagePicker>
  <div class="flex flex-col gap-4 px-10 py-10">
    <h1 class="text-lot-blue text-xl font-bold">Upload Image</h1>
    @if (isLoading) {
    <mat-progress-bar mode="indeterminate" />
    } @if (error) {
    <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
    }
    <div class="flex flex-col justify-center items-center gap-2 pb-10">
      @if (showPreview) {
      <div class="flex flex-col gap-5">
        <div class="flex justify-between items-center">
          <span class="text-lot-dark-gray/50 italic">Preview</span>
          <a
            href="javascript:void(0)"
            (click)="reset()"
            class="text-lot-danger/70 text-xl"
          >
            <i class="fa-solid fa-trash"></i>
          </a>
        </div>
        @if (!aiImages.length) {
        <div class="rounded-2xl shadow-md border border-gray-200 h-52">
          <img
            class="object-cover w-full h-full rounded-2xl"
            [src]="currentUrl"
            alt=""
          />
        </div>
        }@else {
        <div class="grid grid-cols-2">
          @for (item of aiImages; track $index) {
          <div class="rounded-2xl shadow-md border border-gray-200 relative">
            <input
              type="checkbox"
              class="absolute top-1 left-1"
              name="image"
              (change)="selectImage($any($event.target).value)"
              [value]="item"
            />
            <img
              class="object-cover w-full h-full rounded-2xl"
              [src]="item"
              alt=""
            />
          </div>
          }
        </div>
        }
      </div>
      } @if (!showPreview) {
      <a
        href="javascript:void(0)"
        class="relative flex justify-center items-center gap-2 border-2 border-dashed border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20 h-12"
      >
        <i class="fa-solid fa-arrow-up-from-bracket text-lot-blue text-xl"></i>
        <span class="text-lot-dark font-medium text-center">
          Drop it here Or click here to upload the image.
        </span>
        <input
          type="file"
          accept="image/*"
          (change)="onFileChange($event)"
          class="absolute inset-0 opacity-0 cursor-pointer"
        />
      </a>
      <span>- Or -</span>
      <div
        class="flex flex-col w-full bg-white rounded-2xl border-gray-200 border gap-2 p-5"
      >
        <div class="flex justify-between">
          <span class="font-semibold">Generate your image with AI</span>
        </div>

        <div class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5">
          <input
            type="search"
            name="prompt"
            id="prompt"
            (input)="prompt.set($any($event.target).value)"
            class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
            placeholder="Enter your prompt here..."
          />
          <a
            href="javascript:void(0)"
            (click)="generateAI()"
            class="bg-lot-ai-dark text-white p-2 rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="size-6 -rotate-45"
            >
              <path
                d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
              />
            </svg>
          </a>
        </div>
      </div>
      }
    </div>

    <div class="flex items-center gap-3 justify-end">
      <button
        class="button-primary-outline w-fit"
        (click)="dialogRef.close()"
        type="button"
      >
        Cancel and Close
      </button>
      <button class="button-primary w-32" type="button" (click)="subscribe()">
        Save Image
      </button>
    </div>
  </div>
</ng-template>
