export const GlobalKeys = {
  // IDM_TOKEN: '__LMS_TOKEN',
  // ID_INFO: '__ID_INFO',
  ORDER_KEY: '__ORDER_ITEM',
  // EXPIRATION_TOKEN: '__expires',
};

export enum GlobalUrls {
  INDEX = '/',
  ADMIN = '/admin',
  STUDENT = '/student',
  LOGOUT = '/logout',
  REGISTRATION = '/registration',
  SUBSCRIPTION = '/registration/subscription',
  SIGNIN = '/login',
  SIGNUP = '/signup',
  FORGOT = '/forgot',
  NEWPASSWORD = '/renew',
}
export enum LayoutRoute {
  INDEX = 'INDEX',
  ADMIN = 'ADMIN',
  STUDENT = 'LEARNER',
  LOGOUT = 'LOGOUT',
  REGISTRATION = 'REGISTRATION',
  LOGIN = 'LOGIN',
}

export enum LessonType {
  VIDEO = 'Video',
  TEXT = 'Text',
  KNOWLEDGE_CHECK = 'Self-Assessment',
  AUDIO = 'Audio',
  ATTACHMENT = 'Attachment',
  EMBEDDED_LINKS = 'Embedded Links',
  SLIDESHOW = 'Slideshow',
  SCENARIO = 'Scenario',
  ACCORDION = 'Accordion',
  MIX = 'MIX',
}
export enum ToastMessageType {
  SUCCESS = 'success',
  ERROR = 'error',
  INFO = 'info',
  WARNING = 'warning',
}
