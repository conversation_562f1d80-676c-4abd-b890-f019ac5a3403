<div class="flex flex-col text-lot-dark pt-10">
  @if (!isDashboard) {
  <a
    [routerLink]="['/lms/reporting']"
    class="text-lot-blue text-sm flex items-center gap-2"
  >
    <span class="material-symbols-outlined"> arrow_back_ios_new </span>
    Back to <span class="text-lot-blue text-2xl"> Reporting Dashboard</span>
  </a>
  }

  <h1 class="text-lot-dark text-2xl font-bold flex items-center gap-2">
    @if (!isDashboard) { Reporting
    <i class="fa-solid fa-chevron-right text-lot-dark text-sm"></i>
    <span class="text-lot-blue text-2xl"> {{ reportName }}</span>
    }@else {
    {{ reportName }}
    }
  </h1>
</div>

<div class="pb-10 w-full">
  <router-outlet></router-outlet>
</div>
