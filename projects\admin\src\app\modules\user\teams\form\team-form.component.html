<div class="flex flex-col gap-5 px-10 py-10 h-full">
  @if (view === 'TEAM') {
  <h1 class="text-2xl font-semibold flex gap-3">
    {{ data.item?.id ? "Edit " : "Create New " }} Team
  </h1>
  <ng-container *ngTemplateOutlet="teamGroup" />
  } @if (['ASSIGN_USER' , 'ASSIGN_TEAM_GROUP'].includes(view)) {
  <app-assign-user-form [data]="assignerInput" (close)="onClose($event)" />
  }@if (view === 'GROUP' && groupInput) {
  <app-group-form [data]="groupInput" (close)="onClose($event)" />
  }
</div>

<ng-template #teamGroup>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 py-5"
  >
    <div class="form-lot-input min-h-32">
      <div class="field">
        <input type="text" id="name" formControlName="name" />
        <label for="name" [class.error]="f['name'].invalid && f['name'].dirty">
          Name your Team
        </label>
      </div>
      @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
      <div class="error">
        @if (f['name'].errors['required']) {
        <span>Team name is required</span>
        }
      </div>
      }
    </div>

    <div class="flex justify-end items-center gap-4">
      <button class="button-primary-outline w-fit" type="submit">
        Save and Close
      </button>
      <button
        class="button-primary w-fit py-1.5"
        type="button"
        (click)="saveGroupNew()"
      >
        Save and Create Group
      </button>
    </div>
  </form>
</ng-template>
