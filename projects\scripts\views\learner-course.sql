

CREATE OR REPLACE FUNCTION get_learner_course(filter json)
RETURNS json SECURITY DEFINER AS $function$
DECLARE 
    data_records record;
    _user_id uuid := (filter->>'userId')::uuid;
    _course_id uuid := (filter->>'courseId')::uuid;
BEGIN

    WITH course_contents as (        
        SELECT 
            m.id,
            m.name,
            m.course,
            COALESCE(
                json_agg(json_build_object('lesson', l.*)) FILTER (WHERE l.id IS NOT NULL),
                '[]'::json
            ) AS lessons
        FROM public.modules m LEFT JOIN public.lessons l ON l.module = m.id
        WHERE m.course = _course_id
        GROUP BY m.id, m.name, m.course ORDER BY m.created_at
    )
    SELECT 
        c.id,
        COALESCE(json_build_object('course', c.*), 'null'::json) as course,
        COALESCE(
            json_agg(json_build_object('modules', cc.*)) FILTER (WHERE cc.id IS NOT NULL),
            '[]'::json
        ) AS modules,
        COALESCE(
            json_agg(json_build_object('tracking', uc.*)) FILTER (WHERE uc.id IS NOT NULL),
            '[]'::json
        ) as tracking
        INTO data_records
    FROM public.courses c 
        LEFT JOIN public.course_enrollments ce ON ce.course = c.id
        LEFT JOIN public.user_trackings uc ON uc.enrollment = ce.id AND uc.user = _user_id
        LEFT JOIN course_contents as cc ON cc.course = c.id
    WHERE c.id = _course_id
    GROUP BY c.id;
    
    RETURN json_build_object(
            'data', data_records
            );
END;
$function$
LANGUAGE plpgsql;
