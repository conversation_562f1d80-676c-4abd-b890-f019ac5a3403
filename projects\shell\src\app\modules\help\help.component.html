<div class="py-2 min-w-full">
  <h1 class="text-primary-200 text-2xl my-3 font-black">Learn or Teach</h1>
  <span class="text-lot-blue text-4xl font-bold">Knowledge Base</span>

  <div *ngIf="!currentPost" class="mt-20 flex flex-col">
    <h3 class="text-gray-500 text-base mb-4">Categories:</h3>
    <div class="flex flex-col gap-5 w-full bg-white p-3 md:p-10">
      <details *ngFor="let item of blogs">
        <summary
          class="font-semibold text-lg bg-lot-blue/25 text-primary-200 border px-10 py-5 w-full cursor-pointer"
        >
          {{ item.name }}
          <span class="text-gray-600 text-base">({{ item.posts.length }})</span>
        </summary>
        <p
          *ngIf="item.posts.length === 0"
          class="px-10 text-base my-2 text-primary-200/80 font-medium"
        >
          no article for this category
        </p>
        <ol class="list-decimal px-10 md:px-20">
          <li
            *ngFor="let post of item.posts"
            (click)="viewPost(post)"
            class="px-2 text-base my-2 text-primary-200/80 font-medium cursor-pointer hover:underline"
          >
            {{ post.title }}
          </li>
        </ol>
      </details>
    </div>
  </div>

  <div *ngIf="currentPost" class="mt-10 flex flex-col gap-5">
    <div class="flex gap-10">
      <a href="javascript:void(0)" (click)="currentPost = null">
        <i class="fas fa-long-arrow-alt-left mr-2 text-2xl"></i>Back
      </a>
      <h3 class="text-gray-700 text-xl">
        {{ currentPost.postCategory[0].name }}
      </h3>
    </div>
    <div class="flex flex-col">
      <h3 class="text-lot-blue/80 text-3xl font-semibold mb-4">
        {{ currentPost.title }}
      </h3>
      <img
        *ngIf="currentPost.coverImage?.url"
        [src]="currentPost.coverImage.url"
        alt="post"
        class="rounded w-full h-auto object-cover"
      />
      <div class="flex justify-between gap-10">
        <div class="flex gap-2">
          <span>last changed:</span>
          <span class="font-medium">{{
            currentPost.updated_at | date : "longDate"
          }}</span>
        </div>
        <div class="flex gap-2">
          <span>Author:</span>
          <span class="font-medium">{{ currentPost.author.name }}</span>
        </div>
      </div>
    </div>
    <div class="mt-2 px-3 text-lg">
      <app-ui-html-wrapper
        [content]="currentPost.content.html"
      ></app-ui-html-wrapper>
    </div>
  </div>
</div>
