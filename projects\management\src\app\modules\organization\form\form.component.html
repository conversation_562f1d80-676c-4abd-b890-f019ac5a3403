<h1 mat-dialog-title>
  {{ data ? "Edit Organization: " + data.name : "Add New Organization" }}
</h1>
<form [formGroup]="form">
  <div mat-dialog-content>
    <div class="flex flex-col gap-5 px-3">
      <div class="w-full flex flex-col">
        <label for="name" class="text-sm font-semibold px-1 mb-2">Name</label>
        <input
          id="name"
          type="text"
          formControlName="name"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="name"></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="description" class="text-sm font-semibold px-1 mb-2"
          >Description</label
        >
        <textarea
          id="description"
          formControlName="description"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        ></textarea>
      </div>

      <mat-divider></mat-divider>

      <div class="w-full flex flex-col">
        <label for="billingName" class="text-sm font-semibold px-1 mb-2"
          >Billing Name</label
        >
        <input
          id="billingName"
          type="text"
          formControlName="billingName"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="billingName"></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="billingEmail" class="text-sm font-semibold px-1 mb-2"
          >Billing Email</label
        >
        <input
          id="billingEmail"
          type="text"
          formControlName="billingEmail"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="billingEmail"></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="billingAddress1" class="text-sm font-semibold px-1 mb-2"
          >Billing Address</label
        >
        <input
          id="billingAddress1"
          type="text"
          formControlName="billingAddress1"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text
          controlName="billingAddress1"
        ></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="billingAddress" class="text-sm font-semibold px-1 mb-2"
          >Billing City</label
        >
        <input
          id="billingCity"
          type="text"
          formControlName="billingCity"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="billingCity"></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="billingZipCode" class="text-sm font-semibold px-1 mb-2"
          >Billing Zip Code</label
        >
        <input
          id="billingZipCode"
          type="text"
          formControlName="billingZipCode"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="billingZipCode"></app-validation-text>
      </div>
      <div class="w-full flex flex-col">
        <label for="billingCountry" class="text-sm font-semibold px-1 mb-2"
          >Billing Country</label
        >
        <input
          id="billingCountry"
          type="text"
          formControlName="billingCountry"
          class="w-full pl-4 pr-3 py-2 rounded-md border-2 border-gray-200 outline-none focus:border-lot-blue"
        />
        <app-validation-text controlName="billingCountry"></app-validation-text>
      </div>
    </div>
  </div>
  <div mat-dialog-actions class="flex gap-4">
    <button
      class="button-secondary w-fit px-3"
      type="button"
      (click)="onSubmit()"
      cdkFocusInitial
    >
      Save
    </button>
    <button class="button-outline w-fit px-3" (click)="dialogRef.close()">
      Cancel
    </button>
  </div>
</form>
