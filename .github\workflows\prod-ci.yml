name: PROD CI and CD

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ release/prod ]
  pull_request:
    branches: [ release/prod ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:

      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs a single command using the runners shell
      - name: Installing packages
        run: npm i --legacy-peer-deps

      # Runs a set of commands using the runners shell
      - name: Run a build
        run: npm run ci

      - name: Detele Config File - App
        uses: JesseTG/rm@v1.0.2
        with:
          path: dist/shell/browser/env.json

      # Copy Verkademy Configuration
      - name: Re-create Config File - shell App
        id: create-shell-json-vkshell
        uses: jsdaniell/create-json@1.1.2
        with:
          name: "env.json"
          json: ${{ secrets.ENV_PROD_CONFIG_JSON }}
          dir: 'dist/shell/browser/'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      # Copy dist file to Verkademy S3
      # Runs a set of commands using the runners shell
      - name: Copy Files to build to S3
        run: aws s3 sync ./dist/shell/browser s3://app-learnorteach-prod

      # Copy dist file to LearnOrTeach S3
      # Runs a set of commands using the runners shell
      # - name: Copy Files to LMS shell App
      #   run: aws s3 sync ./dist/shell/browser s3://app.learnorteach.com

      - name: Invalidate CloudFront LMS APP
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.LMS_PROD_DISTRIBUTION }}
          PATHS: "/*"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2
