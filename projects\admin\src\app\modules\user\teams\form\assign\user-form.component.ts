import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  resource,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import {
  getId,
  GroupItem,
  ITeamGroupItem,
  Team,
  TeamCoreService,
  ToastMessageType,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { debounceTime, startWith, switchMap } from 'rxjs';
import { TeamFormView } from '../team-form.component';

@Component({
  selector: 'app-assign-user-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatDialogModule,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
  ],
  templateUrl: './user-form.component.html',
})
export class AssignUserFormComponent implements OnInit {
  readonly service = inject(TeamCoreService);
  userService = inject(UsersCoreService);

  @Input() data: {
    item: GroupItem;
    user: UserItem;
    isTeam: boolean;
    type: TeamFormView;
  };

  @Output() close = new EventEmitter<{
    action: 'BACK' | 'SAVE' | 'CANCEL';
  }>();

  isLoading = false;
  error?: string;

  userControl = new FormControl('');
  userOptions = toSignal(
    this.userControl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) =>
        this.userService.getUserOnlyByQuery(value || '').then((res) =>
          res.data
            .map(
              (x) =>
                ({
                  userGroupId: '',
                  userId: x.id,
                  teamId: this.data.isTeam
                    ? this.data.item?.id
                    : getId(this.data.item?.team),
                  teamName: this.data.item?.name,
                  groupId: !this.data.isTeam ? this.data.item?.id : undefined,
                  groupName: this.data.item?.name,
                  userName: x.name.includes('undefined') ? x.email : x.name,
                } as ITeamGroupItem)
            )
            .filter(
              (user) => !this.users.some((ex) => ex.userId === user.userId)
            )
        )
      )
    )
  );

  displayFn = (item: any) => item?.name || '';
  teamSource = resource({
    loader: () => this.service.queryTeams(),
  });

  userTeams: ITeamGroupItem[] = [];
  userGroups: ITeamGroupItem[] = [];
  users: (ITeamGroupItem & { tag: string })[] = [];
  teamGroupToDelete: ITeamGroupItem[] = [];

  groupOptions: GroupItem[] = [];

  ngOnInit(): void {
    if (!this.data?.item?.id) return;
    this.service
      .getUsersAssignedByType(this.data.item.id, this.data.isTeam!)
      .then((res) => {
        this.userGroups = res.data;
      });
  }

  async onSubmitAssignTeamGroup() {
    this.error = undefined;
    if (this.isLoading) return;

    this.isLoading = true;
    const myGroups = this.userGroups.map((group) => {
      const item = {
        user: getId(this.data.user),
        organization: getId(this.service.organization),
        team: group.teamId,
        group: group.groupId,
      } as any;
      return item;
    });

    const myTeams = this.userTeams.filter(
      (x) => !myGroups.find((y) => y.team === x.teamId)
    );

    myGroups.push(
      ...myTeams.map((team) => {
        const item = {
          user: getId(this.data.user),
          organization: getId(this.service.organization),
          team: team.teamId,
        } as any;
        return item;
      })
    );

    if (!myGroups.length) {
      this.isLoading = false;
      this.close.emit({ action: 'CANCEL' });
      return;
    }

    const res2 = await this.service.assignUsers({
      news: myGroups,
      olds: [],
    });
    this.isLoading = false;

    if (res2.length) {
      this.error = res2.join(', ');
      return;
    }
    this.close.emit({ action: 'SAVE' });
  }

  selectUser(item: ITeamGroupItem, action: 'ADD' | 'REMOVE' | 'CLEAR') {
    if (
      action === 'ADD' &&
      !this.userGroups.some((x) => x.userId === item.userId)
    ) {
      this.users.push({ ...item, tag: 'new' });
    }
    if (action === 'REMOVE') {
      this.userGroups = this.userGroups.filter((x) => x.userId !== item.userId);
      this.users.push({ ...item, tag: 'delete' });
    }
    if (action === 'CLEAR') {
      this.users = this.users.filter((x) => x.userId !== item.userId);
    }
  }

  async bulkAssignUsers() {
    this.error = undefined;
    if (this.isLoading || !this.users.length) return;

    this.isLoading = true;
    const newItems = this.users
      .filter((x) => x.tag === 'new')
      .map((u) => {
        const item = {
          user: u.userId,
          organization: getId(this.service.organization),
          team: u.teamId,
          group: u.groupId,
        } as any;
        return item;
      });

    const itemToDelete = [
      ...this.users
        .filter((x) => x.tag === 'delete')
        .map((x) => x.userGroupId)
        .filter(Boolean),
      ...this.teamGroupToDelete.map((x) => x.userGroupId).filter(Boolean),
    ].filter(Boolean).filter((v, i, _) => _.indexOf(v) === i);

    const res2 = await this.service.assignUsers({
      news: newItems,
      olds: itemToDelete,
    });
    this.isLoading = false;

    if (res2.length) {
      this.error = res2.join(', ');
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res2.join(', '),
        type: ToastMessageType.ERROR,
      });
      return;
    }

    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Team saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.close.emit({ action: 'SAVE' });
  }

  onTeam(id: string, teams: Team[], index: number) {
    const team = teams.filter((x) => x.id === id)[0];
    this.groupOptions = (team.groups || []).map(
      (x) => ({ ...x, team: { id } } as GroupItem)
    );

    if (this.userTeams.some((x) => x.teamId === team.id)) return;

    this.userTeams.push({
      teamId: team.id,
      teamName: team.name,
    } as any);
    // if (index > -1) {
    //   this.groups.controls[index].get('group')?.enable();
    // }
  }

  onGroup(ids: string[], groups: GroupItem[]) {
    if (!ids.length) return;
    const selectedGroups = groups.filter((x) => ids.includes(x.id));

    const newGroups = selectedGroups.filter(
      (group) => !this.userGroups.some((x) => x.groupId === group.id)
    );

    this.userGroups.push(
      ...newGroups.map(
        (group) =>
          ({
            teamId: getId(group.team),
            groupId: group.id,
            groupName: group.name,
          } as any)
      )
    );
  }

  removeChips(id: string, type: 'TEAM' | 'GROUP') {
    if (type === 'TEAM') {
      const teamIndex = this.userTeams.findIndex((x) => x.teamId === id);
      if (teamIndex > -1) {
        this.teamGroupToDelete.push(
          this.userTeams.filter((x) => x.teamId === id)[0]
        );
        this.userTeams.splice(teamIndex, 1);
      }
    } else if (type === 'GROUP') {
      const groupIndex = this.userGroups.findIndex((x) => x.groupId === id);
      if (groupIndex > -1) {
        this.teamGroupToDelete.push(
          this.userTeams.filter((x) => x.teamId === id)[0]
        );
        this.userGroups.splice(groupIndex, 1);
      }
    }
  }
}
