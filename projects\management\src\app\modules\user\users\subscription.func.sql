CREATE OR REPLACE FUNCTION get_my_info(filter json)
RETURNS json AS $function$
DECLARE 
    user_record record;
    count_record int;
BEGIN

        SELECT  count(*) into count_record
        FROM public.users
        WHERE organization::text = filter->>'organization'::text;

        SELECT array_agg(
            json_build_object(
                'user',ur.user,
                'subscription', ur.sub,
                'organization', ur.org,
                'plan', ur.plan
                )
        ) INTO user_record
        FROM (
            select 
                to_jsonb(u.*) as user, 
                to_jsonb(org.*) as org, 
                to_jsonb(sub.*) as sub, 
                to_jsonb(pl.*) as plan
            from public.users u
                inner join public.organizations org on org.id = u.organization
                inner join public.org_subscription sub on sub.organization = org.id and (sub.status = 'ACTIVE' or sub.current = 1)
                inner join public.billing_plans pl on pl.id = sub.plan
            where u.id::text = filter->>'userId'::text
        ) ur;

    RETURN json_build_object(
        'used_seat_count', count_record,
        'data', user_record
    );
END;
$function$
LANGUAGE plpgsql;
