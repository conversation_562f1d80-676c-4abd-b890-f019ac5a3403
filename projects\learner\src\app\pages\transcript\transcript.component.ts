import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import dayjs from 'dayjs';

@Component({
  selector: 'app-transcript',
  imports: [DatePipe],
  templateUrl: './transcript.component.html',
})
export class TranscriptComponent {
  certificates = Array.from({ length: 5 }).map((_, i) => ({
    id: '1',
    name: 'Certificate of Completion',
    created_at: dayjs()
      .add(Math.floor(Math.random() * 25), 'day')
      .add(Math.floor(Math.random() * 11), 'month')
      .toDate(),
    expiredDate: dayjs()
      .add(Math.floor(Math.random() * 2), 'year')
      .toDate(),
  }));

  courses = Array.from({ length: 6 }).map((_, i) => ({
    id: '1',
    name: 'Certificate of Completion',
    status: ['PAST', 'DUE', 'IN_PROGRESS', 'COMPLETED'][
      Math.floor(Math.random() * 3)
    ] as 'PAST' | 'DUE' | 'IN_PROGRESS' | 'COMPLETED',
    score: Math.floor(Math.random() * 101),
    created_at: dayjs()
      .add(Math.floor(Math.random() * 25), 'day')
      .add(Math.floor(Math.random() * 11), 'month')
      .toDate(),
    completionDate: dayjs()
      .add(Math.floor(Math.random() * 2), 'year')
      .toDate(),
  }));
}
