export const CoursePlayQuery = `
query getCourseOnly($id: UUID!) {
  coursesCollection(offset: 0, filter: {
    id: {
      eq: $id
    }
  }){
    edges {
      node {
        id
        name
        short
        description
        cover
        venue
        duration
        creator
        organization
        created_by
        created_at
        updated_by
        updated_at
        modulesCollection {
          edges {
            node {
              id
              name
              position
              description
              order
              course
              lessonsCollection {
                edges{
                  node {
                    id
                    name
                    contents
                    module
                    order
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
`;

