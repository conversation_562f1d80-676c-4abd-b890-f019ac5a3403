-- FUNCTION: public.handle_new_user_audit()

-- DROP FUNCTION IF EXISTS public.handle_new_user_audit();

CREATE OR REPLACE FUNCTION public.handle_new_user_audit()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF SECURITY DEFINER
AS $BODY$

declare
   userId uuid;
  BEGIN

    IF new.payload::json->>'action'::varchar = 'login' THEN
      insert into public.user_audits (id, email, "created_by")
      values (CAST(new.payload::json->>'actor_id'::varchar AS uuid), new.payload::json->>'actor_username'::varchar, CAST(new.payload::json->>'actor_id'::varchar AS uuid));
    END IF;

    RETURN new;
  END;
$BODY$;

ALTER FUNCTION public.handle_new_user_audit()
    OWNER TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_new_user_audit() TO PUBLIC;

GRANT EXECUTE ON FUNCTION public.handle_new_user_audit() TO anon;

GRANT EXECUTE ON FUNCTION public.handle_new_user_audit() TO authenticated;

GRANT EXECUTE ON FUNCTION public.handle_new_user_audit() TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_new_user_audit() TO service_role;

