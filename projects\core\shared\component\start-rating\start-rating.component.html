<div class="flex flex-row items-center text-lot-gold" [class.gap-0]="readOnly">
  @for (start of stars; track $index; let i = $index) { @if (start) {
  <a
    href="javascript:void(0)"
    (click)="setStar(i)"
    [class.pointer-events-none]="readOnly"
    [class.text-lg]="size === 'sm'"
    [class.text-xl]="size === 'md'"
    [class.text-4xl]="size === 'lg'"
    [class.text-6xl]="size === 'xl'"
  >
    <mat-icon
      fontIcon="star"
      inline="true"
      [class.material-symbols-outlined]="!start"
    />
  </a>
  } @if (!start) {
  <a
    href="javascript:void(0)"
    (click)="setStar(i)"
    [class.pointer-events-none]="readOnly"
    [class.text-lg]="size === 'sm'"
    [class.text-2xl]="size === 'md'"
    [class.text-5xl]="size === 'lg'"
    [class.text-7xl]="size === 'xl'"
  >
    <mat-icon
      fontIcon="star"
      inline="true"
      [class.material-symbols-outlined]="!start"
    />
  </a>
  } }
</div>
