<a
  href="javascript:void(0)"
  routerLink="/sign-in"
  class="absolute top-5 left-5 text-lot-blue text-lg flex items-center gap-2"
>
  <span class="material-symbols-outlined"> arrow_back_ios_new </span>
  Back to Login
</a>
<div class="text-center mb-10 ml-6 text-lot-dark">
  @if (isSignMode === 1) {
  <h3 class="font-bold text-2xl text-center text-lot-blue">
    Verify your Account
  </h3>
  } @if (isSignMode === 2) {
  <h3 class="font-bold text-2xl text-center text-lot-blue">
    Reset your Password
  </h3>
  }
  <p class="mt-4 text-center font-semibold text-base">
    Thank you for getting back to {{ organization }}.
  </p>
</div>
<form [formGroup]="form" (ngSubmit)="submit()" class="mt-2 space-y-5 text-lot-dark">
  @if (message) {
  <p class="text-sm text-center font-semibold text-red-600 my-3">{{ message }}</p>
  }

  <div class="flex flex-col gap-3">
    @if (isSignMode === 1) {
    <p class="mb-5">
      Please confirm your email by entering the verification code sent to
      <span class="font-semibold text-primary-200">{{ emailMasked }}</span
      >. If you don't see the email, please check your spam folder.
    </p>
    } @if (isSignMode === 2) {
    <p class="mb-5 flex items-center gap-5 border border-lot-danger p-3 rounded-md">
      <span class="text-red-600 text-3xl">
       <i class="fa-solid fa-circle-info"></i>
      </span>
      <span cl>
        Please enter a valid email or if you do not receive any email, please
        reach out to your administrator.
      </span>
    </p>
    } @if (isSignMode === 4) {
    <p class="mb-5 flex items-center gap-5 border border-lot-danger p-3 rounded-md">
      <span class="text-red-600 text-3xl">
       <i class="fa-solid fa-circle-info"></i>
      </span>
      <span
        >We could not find your email address in our system. Please reach out to
        your administrator.</span
      >
    </p>
    } @if (isSignMode === 2) {
    <div class="flex mx-3">
      <div class="form-lot-input mb-5">
        <div class="field">
          <input
            id="email"
            type="email"
            formControlName="email"
            autocomplete="on"
            placeholder="<EMAIL>"
          />
          <label
            for="email"
            [class.error]="
              form.controls['email'].invalid && form.controls['email'].dirty
            "
          >
            Email
          </label>
        </div>
        <app-validation-text controlName="email" />
        <app-validation-text controlName="email" validator="email">
          Invalid Email.
        </app-validation-text>
      </div>
    </div>
    } @if (isSignMode === 1) {
    <div class="flex justify-between items-center gap-2 mb-5">
      <div class="form-lot-input w-2/3">
        <div class="field">
          <input
            id="code"
            type="number"
            name="code"
            mask="0*"
            (input)="onInput($any($event.target))"
            formControlName="code"
            autocomplete="code"
          />
          <label
            for="code"
            [class.error]="
              form.controls['code'].invalid && form.controls['code'].dirty
            "
          >
            Verification Code
          </label>
        </div>
        <app-validation-text controlName="code" />
      </div>
    <button class="button-primary-outline w-fit px-2" type="button"
        (click)="resendCode()">
      Resend Code
    </button>
    </div>
    }
  </div>

  @if (isSignMode !== 4) {
  <div class="flex flex-col justify-center items-center ml-3">
    @if (isSignMode !== 3) {
    <button class="button-primary w-fit py-1.5 px-6" type="submit">
      {{ isSignMode === 1 ? "Verify" : "Send Code" }}
    </button>
    } @if (isSignMode ===3) {
    <button
      class="button-primary w-fit py-1.5 px-6"
      type="button"
      (click)="resendCode()"
    >
      Resend Code
    </button>
    }
  </div>
  }
</form>
