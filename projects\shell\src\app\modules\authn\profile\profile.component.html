<div class="flex flex-col gap-5">
  <div class="text-center">
    <h1 class="font-bold text-center text-2xl text-lot-blue px-5">
      We want to know a little more about you.
    </h1>
  </div>
  @if (message) {
  <p class="text-sm font-semibold text-accent text-center">
    {{ message }}
  </p>
  }
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-3 mt-3"
  >
    <div class="form-lot-input">
      <div class="field">
        <input
          id="firstname"
          type="text"
          formControlName="firstname"
          autocomplete="new-password"
        />
        <label
          for="firstname"
          [class.error]="
            form.controls['firstname'].invalid &&
            form.controls['firstname'].dirty
          "
          >What's your First name?</label
        >
      </div>
      <app-validation-text controlName="firstname" />
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          id="lastname"
          type="text"
          name="lastname"
          formControlName="lastname"
          autocomplete="lastname"
        />
        <label
          for="lastname"
          [class.error]="
            form.controls['lastname'].invalid && form.controls['lastname'].dirty
          "
        >
          And your Last name
        </label>
      </div>
      <app-validation-text controlName="lastname" />
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          id="phone"
          type="text"
          name="phone"
          mask="(*************"
          formControlName="phone"
          autocomplete="phone"
        />
        <label
          for="phone"
          [class.error]="
            form.controls['phone'].invalid && form.controls['phone'].dirty
          "
        >
          What's your Phone number?
        </label>
      </div>
      <app-validation-text controlName="phone" />
    </div>

    <div class="form-lot-input">
      <div class="field">
        <input
          id="title"
          type="text"
          name="title"
          formControlName="title"
          autocomplete="title"
        />
        <label
          for="title"
          [class.error]="
            form.controls['title'].invalid && form.controls['title'].dirty
          "
        >
          and your Job Title
        </label>
      </div>
      <app-validation-text controlName="title" />
    </div>

    <div class="flex flex-col justify-center items-center my-5">
      <button type="submit" class="button-primary w-fit py-1.5 px-6">
        Submit
      </button>
    </div>
  </form>
</div>
