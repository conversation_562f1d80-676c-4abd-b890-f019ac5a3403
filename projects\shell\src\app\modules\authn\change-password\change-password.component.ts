import { Component, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService, UserItem, UsersCoreService } from '@lms/core';
import { PasswordData, PasswordFormComponent } from '@lms/shared';
import { ChangeProfileComponent } from '../profile/profile.component';

@Component({
  selector: 'app-change-sign',
  imports: [RouterLink, PasswordFormComponent, ChangeProfileComponent],
  templateUrl: './change-password.component.html',
})
export class ChangePasswordComponent {
  private authService = inject(AuthService);
  service = inject(UsersCoreService);
  private router = inject(Router);

  message?: string;
  view: 'PASSWORD' | 'VERIFY' = 'PASSWORD';
  user?: UserItem;

  get userId() {
    return this.service.user.id ?? this.user?.id;
  }

  async onSubmitPassword(data: PasswordData) {
    this.message = undefined;
    if (!data?.data?.newPassword) {
      this.message = 'Invalid password';
      return;
    }
    try {
      this.authService.isLoading = true;
      const res = await this.authService.resetPassword(data.data.newPassword);
      this.authService.isLoading = false;
      if (res?.error) {
        this.message = res.error.message;
        return;
      }
      this.message = undefined;
      this.user = res?.data?.user as any as UserItem;
      if (!this.service.user.lastname || !this.service.user.lastname) {
        this.view = 'VERIFY';
      } else {
        this.router.navigate(['/']);
      }
    } catch (error: any) {
      this.message = error?.message;
    } finally {
      this.authService.isLoading = false;
    }
  }

  async onSubmitProfile(value: number) {
    if (!value) return;
    this.router.navigate(['/']);
  }

  gotLogin() {
    this.router.navigate(['/sign-in']);
  }
}
