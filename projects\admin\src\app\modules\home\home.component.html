<div class="flex flex-col w-full pt-12">
  <header class="h-20">
    <h1 class="text-lot-dark text-2xl font-bold">Home</h1>
  </header>
  <div class="flex-grow h-full">
    <div class="flex flex-col gap-12 w-full h-full">
      @if(subscription.plan.name === "TRIAL" || isExpiringSoon){
      <div
        class="flex justify-between items-center bg-lot-dark text-white py-4 px-10 rounded-xl"
      >
        <div class="flex items-center gap-3">
          <i class="fa-solid fa-stopwatch"></i>
          <p>
            {{ dayCount }} days remaining in your
            {{
              subscription.plan.name === "TRIAL"
                ? "free trial"
                : subscription.plan.name + " Plan"
            }}
          </p>
        </div>
        <a
          href="javascript:void(0)"
          (click)="subscribe()"
          class="underline italic font-semibold"
        >
          {{
            subscription.plan.name === "TRIAL"
              ? "Subscribe to a Paid Plan"
              : "Extend Your Plan"
          }}
        </a>
      </div>
      }
      <!-- <div class="flex flex-col gap-2">
        <div class="flex gap-10 items-center w-full">
          <span class="text-lot-blue font-semibold"
            >Learn or Teach Setup is 20% complete</span
          >
          <div class="w-[464px]">
            <app-progress status="IN_PROGRESS" [progress]="30" />
          </div>
        </div>
        <p class="text-lot-dark-gray text-sm">
          Complete the following steps to ensure you make the best of your Learn
          or Teach trial
        </p>
      </div> -->

      <div class="flex gap-8">
        <!-- <a
          href="javascript:void(0)"
          (click)="action(1)"
          class="w-64 flex flex-col items-center gap-2 border p-10 rounded-xl hover:bg-lot-blue/20"
          [class.border-lot-blue]="!profileCompleted"
          [class.border-lot-ai]="profileCompleted"
        >
          <i
            class="fa-solid"
            [class.fa-circle-info]="!profileCompleted"
            [class.fa-circle-check]="profileCompleted"
            [class.text-lot-blue]="!profileCompleted"
            [class.text-lot-ai]="profileCompleted"
          ></i>
          <span class="text-lot-dark font-semibold">Complete Settings</span>
        </a> -->
        <a
          href="javascript:void(0)"
          (click)="action(2)"
          class="w-64 flex flex-col items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
        >
          <i class="fa-solid fa-user text-lot-blue"></i>
          <span class="text-lot-dark font-semibold">Add Users and Groups</span>
        </a>
        <a
          href="javascript:void(0)"
          (click)="action(3)"
          class="w-64 flex flex-col items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
        >
          <i class="fa-solid fa-graduation-cap text-lot-blue"></i>
          <span class="text-lot-dark font-semibold">Create Courses</span>
        </a>
      </div>
    </div>
  </div>
</div>

<ng-template #promo>
  <div class="flex flex-col gap-4 px-10 py-20">
    <h1 class="text-lot-blue text-5xl font-bold">
      Exclusive Offer! <br />
      Extend your Free Trial!
    </h1>

    <p class="max-w-xs">
      Only 3 days remain in your Free Trial. Add your card details to unlock
      extra 4 days.
    </p>

    <button
      type="button"
      (click)="subscribe()"
      class="button-primary w-44 py-2 px-6"
    >
      Add Card Details
    </button>
  </div>
</ng-template>

<ng-template #courses>
  <div class="flex flex-col gap-4 px-10 py-20">
    <h1 class="text-lot-blue text-5xl font-bold">Course Creation</h1>

    <p class="max-w-xs">In Construction</p>

    <button type="button" class="button-primary w-44 py-2 px-6">
      coming soon
    </button>
  </div>
</ng-template>
