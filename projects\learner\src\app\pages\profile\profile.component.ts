import { NgTemplateOutlet } from '@angular/common';
import { Component } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ProfileFormComponent } from './form/form-profile.component';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  imports: [
    NgTemplateOutlet,
    ProfileFormComponent,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class ProfileComponent {
  tab = 1;

  tabs = [
    {
      id: 1,
      name: 'Profile Settings',
    },
    // {
    //   id: 2,
    //   name: 'Application Settings',
    // },
    // {
    //   id: 3,
    //   name: 'Notification Settings',
    // },
  ];

  courseInApp = new FormControl<boolean | null>(null);
  courseEmail = new FormControl<boolean | null>(null);
  courseSMS = new FormControl<boolean | null>(null);
}
