import { Component, inject, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { AccountStatus, AuthService, BillingService } from '@lms/core';
import { PlansComponent } from '../../core';
import { DatePipe } from '@angular/common';

@Component({
  selector: 'app-subscription',
  imports: [DatePipe],
  templateUrl: './subscription.component.html',
})
export class SubscriptionComponent {
  service = inject(BillingService);
  authService = inject(AuthService);
  readonly dialog = inject(MatDialog);

  get subscription() {
    return this.service.state.mySubscription();
  }

  get isNotOwer() {
    return this.service.user.role !== AccountStatus.OWNER;
  }

  get dayCount() {
    const date = new Date(this.subscription.expired_at ?? 0);
    const now = new Date();
    return Math.ceil(
      Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    );
  }
  
  get isExpiringSoon() {
    return this.dayCount <= 3;
  }

  subscribe(): void {
    this.dialog.open(PlansComponent, {
      minWidth: '600px',
      maxWidth: '1024px',
    });
  }
  signOut(): void {
    this.authService.signOutAndRedirect();
  }
}
