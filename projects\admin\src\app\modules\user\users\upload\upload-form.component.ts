import { MatIconModule } from '@angular/material/icon';
import { Component, inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  MatDialogRef,
  MatDialogModule,
  MatDialog,
} from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { NgTemplateOutlet } from '@angular/common';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { UserFormComponent } from '../form/user-form.component';
import * as XLSX from 'xlsx';
import { UploadItem, UploadRequest, UploadUtils } from './upload.utils';
import {
  getId,
  UsersCoreService,
  UserItem,
  UserTeamGroupItem,
  ToastMessageType,
} from '@lms/core';

@Component({
  selector: 'app-upload-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatSelectModule,
    NgTemplateOutlet,
    MatChipsModule,
    MatIconModule,
    MatProgressBarModule,
  ],
  templateUrl: 'upload-form.component.html',
  styles: [
    `
      :host {
        --mdc-linear-progress-active-indicator-color: #2e7ddb;
        --mdc-linear-progress-active-indicator-height: 14px;
        --mdc-linear-progress-track-height: 14px;
      }
    `,
  ],
})
export class UploadFormComponent {
  service = inject(UsersCoreService);
  readonly dialog = inject(MatDialog);
  public dialogRef: MatDialogRef<UploadFormComponent> = inject(MatDialogRef);

  view: 'NONE' | 'UPLOAD' | 'VALIDATION' | 'SUBMIT' = 'NONE';

  validHeaders = [
    'Email',
    'Role',
    'FirstName',
    'LastName',
    'Phone',
    'Team',
    'Group',
  ];

  isLoading = false;
  error?: string;

  dataRows: { row: number; data: UploadItem; error: string }[] = [];

  get dataErrors() {
    return this.dataRows.filter((r) => !!r.error);
  }

  addNewUser() {
    this.dialog.open(UserFormComponent, {
      minWidth: '800px',
      data: {
        type: 'ADD',
        item: null,
      },
    });
    this.dialogRef.close();
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const { file, error } = UploadUtils.onFileLoad(input);
    this.error = error;
    if (file) {
      this.readAndValidateFile(file);
    }
  }

  readAndValidateFile(file: File): void {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      const data = new Uint8Array(e.target.result);
      const workbook = XLSX.read(data, { type: 'array' });

      // Get the first sheet
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      // Convert sheet to JSON
      const rowData = XLSX.utils.sheet_to_json(sheet, {
        header: 1,
      });
      const jsonData = XLSX.utils.sheet_to_json<UploadRequest>(sheet);

      if (rowData.length > 300) {
        this.error =
          'Max row limit reached. Please limit your records to 300 or less.';
        return;
      }

      if (rowData.length > 0) {
        const headers = rowData[0] as string[];
        const isValid = this.validHeaders.every(
          (header, index) => headers[index] === header
        );

        if (isValid) {
          this.dataRows = UploadUtils.validateRows(jsonData);
          this.view = this.dataErrors.length ? 'VALIDATION' : 'SUBMIT';
        } else {
          this.error =
            'Invalid headers. Expected: ' + this.validHeaders.join(', ');
        }
      } else {
        this.error = 'The file is empty.';
      }
    };

    reader.readAsArrayBuffer(file);
  }

  async onSubmit() {
    this.error = undefined;
    if (this.isLoading || !this.dataRows.length) return;
    this.isLoading = true;

    const res = await this.addUsers();

    if (res === 'SUCCESS') {
      this.dialogRef.close();
      this.service.userSource.reload();
    }
    if (res === 'VALID') {
      this.isLoading = false;
      return;
    }
    this.view = 'UPLOAD';
  }

  async addUsers() {
    const users = this.dataRows.map(
      (user) =>
        ({
          email: user.data.email,
          role: user.data.role,
          organization: getId(this.service.organization),
          firstname: user.data.firstname,
          lastname: user.data.lastname,
        } as any)
    ) as UserItem[];

    let result: 'SUCCESS' | 'FAILED' | 'VALID' = 'FAILED';
    const res = await this.service.add(users);

    if (res.error) {
      this.view = 'VALIDATION';
      this.error = 'Error adding users';
      this.dataRows = res.error.split(',').map((e, i) => ({
        row: i + 1,
        data: {} as any,
        error: e.trim(),
      }));
      this.isLoading = false;
      result = 'VALID';
      this.service.state.openToast({
        message: 'Failed to upload users. Please check the errors.',
        type: ToastMessageType.ERROR,
      });
      return result;
    }
    result = 'SUCCESS';
    const userGroupData = await this.lookupTeamGroups(res.data);

    if (userGroupData.length) {
      const rv = await this.assignTeamGroups(userGroupData);
      if (!rv) {
        result = 'FAILED';
      }
    }

    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'User Upload saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    return result;
  }

  async lookupTeamGroups(users: UserItem[]) {
    const teams = this.dataRows
      .map((d) => d.data.teams || [])
      .flat()
      .map((v) => v.trim())
      .filter(Boolean)
      .filter((v, i, _) => _.indexOf(v) === i);
    const groups = this.dataRows
      .map((d) => d.data.groups || [])
      .flat()
      .map((v) => v.trim())
      .filter(Boolean)
      .filter((v, i, _) => _.indexOf(v) === i);

    const [resTeams, resGroups] = await Promise.all([
      this.service.teamsService.getTeamGroupByNames(
        'TEAM',
        teams,
        getId(this.service.organization)
      ),
      this.service.teamsService.getTeamGroupByNames(
        'GROUP',
        groups,
        getId(this.service.organization)
      ),
    ]);

    if (!resTeams.data.length && !resGroups.data.length) {
      return [];
    }

    const userRequest = this.dataRows
      .map((d) => {
        return (d.data.teams || []).map((t) => ({
          team: resTeams.data.filter((u) => u.name === t).at(0)?.id!,
          group: '',
          id: users.filter((u) => u.email === d.data.email).at(0)?.id!,
        }));
      })
      .flat();

    const groupRequest = this.dataRows
      .map((d) => {
        return (d.data.teams || []).map((t) => ({
          team: getId(resGroups.data.filter((u) => u.name === t).at(0)?.team!),
          group: resGroups.data.filter((u) => u.name === t).at(0)?.id!,
          id: users.filter((u) => u.email === d.data.email).at(0)?.id!,
        }));
      })
      .flat();

    userRequest.push(...groupRequest);

    return userRequest.filter((u) => !!u.team && !!u.group);
  }

  async assignTeamGroups(
    users: {
      team: string;
      group: string;
      id: string;
    }[]
  ) {
    const userTeamsGroups = users.map((user) => ({
      user: user.id,
      organization: getId(this.service.organization),
      team: user.team,
      group: user.group,
    })) as UserTeamGroupItem[];

    const res2 = await this.service.teamsService.assignUsers({
      news: userTeamsGroups,
      olds: [],
    });
    if (res2.length) {
      this.error = res2.join(', ');
      this.isLoading = false;
      return;
    }
    return true;
  }
}
