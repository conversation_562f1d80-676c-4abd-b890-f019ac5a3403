<div class="w-full relative my-4 px-5">
  <!-- <i class="fa fa-search search-icon text-lot-blue absolute"></i> -->
  <input
    [formControl]="userSearchKey"
    class="search-form text-10xs appearance-none block w-full bg-dark-100 text-gray-700 p-l border rounded py-3 px-4 leading-tight focus:bg-white focus:border-gray-500"
    type="text"
    placeholder="Search and add Student"
  />
</div>

<div class="flex flex-col md:flex-row items-center gap-5 md:gap-20 mt-4">
  <div class="w-full">
    <div class="w-full flex flex-row justify-between items-center">
      <div class="flex justify-start items-center h-10 pl-5 w-1/2">
        <mat-checkbox
          [checked]="allSelected"
          [indeterminate]="!!itemsSelected.length && !allSelected"
          (change)="toggleSelection()"
        >
        </mat-checkbox>
        <span class="ml-7">Total {{ userPaged.count || 0 }} items</span>
      </div>
      <div class="flex justify-between items-center h-10 pl-5 w-1/2">
        <span class="selected-text"> {{ itemsSelected.length }} assigned</span>
        <button
          type="button"
          class="button-primary md:w-24"
          (click)="saveItems()"
          *ngIf="displaySaveButton"
        >
          Save <i class="fas fa-check-circle ml-2"></i>
        </button>
      </div>
    </div>
    <div class="flex flex-row items-center">
      <div class="w-1/2 mt-2 flex items-center justify-between">
        <div class="w-10/12 border min-h-[300px] h-[500px] overflow-y-scroll">
          <div *ngFor="let item of items" style="padding: 0">
            <div
              class="cursor-pointer hover:bg-gray-100 flex justify-between items-center w-full px-5 py-2"
              (click)="onUserSelectionChanged($event, item)"
            >
              <div class="flex flex-col gap-1">
                <h3 matLine>
                  {{ item?.user?.firstname + " " + item?.user?.lastname }}
                </h3>
                <p matLine class="text-gray-500">{{ item?.user?.email }}</p>
              </div>

              <mat-checkbox
                mat-list-icon
                [checked]="isUserAssigned(item)"
                (change)="onUserSelectionChanged($event, item)"
              >
              </mat-checkbox>
            </div>
            <mat-divider></mat-divider>
          </div>
          <mat-paginator
            [length]="items.length"
            [pageSize]="10"
            (page)="handlePageEvent($event)"
          />
        </div>
        <i class="fa-solid fa-arrow-right text-primary-200 text-3xl"></i>
      </div>

      <div class="w-1/2 min-h-[300px] h-[500px] overflow-y-scroll mt-2">
        <div
          *ngFor="let item of itemsSelected"
          class="flex flex-col w-full gap-2 p-2"
        >
          <div class="flex flex-col">
            <h3 matLine>
              {{ item?.user?.firstname + " " + item?.user?.lastname }}
            </h3>
            <p matLine class="text-gray-500">{{ item?.user?.email }}</p>
          </div>

          <div
            style="
              display: flex;
              align-items: center;
              overflow-x: auto;
              overflow-y: hidden;
              width: 100%;
            "
          >
            <mat-chip-set aria-label="Assignments">
              <mat-chip
                class="flex justify-between items-center gap-2"
                *ngFor="let assign of getAssignments(item); let idx = index"
                aria-disabled="true"
                (click)="assignmentItemClicked(item, idx)"
                >{{ assign }}
                <i
                  *ngIf="canUpdateDueDate(item, idx)"
                  class="fa-solid fa-ellipsis-v"
                ></i>
              </mat-chip>
            </mat-chip-set>
          </div>
          <mat-divider></mat-divider>
        </div>
      </div>
    </div>
  </div>
</div>
