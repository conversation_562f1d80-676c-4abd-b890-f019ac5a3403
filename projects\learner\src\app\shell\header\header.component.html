<header class="py-3 md:py-5">
  <div class="mx-auto px-6 max-w-[1300px]">
    <nav class="flex items-center justify-between h-14">
      <a href="/myTraining" class="w-64 h-8">
        <img
          alt="Company Logo"
          class="w-auto h-full"
          src="assets/images/new/logo-light.png"
        />
      </a>
      <div class="flex items-center">
        <ul class="flex items-center gap-20">
          <li>
            <a
              class="font-[500] text-xl leading-10 hover:text-lot-blue"
              [routerLink]="['/myTraining']"
              [class.text-lot-blue]="router.url === '/'"
              [class.text-lot-dark]="router.url !== '/'"
              >Home
            </a>
          </li>
          <li>
            <a
              class="font-[500] text-xl leading-10 hover:text-lot-blue"
              [routerLink]="['/myTraining/course-library']"
              [class.text-lot-blue]="
                router.url === '/myTraining/course-library'
              "
              [class.text-lot-dark]="
                router.url !== '/myTraining/course-library'
              "
              >Course Library
            </a>
          </li>
          <li>
            <a
              class="font-[500] text-xl leading-10 hover:text-lot-blue"
              [routerLink]="['/myTraining/transcript']"
              [class.text-lot-blue]="router.url === '/myTraining/transcript'"
              [class.text-lot-dark]="router.url !== '/myTraining/transcript'"
              >Transcript
            </a>
          </li>
          <li>
            <a
              class="font-[500] text-xl leading-10 hover:text-lot-blue"
              [routerLink]="['/myTraining/skillQuest']"
              [class.text-lot-blue]="router.url === '/myTraining/skillQuest'"
              [class.text-lot-dark]="router.url !== '/myTraining/skillQuest'"
              >SkillQuest
            </a>
          </li>
        </ul>
      </div>
      <div class="flex items-center gap-10">
        <div class="relative">
          <button
            [matMenuTriggerFor]="menu"
            class="p-2 size-14 rounded-full focus:outline-none"
          >
            <img
              src="https://images.pexels.com/photos/460031/pexels-photo-460031.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
              alt=""
              srcset=""
              class="w-full h-full object-cover rounded-full"
            />
          </button>
          <mat-menu #menu="matMenu">
            <ul class="flex flex-col w-64 p-6">
              @for (item of subMenu; track $index; let last=$last) {
              <li
                class="border-gray-200 w-full flex justify-center items-center py-2"
                [class.border-b]="!last"
              >
                @if (item.name === 'Logout') {
                <a
                  class="font-[500] text-lg leading-10 text-lot-dark hover:text-lot-blue flex items-center gap-2"
                  href="javascript:void(0)"
                  (click)="signOut()"
                >
                  <span class="material-symbols-outlined">{{ item.icon }}</span>
                  {{ item.name }}
                </a>
                } @if (item.name !== 'Logout') {
                <a
                  class="font-[500] text-lg leading-10 text-lot-dark hover:text-lot-blue flex items-center gap-2"
                  [routerLink]="[item.link]"
                >
                  <span class="material-symbols-outlined">{{ item.icon }}</span>
                  {{ item.name }}
                </a>
                }
              </li>
              }
            </ul>
          </mat-menu>
        </div>
      </div>
    </nav>
  </div>
</header>
