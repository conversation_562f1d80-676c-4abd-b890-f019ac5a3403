import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { HtmlWrapperComponent } from '../html-wrapper/html-wrapper.component';

@Component({
  selector: 'app-block-list',
  imports: [HtmlWrapperComponent],
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg {{ metaClass }}"
      [style]="styleForm"
      (click)="view.emit(content)"
    >
      @if (content.heading) {
      <h3 class="text-xl font-semibold text-lot-dark break-words">
        {{ content.heading }}
      </h3>
      }

      <ul class="flex flex-col gap-3">
        @for (item of content.items; track $index) {
        <li class="flex items-center gap-3">
          <div class="size-10">
            @if (content.style === 'number') {
            <div
              class="flex items-center justify-center bg-lot-blue text-white rounded-full w-[29px] h-[29px] text-sm font-medium mt-2"
            >
              {{ $index + 1 }}
            </div>
            } @if (content.style === 'disc') {
            <div class="w-2 h-2 rounded-full bg-lot-dark mt-3"></div>
            } @if (content.style === 'circle') {
            <div
              class="size-4 rounded-full border-2 border-lot-dark mt-3"
            ></div>
            }
          </div>
          <app-ui-html-wrapper
            class="text-lg break-words"
            [content]="item.text"
          />
        </li>
        }
      </ul>
    </div>
  `,
})
export class UIBlockListComponent {
  @Input() content: {
    readyForLecture: boolean;
    style: 'disc' | 'circle' | 'number';
    heading?: string;
    items: Array<{
      text: string;
      checked?: boolean;
    }>;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  @Output() view = new EventEmitter();

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit(this.content);
  }

  onCheckItem(index: number): void {}
}

export const getListTemplate = (type: number) => {
  switch (type) {
    case 0: // Numbered List with Heading
      return {
        uiLabel: 'Numbered List Block',
        style: 'number',
        heading: 'Time Off Request Guidelines',
        items: [
          {
            text: 'Plan your vacation or personal time off at least 3 business days in advance.',
          },
          {
            text: 'Notify your manager immediately if you’re feeling sick and unable to work.',
          },
          {
            text: 'Submit all time off requests through the HR Portal for proper tracking.',
          },
          {
            text: 'For unpaid leave, discuss and get approval from your manager before applying.',
          },
          {
            text: 'Keep your team informed about your planned absences to ensure smooth coverage.',
          },
        ],
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      };

    case 1: // Disc List without Heading
      return {
        uiLabel: 'Disc List Block',
        style: 'disc',
        items: [
          {
            text: 'Plan your vacation or personal time off at least 3 business days in advance.',
          },
          {
            text: 'Notify your manager immediately if you’re feeling sick and unable to work.',
          },
          {
            text: 'Submit all time off requests through the HR Portal for proper tracking.',
          },
          {
            text: 'For unpaid leave, discuss and get approval from your manager before applying.',
          },
          {
            text: 'Keep your team informed about your planned absences to ensure smooth coverage.',
          },
        ],
        meta: {
          width: 50,
          padding: 4,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };

    default: // Circle List with Heading
      return {
        uiLabel: 'Circle List Block',
        style: 'circle',
        heading: 'Time Off Request Guidelines',
        items: [
          {
            text: 'Plan your vacation or personal time off at least 3 business days in advance.',
          },
          {
            text: 'Notify your manager immediately if you’re feeling sick and unable to work.',
          },
          {
            text: 'Submit all time off requests through the HR Portal for proper tracking.',
          },
          {
            text: 'For unpaid leave, discuss and get approval from your manager before applying.',
          },
          {
            text: 'Keep your team informed about your planned absences to ensure smooth coverage.',
          },
        ],
        meta: {
          width: 100,
          height: 50,
          padding: 6,
          margin: 4,
          background: 'lot-blue',
          color: 'white',
        },
      };
  }
};
