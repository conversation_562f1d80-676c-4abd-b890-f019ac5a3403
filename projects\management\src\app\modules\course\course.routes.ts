import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./container/container.component').then(
        (c) => c.CourseContainerComponent
      ),
  },
  {
    path: 'view/:id',
    loadComponent: () =>
      import('./editor/course-view.component').then(
        (c) => c.CourseViewComponent
      ),
  },
  {
    path: 'view/:id/:lessonId',
    loadComponent: () =>
      import('./editor/course-view.component').then(
        (c) => c.CourseViewComponent
      ),
  },
  // {
  //   path: 'preview/:id',
  //   loadComponent: () =>
  //     import('./preview/preview.component').then(
  //       (c) => c.CoursePreviewComponent
  //     ),
  // },
];
