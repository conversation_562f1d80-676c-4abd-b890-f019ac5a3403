import { NgTemplateOutlet } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Router } from '@angular/router';
import { AuthService, getId, LoadingService, markControlsDirty } from '@lms/core';
import {
  ValidationTextComponent,
  PasswordData,
  PasswordFormComponent,
} from '@lms/shared';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-sign-up',
  imports: [
    NgTemplateOutlet,
    FormsModule,
    ReactiveFormsModule,
    ValidationTextComponent,
    MatProgressBarModule,
    NgxMaskDirective,
    PasswordFormComponent,
  ],
  templateUrl: './sign-up.component.html',
})
export class SignUpComponent {
  router = inject(Router);
  loader = inject(LoadingService);
  authService = inject(AuthService);

  view: 'SIGNUP' | 'PASSWORD' | 'SUBMIT' = 'SIGNUP';
  isLoading = false;

  message?: string;
  form = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email]),
    phone: new FormControl('', Validators.required),
    firstname: new FormControl('', Validators.required),
    lastname: new FormControl('', Validators.required),
    title: new FormControl('', Validators.required),
    company: new FormControl('', Validators.required),
    nbEmployees: new FormControl('', Validators.required),
    about: new FormControl(''),
  });

  year = new Date().getFullYear();

  onSubmit() {
    this.message = undefined;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    this.view = 'PASSWORD';
  }

  async onSubmitPassword(data: PasswordData) {
    this.message = undefined;
    if (this.isLoading || !data.data?.newPassword) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    this.isLoading = true;
    const phone = this.form.value.phone!.replace(/\D/g, '');
    const response = await this.authService.signUp({
      user: {
        email: this.form.value.email!,
        phone: phone,
        firstname: this.form.value.firstname!,
        lastname: this.form.value.lastname!,
        password: data.data?.newPassword,
      },
      org: {
        name: this.form.value.company!,
        email: this.form.value.email!,
        phone: phone,
        about: {
          nbEmployees: this.form.value.nbEmployees!,
          about: this.form.value.about!,
        },
        contact: {
          name: this.form.value.firstname! + ' ' + this.form.value.lastname!,
          email: this.form.value.email!,
          phone: phone,
          title: this.form.value.title!,
        },
        description: this.form.value.company!,
      },
    });
    this.isLoading = false;
    if (response.error) {
      this.message = response.error;
      return;
    }

    if (response.data.at(0)?.organization) {
      this.authService.subscribeTrial(getId(response.data.at(0)?.organization));
    }
    this.view = 'SUBMIT';
  }

  reverify() {
    this.authService.resendCode(this.form.value.email!);
  }
}
