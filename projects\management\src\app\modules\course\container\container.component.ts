import { Component, inject } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CourseCoreService, CourseType, stripHtml } from '@lms/core';
import { CourseComponent } from '../course/course.component';
import { UploadScormFormComponent } from '../components/upload/upload-form.component';
import { SCORM_DATA } from './scorm.data';

@Component({
  selector: 'app-course-container',
  imports: [CourseComponent],
  templateUrl: 'container.component.html',
})
export class CourseContainerComponent {
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  tab = 1;

  type = this.service.viewType;

  add() {
    if (this.tab === 1) {
      this.dialog.open(UploadScormFormComponent, {
        minWidth: '800px',
      });
      return;
    }
    // this.dialog.open(TeamFormComponent, {
    //   width: '600px',
    //   data: {
    //     type: this.type() === 'GROUP' ? 'GROUP' : 'TEAM',
    //     item: this.type() === 'GROUP' ? this.service.viewTeam() : null,
    //   },
    // });
  }

  async saveCourse() {
    const items = SCORM_DATA;
    const chunkSize = 100;
    for (let i = 0; i < items.length; i += chunkSize) {
      const payload = items.slice(i, i + chunkSize).map((item) => {
        return {
          name: item.Name,
          description: item.Description,
          cover: 'assets/images/new/card-2.jpeg',
          short: stripHtml(item.Description!)?.substring(0, 250),
          venue: 'Online',
          duration: +item.Runtime,
          type: CourseType.LIBRARY,
          tags: item.Category,
          language: item.Language?.toUpperCase(),
          scormCourseId: item.Sku,
          creator: {
            name:
              this.service.user.firstname + ' ' + this.service.user.lastname,
            email: this.service.user.email,
            bio: 'Course Creator',
            avatar: this.service.user.avatar,
          },
          created_by: this.service.user.id,
        };
      });
    const { data, error } = await this.service.state.supabase
      .from('courses')
      .insert(payload)
      .select('*');
    }
  }

  setTab(tab: number) {
    this.tab = tab;
    this.service.viewType.set(tab === 1 ? 'COURSE' : 'LEARNINGPATH');
  }
}
