export const InstructorLearnerPlayer = `
query getCatalogPathOne($id: UUID!, $userId: UUID!, $teams: [UUID!], $groups: [UUID!]) {
  instructor_enrollmentsCollection(
  filter: { or: [{instructor: {eq: $id}}, {team: {in: $teams}},{group: {in: $groups}}] }
) {
    edges {
      node {
        id
        created_at
        dueDate
        instructor
        learning_instructors {
          id
          name
          type
          items
          short
          description
          prerequisites
          cover
          creator
          resources
          sessions
        }
        user_trackings_comboCollection(
          filter: {
            user: {eq: $userId }
          }
        ) {
          edges {
            node {
              id
              enrollment
              type
              status
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
              sessions
            }
          }
        }
      }
    }
  }
}
`;
