import {
  Component,
  computed,
  inject,
  input,
  OnInit,
  resource,
  signal,
} from '@angular/core';
import { PlayerService } from '../services/app.service';
import { NavigationItem } from '../services/common.model';
import {
  Course,
  LearningType,
  Lesson,
  Module,
  TraceStatus,
  UserTrackingItem,
  VisitedLesson,
} from '@lms/core';
import {
  DynamicContentComponent,
  HtmlWrapperComponent,
  ResourceHeaderComponent,
  StartRatingComponent,
  UIBlockData,
  WidgetType,
} from '@lms/shared';
import { NgTemplateOutlet } from '@angular/common';
import { filter, of, switchMap, take } from 'rxjs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { OverlayModule } from '@angular/cdk/overlay';

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  imports: [
    ResourceHeaderComponent,
    NgTemplateOutlet,
    HtmlWrapperComponent,
    DynamicContentComponent,
    MatProgressBarModule,
    OverlayModule,
    StartRatingComponent,
  ],
})
export class MainComponent implements OnInit {
  service = inject(PlayerService);

  id = input<string>('');
  token = input<string>('');
  source = input<'ADMIN' | 'LEARNER'>('ADMIN');

  subMenu = [
    {
      name: 'Logout',
      link: '/',
      icon: 'input',
    },
  ];

  view: 'COURSE' | 'MODULE' | 'LESSON' | 'END' = 'COURSE';
  viewChat = true;
  enableNext = true;
  openFeedback = false;

  rating = signal<number>(0);
  comments = signal<string>('');

  isLoading = false;
  currentLessonIndex = -1;
  currentModuleIndex = -1;
  currentTrace?: VisitedLesson;
  _contents: any[] = [];

  course?: Course;
  tracking?: UserTrackingItem;
  modules: Module[] = [];
  lessons: Lesson[] = [];

  lectures: any[] = [];

  get isLearner() {
    return this.service.source === 'LEARNER';
  }
  get currentModule() {
    return this.modules.at(this.currentModuleIndex);
  }
  get currentLesson() {
    return this.lessons.at(this.currentLessonIndex);
  }
  get disableFeedback() {
    return (
      this.tracking?.status === 'COMPLETED' ||
      !!this.tracking?.feedback?.rate
    );
  }

  courseSource = this.service.courseSource;
  navigations = this.service.courseNavigation;

  tab = 1;
  extraTab = 1;

  tabs = computed(() => [
    {
      id: 1,
      name: 'Course Information',
    },
    {
      id: 2,
      name: 'Instructor Bio',
    },
    {
      id: 3,
      name: 'Resource',
    },
  ]);

  param = {
    id: '',
    source: '',
  };

  refresher: any;

  ngOnInit(): void {
    const urlParams = new URLSearchParams(window.location.search);
    this.param = {
      id: this.id() || urlParams.get('id') || '',
      source: this.source() || urlParams.get('source') || 'ADMIN',
    };

    if (!this.param.id || !['ADMIN', 'LEARNER'].includes(this.param.source))
      return;

    this.service.source = this.param.source as 'ADMIN' | 'LEARNER';
    this.service.filter.set({
      id: this.param.id,
      source: this.param.source as 'ADMIN' | 'LEARNER',
    });

    this.service.launch
      .pipe(
        take(2),
        switchMap((item) =>
          item ? this.launch(this.service.courseSource.value()!) : of(null)
        )
      )
      .subscribe(() => {});
  }

  getTrack(item: UIBlockData) {
    if (item.widgetType === WidgetType.QUIZ && !!item.content['requirePass']) {
      this.enableNext = !!item.userAnswers!['summary']['passed'];
    }

    if (
      [WidgetType.RESOURCE, WidgetType.VIDEO].includes(item.widgetType) &&
      !!item.content['requirePass']
    ) {
      this.enableNext = !!item.userAnswers!['completed'];
    }
  }

  async launch(item: UserTrackingItem) {
    if (item.lastTrace && item.progress >= 2) {
      this.resumeCourse(item);
      return;
    }
    this.tracking = item;
    this.course = item.course;
    this.modules = (this.course.modules ?? []).sort((a: any, b: any) =>
      a.order < b.order ? -1 : 1
    );
    this.currentModuleIndex = 0;
    this.view = 'MODULE';
    await this.updateTrace(
      this.course.id,
      LearningType.COURSE,
      TraceStatus.IN_PROGRESS
    );
    this.service.currentLecture.set({
      id: this.currentModule?.id!,
      type: 'MODULE',
    });
    await this.updateTrace(
      this.currentModule?.id!,
      LearningType.MODULE,
      TraceStatus.IN_PROGRESS
    );
  }

  async continue(module: Module) {
    window.scrollTo(0, 0);
    this.lessons = (module.lessons ?? []).sort((a: any, b: any) =>
      a.order < b.order ? -1 : 1
    );
    this.currentLessonIndex = 0;
    this.viewChat = false;
    this.view = 'LESSON';
    await this.updateCurrentLecture();
  }

  async next() {
    window.scrollTo(0, 0);
    if (this.currentLessonIndex >= this.lessons.length - 1) {
      if (this.currentModuleIndex >= this.modules.length - 1) {
        this.view = 'END';
        await await this.updateCurrentLecture();
        await this.updateTraceComplete(this.course?.id!);
        return;
      }
      await this.updateTraceComplete(this.currentModule?.id!);
      this.currentModuleIndex++;
      this.view = 'MODULE';
      this.service.currentLecture.set({
        id: this.currentModule?.id!,
        type: 'MODULE',
      });
      await this.updateTrace(
        this.currentModule?.id!,
        LearningType.MODULE,
        TraceStatus.IN_PROGRESS
      );
      return;
    }
    this.currentLessonIndex++;
    await this.updateCurrentLecture();
  }

  gotTo(item: NavigationItem) {
    if (!item.current && !item.visited) return;
    this.service.viewChat = ['LearnMate', 'SkillQuest'].includes(item.id);
  }

  async updateCurrentLecture() {
    const lesson = this.lessons.at(this.currentLessonIndex);

    if (!lesson) return;

    this.service.currentLecture.set({
      id:
        this.currentLessonIndex === 0
          ? lesson.id
          : this.lessons.at(this.currentLessonIndex - 1)?.id!,
      type: 'LESSON',
    });

    await this.updateTrace(
      lesson.id,
      LearningType.LESSON,
      TraceStatus.IN_PROGRESS
    );
    this.lectures = (lesson.contents || []).map(
      (c) =>
        ({
          ...c,
          lessonId: lesson.id,
          lessonName: lesson.name,
        } as any)
    );

    this.enableNext = !this.lectures.some(
      (l) =>
        [WidgetType.RESOURCE, WidgetType.VIDEO, WidgetType.QUIZ].includes(
          l.widgetType
        ) && !!l.content['requirePass']
    );
  }

  private async updateTrace(
    id: string,
    type: LearningType,
    status: TraceStatus
  ) {
    const previous = this.service.visited().find((v) => v.id === id);
    if (previous) return;
    this.currentTrace = {
      id: id,
      date: new Date(),
      complete: false,
      type: type,
      status: status,
      moduleIndex: this.currentModuleIndex,
      lessonIndex: this.currentLessonIndex,
      userTracking: [],
    };
    await this.saveTracking(this.currentTrace!);
  }

  private async saveTracking(trace: VisitedLesson) {
    const ids = [this.course?.id!];
    const idLessons = (this.course?.modules ?? [])
      .map((m) => (m.lessons ?? []).map((l) => l.id))
      .flat();
    ids.push(...idLessons, ...(this.course?.modules ?? []).map((m) => m.id));

    const visited = this.service.visited();
    visited.push(trace);

    this.service.visited.set(visited);
    const visitedIds = visited
      .map((v) => v.id)
      .filter((v, i, self) => self.map((x) => x).indexOf(v) === i);
    // const notVisited = ids.filter((v) => !visitedIds.includes(v));
    const progress = Math.round((visitedIds.length / ids.length) * 100);

    const trackings = [...(this.tracking?.trackings || []), ...visited].filter(
      (v, i, self) => self.map((x) => x.id).indexOf(v.id) === i
    );
    const tracking = {
      progress: progress,
      status: progress >= 100 ? TraceStatus.COMPLETED : TraceStatus.IN_PROGRESS,
      rating: 0,
      trackings: trackings,
      lastTrace: trace,
    } as any;

    this.refresher = tracking;
    if (this.source() === 'ADMIN') {
      return;
    }

    const res = await this.service.trainingService.updateTracking(
      this.tracking?.id!,
      tracking
    );

    if (res.error) {
      return;
    }

    this.service.courseSource.reload();
  }

  private async updateTraceComplete(id: string) {
    const items = this.service.visited();
    const previous = items.find((v) => v.id === id);
    if (!previous) return;
    this.service.visited.update((v) => [
      ...v.filter((x) => x.id !== id),
      {
        ...previous!,
        complete: true,
        status: TraceStatus.COMPLETED,
      },
    ]);
    await this.saveTracking(this.currentTrace!);
  }

  viewSummary() {
    const logs = this.service.visited();
    console.log('Logs :: ', logs);
  }

  resumeCourse(tracking: UserTrackingItem) {
    this.tracking = tracking;
    const visitedItems = tracking.trackings || [];
    if (!visitedItems.length) return;

    this.currentTrace = tracking.lastTrace;
    this.course = tracking.course;
    this.modules = (this.course.modules ?? []).sort((a: any, b: any) =>
      a.order < b.order ? -1 : 1
    );
    this.currentModuleIndex = tracking.lastTrace.moduleIndex || 0;
    this.view = 'COURSE';

    if (tracking.feedback) {
      this.rating.set(tracking.feedback.rate || 0);
      this.comments.set(tracking.feedback.description || '');
    }

    if (tracking.status === TraceStatus.COMPLETED) {
      this.view = 'END';
      return;
    }

    if (
      tracking.lastTrace.type === LearningType.MODULE ||
      this.service.currentLecture()?.type === 'MODULE'
    ) {
      this.currentModuleIndex = tracking.lastTrace.moduleIndex || 0;
      this.view = 'MODULE';
      this.service.currentLecture.set({
        id: this.currentModule?.id!,
        type: 'MODULE',
      });
    }
    if (tracking.lastTrace.type === LearningType.LESSON) {
      this.currentModuleIndex = tracking.lastTrace.moduleIndex || 0;
      this.lessons = (this.currentModule?.lessons ?? []).sort(
        (a: any, b: any) => (a.order < b.order ? -1 : 1)
      );
      this.currentLessonIndex = tracking.lastTrace.lessonIndex || 0;
      this.view = 'LESSON';
      this.service.currentLecture.set({
        id: this.currentLesson?.id!,
        type: 'LESSON',
      });
      this.viewChat = false;
      this.lectures = (this.currentLesson?.contents || []).map(
        (c) =>
          ({
            ...c,
            lessonId: this.currentLesson?.id,
            lessonName: this.currentLesson?.name,
          } as any)
      );
    }
  }

  async saveFeedback() {
    this.isLoading = true;
    const rating = this.rating();
    const comments = this.comments();
    if (!rating || !comments) return;
    const res = await this.service.trainingService.updateTracking(
      this.tracking?.id!,
      {
        ...this.tracking,
        feedback: {
          rate: rating,
          description: comments,
        },
      } as any
    );
    this.isLoading = false;
    this.openFeedback = false;
  }

  exit() {
    window.close();
  }
}
