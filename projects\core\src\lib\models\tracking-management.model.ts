// "userId": "6ffe6d1b-7b28-40c6-8002-c07c4635c2f9",
// "organizationId": "076b6dc8-c407-4ee5-a47c-745fa6f8a825",
// "courses": [],
// "learningPaths": [],
// "quizzes": [],
// "created_at": "2021-12-22T07:50:25.197Z",
// "created_by": "Anonymous Add",
// "updated_by": null,
// "updated_at": null,
// "id": "6d78be71-6c9c-423a-af6d-7553fe53ee58"

// export interface TrackingManagement {
//   userId: string;
//   organizationId: string;
//   courses: ItemTracker[];
//   learningPaths: ItemTracker[];
//   quizes: ItemTracker[];
//   id: string;
// }

// export interface ItemTracker {
//   id: string;
//   type: string;
//   date: Date;
//   visited: boolean;
//   complete: boolean;
//   content: any;
//   items: ItemTracker[];
// }

// export enum ItemTrackerType {
//   COURSE = 'COURSE',
//   MODULE = 'MODULE',
// }
