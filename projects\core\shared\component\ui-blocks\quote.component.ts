import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-block-quote',
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg {{ metaClass }}"
      [style]="styleForm"
      (click)="selectBlock()"
    >
      @if (content.style === 'simple') {
      <div
        class="flex gap-4"
        [class.flex-row-reverse]="content.alignment === 'right'"
        [class.justify-center]="content.alignment === 'center'"
      >
        <i class="fas fa-quote-left text-4xl text-lot-blue"></i>
        <blockquote class="flex flex-col gap-2">
          <div
            class="text-lg italic break-words"
            [innerHTML]="content.quote"
          ></div>
          @if (content.author) {
          <footer class="text-sm text-lot-dark-gray">
            — {{ content.author }}
            @if (content.source) {
            <span>, {{ content.source }}</span>
            }
          </footer>
          }
        </blockquote>
      </div>
      } @if (content.style === 'card') {
      <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex flex-col gap-4 relative">
          <i
            class="fas fa-quote-left text-6xl text-lot-blue/10 absolute -top-2 -left-2"
          ></i>
          <blockquote class="flex flex-col gap-3 z-10">
            <div
              class="text-xl italic pt-4 break-words"
              [innerHTML]="content.quote"
            ></div>
            @if (content.author) {
            <footer class="flex items-center gap-3 mt-2">
              @if (content.authorImage) {
              <img
                [src]="content.authorImage"
                alt="{{ content.author }}"
                class="w-12 h-12 rounded-full object-cover"
              />
              }
              <div class="flex flex-col">
                <span class="font-medium text-lot-dark">{{
                  content.author
                }}</span>
                @if (content.source) {
                <span class="text-sm text-lot-dark-gray">{{
                  content.source
                }}</span>
                }
              </div>
            </footer>
            }
          </blockquote>
        </div>
      </div>
      } @if (content.style === 'bordered') {
      <div
        class="border-l-4 border-lot-blue pl-6"
        [class.text-center]="content.alignment === 'center'"
        [class.text-right]="content.alignment === 'right'"
      >
        <blockquote class="flex flex-col gap-3">
          <div
            class="text-xl font-light break-words"
            [innerHTML]="content.quote"
          ></div>
          @if (content.author) {
          <footer class="text-lot-dark-gray">
            <span class="font-medium text-lot-dark">{{ content.author }}</span>
            @if (content.source) {
            <span class="text-sm"> • {{ content.source }}</span>
            }
          </footer>
          }
        </blockquote>
      </div>
      } @if (content.style === 'modern') {
      <div class="flex flex-col gap-6">
        <div class="flex justify-center">
          <div class="w-20 h-1 bg-lot-blue rounded-full"></div>
        </div>
        <blockquote
          class="text-center flex flex-col gap-4"
          [class.text-left]="content.alignment === 'left'"
          [class.text-right]="content.alignment === 'right'"
        >
          <div
            class="text-2xl font-light leading-relaxed break-words"
            [innerHTML]="content.quote"
          ></div>
          @if (content.author) {
          <footer class="flex flex-col items-center gap-1">
            <span class="font-medium text-lot-blue">{{ content.author }}</span>
            @if (content.source) {
            <span class="text-sm text-lot-dark-gray">{{ content.source }}</span>
            }
          </footer>
          }
        </blockquote>
      </div>
      }@if (content.style === 'note') {
      <div class="flex justify-between gap-10 p-6">
        <i class="fa-solid fa-circle-info text-4xl text-lot-blue"></i>
        <div class="flex-grow w-full">
          <div
            class="text-lg text-justify break-words"
            [innerHTML]="content.quote"
          ></div>
        </div>
      </div>
      }
    </div>
  `,
})
export class UIBlockQuoteComponent {
  @Input() content: {
    readyForLecture: boolean;
    style: 'simple' | 'card' | 'bordered' | 'modern' | 'note';
    quote: string;
    author?: string;
    source?: string;
    authorImage?: string;
    alignment: 'left' | 'center' | 'right';

    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  @Output() view = new EventEmitter();
  
  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      // background: !this.content?.meta?.background
      //   ? 'bg-lot-gray/20'
      //   : 'bg-' + this.content?.meta?.background,
      // color: !this.content?.meta?.color
      //   ? ''
      //   : 'text-' + this.content?.meta?.color,
    };
    const position =
      (this.content?.meta?.width || 0) < 100
        ? ' mx-auto justify-center items-center '
        : '';
    return Object.values(meta).join(' ') ;
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit(this.content);
  }
}

export const getQuoteTemplate = (type: number) => {
  switch (type) {
    case 0: // Simple Left-aligned Quote
      return {
        uiLabel: 'Simple Quote Block',
        style: 'simple',
        quote:
          'Learn Or Teach is a powerful e-learning course builder that enables users to create engaging and interactive content. One of its features includes the ability to add text-based learning content in a structured and digestible format.',
        author: 'Peter Drucker',
        alignment: 'left',
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };

    case 1: // Card Quote with Author Image
      return {
        uiLabel: 'Card Quote Block',
        style: 'card',
        quote: `We need to do a better job of putting ourselves higher on our own ‘to-do’ list`,
        author: 'Michelle Obama',
        source: 'Former First Lady of the United States',
        authorImage: 'https://picsum.photos/200/200',
        alignment: 'left',
        meta: {
          width: 75,
          padding: 4,
          margin: 2,
          background: 'lot-white',
          color: 'lot-blue',
        },
      };

    case 2: // Bordered Center Quote
      return {
        uiLabel: 'Bordered Quote Block',
        style: 'bordered',
        quote:
          'These text sections are designed to convey key information clearly and concisely, making it easier for learners to absorb the material. By following best practices, such as keeping headings concise and paragraphs focused, users can create effective learning experiences.',
        author: 'Stewart Brand',
        source: 'Whole Earth Catalog',
        alignment: 'center',
        meta: {
          width: 75,
          padding: 8,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };

    case 3: // Modern Right-aligned Quote
      return {
        uiLabel: 'Modern Quote Block',
        style: 'modern',
        quote: 'The future belongs to those who believe in the beauty of their dreams.',
        author: 'Eleanor Roosevelt',
        alignment: 'right',
        meta: {
          width: 100,
          height: 50,
          padding: 6,
          margin: 4,
          background: 'lot-blue',
          color: 'white'
        }
      };

    case 4: // Card Quote without Image
      return {
        uiLabel: 'Card Quote Block',
        style: 'card',
        quote: 'Simplicity is the ultimate sophistication.',
        author: 'Leonardo da Vinci',
        source: 'Notebooks',
        alignment: 'left',
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark'
        }
      };

    default: // Default Configuration
      return {
        uiLabel: 'Note Quote Block',
        style: 'note',
        quote: `Learn Or Teach is a powerful e-learning course builder that enables users to create engaging and interactive content. One of its features includes the ability to add text-based learning content in a structured and digestible format. These text sections are designed to convey key information clearly and concisely, making it easier for learners to absorb the material. By following best practices, such as keeping headings concise and paragraphs focused, users can create effective learning experiences.`,
        meta: {
          width: 100,
          padding: 0,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };
  }
};