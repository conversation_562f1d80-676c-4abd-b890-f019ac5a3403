import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { RouterLink } from '@angular/router';
import { AuthService, markControlsDirty } from '@lms/core';
import { ValidationTextComponent } from '@lms/shared';

@Component({
  selector: 'app-forgot-password',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    ValidationTextComponent,
    MatButtonModule,
    RouterLink,
  ],
  templateUrl: './forgot.component.html',
})
export class ForgotComponent {
  authService = inject(AuthService);
  form = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email]),
  });

  message: string;
  submitted = false;

  get organization() {
    return this.authService.organization['name'] ?? '';
  }

  async onSubmit() {
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    try {
      this.authService.isLoading = true;
      const { error } = await this.authService.requestPasswordReset(
        this.form.value.email!
      );
      this.authService.isLoading = false;
      if (error) {
        this.message = error.message;
        return;
      }
      this.submitted = true;
    } catch (error: any) {
      this.message = error?.message;
    } finally {
      this.authService.isLoading = false;
    }
  }
}
