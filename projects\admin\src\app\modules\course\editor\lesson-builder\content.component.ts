import {
  Component,
  EventEmitter,
  inject,
  Injector,
  input,
  Input,
  linkedSignal,
  OnInit,
  Output,
  resource,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  CourseCoreService,
  getId,
  Lesson,
  Module,
  ToastMessageType,
  tryPromise,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { NgTemplateOutlet } from '@angular/common';
import { LessonWidgetEditorComponent } from '../widget-form/lesson-widget-editor.component';
import { v4 as gUID } from 'uuid';
import { MatMenuModule } from '@angular/material/menu';
import { Overlay, OverlayModule, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import {
  DialogComponent,
  DynamicContentComponent,
  LessonWidgetTypes,
  ResourceHeaderComponent,
  UIBlockPickerComponent,
  UIFormatterComponent,
} from '@lms/shared';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-lesson-builder',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    NgTemplateOutlet,
    DynamicContentComponent,
    OverlayModule,
    ResourceHeaderComponent,
  ],
  templateUrl: 'content.component.html',
})
export class LessonBuilderComponent implements OnInit {
  readonly router = inject(Router);
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  overlay = inject(Overlay);

  // @Input({ required: true }) data: { module: Module; lesson: Lesson };
  // @Output() view = new EventEmitter<{ module: Module; lesson: Lesson }>();
  id = input<string>('');

  // lessonId = signal<string>('');
  lessonSource = resource({
    request: () => this.id(),
    loader: async ({ request }) => {
      if (!request) {
        return;
      }
      const [error, res] = await tryPromise(this.service.queryLesson(request));
      if (error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return res;
    },
  });

  isLoading = false;
  error?: string;
  type: 'MODULE' | 'LESSON' = 'MODULE';

  isOpenFormatter = false;
  overlayRef: OverlayRef;

  drawerRef = viewChild<TemplateRef<any>>('widgetPickerAll');
  formatterTrigger = viewChild<TemplateRef<any>>('formatterTrigger');

  get module() {
    return this.lessonSource.value()?.module!;
  }
  get lesson() {
    return this.lessonSource.value()?.lesson!;
  }

  editorStyle = {
    height: '250px',
    backgroundColor: '#ffffff',
    border: 'none',
  };

  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl(''),
  });

  dialogRef?: MatDialogRef<any>;

  widgets = LessonWidgetTypes;

  contents = linkedSignal(() =>
    (this.lessonSource.value()?.lesson?.contents || []).filter(
      (x) => x && !!x['id']
    )
  );

  get f() {
    return this.form.controls;
  }

  back() {
    this.service.state.viewType.set('COURSE');
    this.router.navigate(['/lms/courses/view', getId(this.module.course)]);
  }

  add(type: 'MODULE' | 'LESSON' = 'MODULE') {
    this.type = type;
  }

  ngOnInit(): void {
    this.service.state.viewType.set('LESSON');
    this.service.formatterTrigger.subscribe((res) => {
      const contents = this.contents();
      contents[res.index] = res.content;
      this.contents.set(contents);
      this.onSubmit();
    });
  }

  openMore(item: any) {
    this.dialog
      .open(UIBlockPickerComponent, {
        maxWidth: 'calc(100vw - 100px)', //1050px
        maxHeight: 'calc(100vh - 100px)', //700px
        width: '100%',
        height: '100%',
        position: { top: '50px', left: '5px' },
        data: {
          id: item.id,
        },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.contents.set([
            ...this.contents(),
            {
              content: {
                ...(result.data?.content ?? result.data),
                readyForLecture: true,
              },
              widgetType: result.widget.id,
              userAnswers: result.data?.userAnswers ?? [],
              id: gUID(),
            },
          ]);
          this.onSubmit();
        }
      });
  }

  removeContent(index: number): void {
    if (index >= 0 && index < this.contents().length) {
      this.contents.update((contents) => {
        contents.splice(index, 1);
        return contents;
      });
    }
  }

  getTrack(item: any) {
    console.log('Widget track :: ', item);
  }

  async getAction(
    type: 'moveUp' | 'moveDown' | 'clone' | 'delete' | 'edit',
    currentIndex: number
  ) {
    const items = [...this.contents()];

    if (type === 'edit') {
      this.editWidget(currentIndex);
      return;
    }

    if (type === 'delete') {
      items.splice(currentIndex, 1);
      this.contents.set(items);
    }

    if (type === 'clone') {
      const clonedItem = items[currentIndex];
      this.contents.set([...items, clonedItem]);
    }

    if (type === 'moveUp' && currentIndex > 0) {
      const temp = items[currentIndex];
      items[currentIndex] = items[currentIndex - 1];
      items[currentIndex - 1] = temp;
      this.contents.set(items);
    }

    if (type === 'moveDown' && currentIndex < items.length - 1) {
      const temp = items[currentIndex];
      items[currentIndex] = items[currentIndex + 1];
      items[currentIndex + 1] = temp;
      this.contents.set(items);
    }
    await this.onSubmit();
  }

  openFormatter(index: number) {
    const target = document.querySelector('#overMenu-' + index) as HTMLElement;

    const overlayRef = this.overlay.create({
      hasBackdrop: true,
      positionStrategy: this.overlay
        .position()
        .flexibleConnectedTo(target)
        .withLockedPosition()
        .withPositions([
          {
            originX: 'start',
            originY: 'bottom',
            overlayX: 'start',
            overlayY: 'top',
          },
        ]),
    });

    const popupComponentPortal = new ComponentPortal(UIFormatterComponent);

    setTimeout(() => {
      overlayRef.attach(popupComponentPortal);
      this.service.formatterData = {
        index,
        content: this.contents()[index],
        overlayRef,
      };
    }, 300);

    overlayRef.backdropClick().subscribe(() => {
      overlayRef.dispose();
    });
  }

  editWidget(index: number): void {
    if (!(index >= 0 && index < this.contents().length)) return;
    let widget = this.contents()[index];
    this.dialog
      .open(LessonWidgetEditorComponent, {
        maxWidth: '1200px',
        maxHeight: '90vh',
        width: '100%',
        height: '90vh',
        data: {
          widgetType: widget['widgetType'],
          widgetContent: widget['content'],
        },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          widget = {
            ...widget,
            content: { ...result, readyForLecture: true },
            changed: new Date().toISOString(),
            id: gUID(),
          };
          this.contents.update((contents) => {
            contents[index] = widget;
            return contents;
          });
          this.onSubmit();
        }
      });
  }

  async onSubmit() {
    if (this.isLoading) return;
    const cleanContents = this.contents().map((widget) => {
      const cleanWidget = {
        id: widget['id'],
        widgetType: widget['widgetType'],
        content: { ...widget['content'], component: null },
        userAnswers: widget['userAnswers'] || [],
        changed: widget['changed'],
      };

      if (widget['widgetType'] === 'FLASHCARD' && cleanWidget.content?.cards) {
        cleanWidget.content.cards = cleanWidget.content.cards.map(
          (card: any) => ({
            ...card,
            front: { ...card.front, component: null },
            back: { ...card.back, component: null },
          })
        );
      }

      return cleanWidget;
    });

    const payload = {
      ...this.lesson,
      contents: cleanContents.filter((x) => x && !!x['id']),
    };
    this.isLoading = true;
    this.contents.set([]);
    const [error, res] = await tryPromise(this.service.saveLesson(payload));
    this.isLoading = false;
    if (error || res.error) {
      this.error = error?.message ?? res?.error;
      this.service.state.openToast({
        message: 'Opous!: ' + (error?.message ?? res?.error),
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.lessonSource.reload();
    this.service.state.openToast({
      message: 'Lesson saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.contents.set(res.data?.contents || []);
  }

  preview() {
    const query = new URLSearchParams({
      id: getId(this.module.course),
      source: 'ADMIN',
    });

    window.open(`go-training?${query.toString()}`, 'LearnOrTeach', 'popup');
    this.service.state.viewType.set('COURSE');
    this.router.navigate(['/lms/courses/view', getId(this.module.course)]);
  }

  async deleteLesson() {
    const data = this.lesson;
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${data.name} lesson. All contents associated to it will be deleted.`,
            title: 'Are you sure to remove this Lesson?',
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.deleteLesson(data.id);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
      }
      if (result.data) {
        this.router.navigate(['/lms/courses/view', getId(this.module.course)]);
      }
    }
  }
}
