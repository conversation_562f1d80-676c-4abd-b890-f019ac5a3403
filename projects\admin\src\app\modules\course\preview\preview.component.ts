import {
  Component,
  computed,
  inject,
  input,
  On<PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { Course, CourseCoreService, Lesson, Module } from '@lms/core';
import { DynamicContentComponent, HtmlWrapperComponent, ResourceHeaderComponent } from '@lms/shared';
import { Router, RouterLink } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';

type NavigationItem = {
  id: string;
  name: string;
  icon: string;
  items?: NavigationItem[];
  type: 'MODULE' | 'TOP' | 'EXIT';
};

@Component({
  selector: 'app-preview',
  imports: [
    ResourceHeaderComponent,
    RouterLink,
    MatMenuModule,
    NgTemplateOutlet,
    HtmlWrapperComponent,
    DynamicContentComponent,
  ],
  templateUrl: 'preview.component.html',
  styles: [
    `
      ::ng-deep {
        ::ng-deep .mat-mdc-menu-panel {
          max-width: 410px !important;
        }
      }
    `,
  ],
})
export class CoursePreviewComponent implements OnInit, OnDestroy {
  readonly router = inject(Router);
  readonly service = inject(CourseCoreService);
  id = input<string>('');

  subMenu = [
    {
      name: 'Logout',
      link: '/',
      icon: 'input',
    },
  ];

  view: 'COURSE' | 'MODULE' | 'LESSON' | 'END' = 'COURSE';
  viewChat = true;
  currentLessonIndex = -1;
  currentModuleIndex = -1;

  _contents: any[] = [];

  course?: Course;
  modules: Module[] = [];
  lessons: Lesson[] = [];

  lectures: any[] = [];

  get currentModule() {
    return this.modules.at(this.currentModuleIndex);
  }

  source = this.service.courseSource;
  courseSource = computed(() => ({
    ...this.source.value(),
    progress: 0,
    status: 'IN_PROGRESS' as 'IN_PROGRESS' | 'PAST' | 'DUE' | 'COMPLETED',
    author: {
      name: 'LearnOrTeam Editor',
      email: 'help@learnorteam',
      bio: '',
      avatar:
        'https://images.pexels.com/photos/810775/pexels-photo-810775.jpeg',
    },
  }));

  tab = 1;
  extraTab = 1;

  tabs = computed(() => [
    {
      id: 1,
      name: 'Course Information',
    },
    {
      id: 2,
      name: 'Instructor Bio',
    },
    {
      id: 3,
      name: 'Resource',
    },
  ]);

  courseNav = computed(() =>
    (this.courseSource().modules ?? []).map(
      (m) =>
        ({
          id: m.id,
          name: m.name,
          icon: 'line_start_circle',
          type: 'MODULE',
          items: (m.lessons ?? []).map((l) => ({
            id: l.id,
            name: l.name,
            icon: 'line_end',
          })),
        } as NavigationItem)
    )
  );

  navigations = computed(() => {
    const items: NavigationItem[] = [
      {
        id: 'exit',
        name: 'Exit',
        icon: 'close',
        type: 'EXIT',
      },
      {
        id: 'LearnMate',
        name: 'LearnMate',
        icon: 'mark_unread_chat_alt',
        type: 'TOP',
      },
      {
        id: 'SkillQuest',
        name: 'SkillQuest',
        icon: 'assignment_add',
        type: 'TOP',
      },
    ];
    items.push(...this.courseNav());
    return items;
  });

  ngOnInit(): void {
    this.service.courseId.set(this.id());
    this.service.state.viewType.set('PREVIEW');
  }

  launch(course: Course & any) {
    this.course = course;
    this.modules = course.modules ?? [];
    this.currentModuleIndex = 0;
    this.view = 'MODULE';
  }

  continue(module: Module) {
    window.scrollTo(0, 0);
    this.lessons = module.lessons ?? [];
    this.currentLessonIndex = 0;
    this.viewChat = false;
    this.updateCurrentLecture();
    this.view = 'LESSON';
  }

  next() {
    window.scrollTo(0, 0);
    if (this.currentLessonIndex >= this.lessons.length - 1) {
      if (this.currentModuleIndex >= this.modules.length - 1) {
        this.view = 'END';
        return;
      }
      this.currentModuleIndex++;
      this.view = 'MODULE';
      return;
    }
    this.currentLessonIndex++;
    this.updateCurrentLecture();
  }

  updateCurrentLecture() {
    this.lectures = (
      this.lessons.at(this.currentLessonIndex)?.contents || []
    ).map(
      (c) =>
        ({
          ...c,
          lessonId: this.lessons[this.currentLessonIndex].id,
          lessonName: this.lessons[this.currentLessonIndex].name,
        } as any)
    );
  }

  gotTo(item: NavigationItem) {
    if (item.type === 'EXIT') {
      this.service.state.viewType.set('COURSE');
      this.router.navigate(['/lms/courses/view', this.id()]);
      return;
    }

    this.viewChat = ['LearnMate', 'SkillQuest'].includes(item.id);
  }

  back() {
    this.service.state.viewType.set('COURSE');
    this.router.navigate(['/lms/courses/view', this.id()]);
  }
  signOut() {}

  getTrack(item: any) {
    console.log('Widget track :: ', item);
  }

  ngOnDestroy(): void {
    this.service.state.viewType.set('COURSE');
  }
}
