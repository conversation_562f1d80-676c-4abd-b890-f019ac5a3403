<div class="flex flex-col gap-5 px-10 py-10 h-full">
  @if (['TEAM', 'GROUP'].includes(view)) {
  <h1 class="text-2xl font-semibold">
    {{ title }}
    @if (view === 'GROUP') {
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-blue underline italic"
      (click)="view = 'TEAM'"
    >
      {{ data.item?.name }}
    </a>
    }
  </h1>
  <ng-container *ngTemplateOutlet="teamGroup" />
  } @if (view === 'ASSIGN') {
  <ng-container *ngTemplateOutlet="assignTeam" />
  }@if (view === 'ASSIGN_GROUP') {
  <ng-container *ngTemplateOutlet="assignUsers" />
  }
</div>

<ng-template #teamGroup>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 py-5"
  >
    @if (view === 'TEAM') {
    <div class="form-lot-input min-h-32">
      <div class="field">
        <input type="text" id="name" formControlName="name" />
        <label for="name" [class.error]="f['name'].invalid && f['name'].dirty">
          Name your Team
        </label>
      </div>
      @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
      <div class="error">
        @if (f['name'].errors['required']) {
        <span>Team name is required</span>
        }
      </div>
      }
    </div>

    <div class="flex justify-end items-center gap-4">
      @if (!data.item?.id) {
      <button class="button-primary-outline w-fit" type="submit">
        Save Team and Close
      </button>
      <button
        class="button-primary w-fit py-1.5"
        type="button"
        (click)="saveGroupNew()"
      >
        Save and Create Group
      </button>
      } @if (data.item?.id) {
      <button class="button-primary w-fit" type="submit">
        Save Team and Close
      </button>
      }
    </div>
    } @if (view === 'GROUP') {
    <div class="flex flex-col" formArrayName="groups">
      @for (item of groups.controls; track $index; let i= $index; let last =
      $last) {
      <div
        [formGroupName]="i"
        class="flex justify-between items-center border-lot-gray py-1"
        [class.border-y]="i === 0"
        [class.border-b]="i !== 0"
      >
        <div class="flex flex-col px-2 w-full">
          <label for="{{ 'name' + i }}" class="sr-only">groupe name</label>
          <input
            [id]="'name' + i"
            type="text"
            formControlName="name"
            name="name"
            placeholder="New Group Name"
            autocomplete="new-password"
            class="bg-transparent rounded-md border-none px-5 py-2.5 w-full text-lot-dark placeholder:text-lot-dark-gray outline-none"
          />
          @if (item.get('error')?.value) {
          <span class="pl-2 text-xs text-lot-danger"
            >Group name is required</span
          >
          }
        </div>
        @if (last) {
        <button
          class="button-primary w-fit"
          (click)="addGroup(item)"
          type="button"
        >
          Add
        </button>
        } @if (!last) {
        <a
          href="javascript:void(0)"
          (click)="removeGroup(i)"
          class="text-lot-danger/70 hover:text-lot-danger text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
        }
      </div>
      }
    </div>
    <button class="button-primary w-32" type="submit" cdkFocusInitial>
      Save Group
    </button>
    }
  </form>
</ng-template>

<ng-template #assignTeam>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 py-5"
  >
    <h1 class="text-2xl font-semibold">
      Assign Team(s) to {{ data.user?.username }}
    </h1>
    <div class="flex flex-col mb-8 w-full min-h-64">
      <div class="grid grid-cols-2 gap-3 items-center">
        <div class="form-input">
          <label for="team">Select Team</label>
          <mat-select
            (valueChange)="onTeam($event, teamSource.value() || [], -1)"
          >
            <mat-option> -- </mat-option>
            @for (item of teamSource.value() || []; track item) {
            <mat-option [value]="item.id">
              {{ item.name }}
            </mat-option>
            }
          </mat-select>
        </div>
        <div class="form-input">
          <label for="group">Select Groups</label>
          <mat-select (valueChange)="onGroup($event, groupOptions)" multiple>
            <mat-option> -- </mat-option>
            @for (item of groupOptions; track item) {
            <mat-option [value]="item.id">
              {{ item.name }}
            </mat-option>
            }
          </mat-select>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-3 items-center mt-3">
        <div class="form-input">
          <label for="teams">Assigned Teams</label>
          <mat-chip-set aria-label="Groups selection">
            @for (team of userTeams; track team) {
            <mat-chip>
              <div class="flex items-center gap-1">
                <span>{{ team.teamName }}</span>
                <a
                  href="javascript:void(0)"
                  (click)="removeChips(team.teamId, 'TEAM')"
                >
                  <i class="fa-solid fa-circle-xmark"></i>
                </a>
              </div>
            </mat-chip>
            }@empty {
            <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
              >Not yet assigned</span
            >
            }
          </mat-chip-set>
        </div>

        <div class="form-input">
          <label for="teams">Assigned Groups </label>
          <mat-chip-set aria-label="Groups selection">
            @for (group of userGroups; track group) {
            <mat-chip>
              <div class="flex items-center gap-1">
                <span>{{ group.groupName }}</span>
                <a
                  href="javascript:void(0)"
                  (click)="removeChips(group.groupId, 'GROUP')"
                >
                  <i class="fa-solid fa-circle-xmark"></i>
                </a>
              </div>
            </mat-chip>
            }@empty {
            <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
              >Not yet assigned</span
            >
            }
          </mat-chip-set>
        </div>
      </div>
    </div>

    <div class="flex justify-end items-center gap-3">
      <button
        class="button-primary-outline w-fit"
        (click)="dialogRef.close()"
        type="button"
      >
        Cancel
      </button>
      <button
        class="button-primary w-fit py-1.5"
        type="button"
        (click)="onSubmitAssignTeamGroup()"
      >
        Save and Close
      </button>
    </div>
  </form>
</ng-template>

<ng-template #assignUsers>
  <div class="flex flex-col items-stretch gap-5 py-1 h-full min-h-full">
    <h1 class="text-2xl font-semibold">
      Assign Users to
      <span class="text-lot-blue italic">{{ data.item?.name }}</span>
    </h1>
    <div class="flex flex-col gap-3 mb-8 w-full h-full">
      <div class="flex justify-between items-center">
        <div
          class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-1"
        >
          <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
          <input
            type="search"
            name="search"
            id="search"
            [formControl]="userControl"
            [matAutocomplete]="userTmp"
            class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
            placeholder="Search Users"
          />
          <mat-autocomplete
            autoActiveFirstOption
            #userTmp="matAutocomplete"
            (optionSelected)="selectUser($event.option.value, 'ADD')"
            [displayWith]="displayFn"
          >
            @for (option of userOptions(); track option) {
            <mat-option [value]="option">{{ option.userName }}</mat-option>
            }
          </mat-autocomplete>
        </div>

        <div class="flex items-center gap-3">
          <button
            class="button-primary-outline w-fit"
            (click)="dialogRef.close()"
            type="button"
          >
            Cancel
          </button>
          <button
            class="button-primary w-fit"
            type="button"
            (click)="bulkAssignUsers()"
          >
            Save
          </button>
        </div>
      </div>

      <div class="flex-grow min-h-96 h-full">
        <div class="grid grid-cols-2 gap-10 items-center">
          <div class="flex flex-col gap-2 p-4 rounded-xl border h-full">
            <h4 class="text-lot-dark font-semibold mb-2">
              ({{ userGroups.length }}) - Assigned Users
            </h4>

            <ul
              class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
            >
              @for (item of userGroups; track item) {
              <li
                class="flex justify-between items-center gap-3 border-b py-2 group"
              >
                <span>{{ item.userName }}</span>
                <a
                  href="javascript:void(0)"
                  (click)="selectUser(item, 'REMOVE')"
                  class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
                >
                  <i class="fa-solid fa-trash"></i>
                </a>
              </li>
              } @empty {
              <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
              }
            </ul>
          </div>
          <div class="flex flex-col gap-2 p-4 rounded-xl border h-full">
            <h4 class="text-lot-dark font-semibold mb-2">
              ({{ users.length }}) - Ready for saving
            </h4>

            <ul
              class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
            >
              @for (item of users; track item) {
              <li
                class="flex justify-between items-center gap-3 border-b py-2 group"
              >
                <span>{{ item.userName }}</span>
                <div class="flex items-center gap-2">
                  <span
                    class="px-1 border rounded-md"
                    [class.border-lot-ai]="item.tag === 'new'"
                    [class.border-lot-danger]="item.tag === 'delete'"
                    [class.text-lot-ai]="item.tag === 'new'"
                    [class.text-lot-danger]="item.tag === 'delete'"
                    >{{ item.tag }}</span
                  >
                  <a
                    href="javascript:void(0)"
                    (click)="selectUser(item, 'CLEAR')"
                    class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
                  >
                    <i class="fa-solid fa-trash"></i>
                  </a>
                </div>
              </li>
              } @empty {
              <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
              }
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-template>
