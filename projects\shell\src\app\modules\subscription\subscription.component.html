<div
  class="relative h-screen bg-lot-dark-gray/50 flex justify-center items-center"
>
  <img
    src="assets/images/new/coffee.png"
    alt=""
    srcset=""
    class="absolute top-0 left-0"
  />
  <section
    class="relative w-[calc(100vw-80px)] max-w-7xl h-[calc(100vh-80px)] bg-white rounded-lg flex flex-col justify-center gap-5 items-center px-20 py-16"
  >
  @if(!isExpiringSoon){
        <div class="flex flex-col">
      @if( subscription.plan.name === 'TRIAL'){
      <h1 class="text-lot-blue text-3xl font-bold">
        Uh oh. Your 7 Days Trial is over!
      </h1>
      }@else{
      <h1 class="text-lot-blue text-3xl font-bold">
        Uh oh. Your subscription
        <span class="italic text-lot-dark underline">{{
          subscription.plan.name
        }}</span>
        is over!
      </h1>
      }
    </div>
  }@else{
    <div class="flex flex-col">
      <h1 class="text-lot-blue text-3xl font-bold">
        Only {{dayCount}} Days remain in your <span class="italic text-lot-dark underline">{{
          subscription.plan.name
        }}</span> Plan!
      </h1>
    </div>
  }

    <div class="flex flex-col justify-center items-center gap-8 w-full px-2">
      <div class="w-full flex flex-col justify-center items-center gap-8">
        <div class="flex flex-col justify-center items-center max-w-sm">
          <img
            src="assets/images/new/Illustration-strolling.png"
            alt=""
            srcset=""
            class="w-full"
          />
        </div>
        <div class="px-10 text-xl">
          @if(!isExpiringSoon){
            <p class="text-center text-lot-danger font-semibold">
            Expired on {{ subscription.expired_at | date : "longDate" }}
          </p>
          }
          @if(!isNotOwer){
          <p class="text-center">
            Purchase a plan to keep using <b>Learn Or Teach</b>
          </p>
          }@else{
          <p class="text-center">
            Please contact your administrator or account owner to extend or
            purchase a subscription.
          </p>
          }
        </div>
      </div>
      @if(!isNotOwer){
      <button
        type="button"
        (click)="subscribe()"
        class="button-primary w-fit py-4 px-6"
      >
        Subscribe to Learn Or Teach
      </button>
      }
      <div
        class="flex justify-center items-center border-t-2 pt-8 w-full max-w-sm"
      >
        <button
          type="button"
          (click)="signOut()"
          class="button-primary-outline w-fit py-1"
        >
          Sign out
        </button>
      </div>
    </div>
  </section>
  <img
    src="assets/images/new/sitting-reading.png"
    alt=""
    srcset=""
    class="absolute bottom-0 right-0"
  />
  <button
    class="absolute bg-white bottom-10 right-20 z-30 rounded-lg border border-lot-ai text-lot-ai-dark px-5 py-2 flex items-center gap-2"
  >
    <span class="material-symbols-outlined"> help </span>
    Need any Help?
  </button>
</div>
