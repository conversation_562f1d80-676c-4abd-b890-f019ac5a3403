import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { catchError, lastValueFrom, Observable, of } from 'rxjs';
import { GlobalStateService } from './global-state.service';
import dayjs from 'dayjs';

@Injectable({ providedIn: 'root' })
export class FileStorageService {
  state = inject(GlobalStateService);
  private http = inject(HttpClient);

  bucket = 'avatar';

  async upload(bucket: string, path: string, file: File | string) {
    const { data, error } = await this.state.supabase.storage
      .from(bucket)
      .upload(path, file);
    return { data, error };
  }

  async download(bucket: string, path: string) {
    const { data, error } = await this.state.supabase.storage
      .from(bucket)
      .download(path);
    return { data, error };
  }

  async checkBucketExists(name: string) {
    const bRes = await this.getBucket(name);
    if (bRes.error) {
      return { data: null, error: bRes.error };
    }
    if (bRes.data) {
      return { data: bRes.data.name, error: null };
    }
    const res = await this.createBucket(name);
    return { data: res.data, error: res.error };
  }

  async createBucket(name: string) {
    return await this.state.supabase.storage.createBucket(name, {
      public: false,
    });
  }
  async getBucket(name: string) {
    return await this.state.supabase.storage.getBucket(name);
  }

  async uploadFile(fileData: File, folderName: string): Promise<string> {
    const formData = new FormData();
    formData.append(
      'file',
      fileData,
      `${folderName}/${dayjs().format('yyyy-mm-dd') + '_' + fileData.name}`
    );
    const regex = /^(https?:\/\/)?([\w.-]+)\.([a-z]{2,6})(\/[\w.-]*)*\/?$/i;
    try {
      const resFile = await fetch(
        `${this.state.envConfig.env.apiEnpointConfig.UPLOAD}/Upload`,
        {
          method: 'POST',
          body: formData,
        }
      );
      const url = await resFile.text();
      return regex.test(url) ? url : '';
    } catch (error) {
      return '';
    }
  }

  deleteFile(fileName: string): Observable<string> {
    return this.http.delete<string>(
      `${this.state.envConfig.env.apiEnpointConfig.UPLOAD}/deleteFile/${fileName}`
    );
  }

  async getFileFromUrl(url: string) {
    const fileName = url.split('/').pop();
    const response = await fetch(url);
    const data = await response.blob();
    return new File([data], fileName ?? 'dumbFile', {
      type: response.headers.get('content-type') ?? '',
    });
  }
}
