import { Component, Input, EventEmitter, Output } from '@angular/core';
import { WidgetType } from '../ui-block.utils';
import { UIBlockFlashcardComponent } from './flashcard.component';

@Component({
  selector: 'app-block-flashcards',
  imports: [UIBlockFlashcardComponent],
  template: `
    <div
      class="flex flex-wrap justify-center h-full rounded-lg {{ metaClass }}"
      (click)="view.emit(content)"
    >
      @for (card of content.cards; track $index) {
      <app-block-flashcard
        [content]="card"
        class="max-w-[450px] mx-4"
        (view)="view.emit($event)"
      />
      }
    </div>
  `,
})
export class UIBlockFlashcardsComponent {
  @Input() content: {
    cards: {
      front: {
        type: WidgetType;
        content: any;
      };
      back: {
        type: WidgetType;
        content: any;
      };
      showButton?: boolean;
      meta: {
        width?: number;
        height?: number;
        padding?: number;
        margin?: number;
        background?: string;
        color: string;
      };
    }[];
    column?: number;
    requirePass: boolean;
    meta: {
      spacing?: number;
      padding?: number;
      margin?: number;
    };
  };
  @Output() view = new EventEmitter();

  get metaClass() {
    const meta = {
      // column: !this.content?.cards?.length
      //   ? 'grid-cols-1'
      //   : 'grid-cols-' + this.content?.cards?.length,
      width: !this.content?.meta?.spacing
        ? 'gap-10'
        : 'gap-' + this.content?.meta?.spacing,
      padding: !this.content?.meta?.padding
        ? 'p-0'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
    };
    return Object.values(meta).join(' ');
  }
}

export const getFlashcardsTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Flashcards Block',
        cards: [
          {
            front: {
              content: {
                heading: '🗓️ Vacation & Personal Time',
                description: 'When should I request time off?',
              },
              type: WidgetType.TEXT,
            },
            back: {
              content: {
                description: ` <b>✅ At least 3 business days in advance.</b><br/>
 This gives your manager time to plan and ensures team coverage.`,
              },
              type: WidgetType.TEXT,
            },
          },
          {
            front: {
              content: {
                heading: '🤒 Sick Leave Protocol',
                description: 'What should I do if I wake up feeling sick?',
              },
              type: WidgetType.TEXT,
            },
            back: {
              content: {
                description: `<b>📲 Notify your supervisor ASAP</b>, then submit your sick leave in the HR Portal.
 Timely updates help keep everyone in the loop.`,
              },
              type: WidgetType.TEXT,
            },
          },
        ],
        meta: {
          column: 2,
          spacing: 4,
          padding: 2,
        },
      };
    default:
      return {
        uiLabel: 'Flashcards Block',
        cards: [
          {
            front: {
              content: {
                url: 'https://picsum.photos/800/450',
                caption: '🤒 Sick Leave Protocol',
                description: 'What should I do if I wake up feeling sick?',
                position: 'center',
                textPosition: 'bottom',
              },
              type: WidgetType.IMAGE,
            },
            back: {
              component: {
                description: `<b>📲 Notify your supervisor ASAP</b>, then submit your sick leave in the HR Portal.
 Timely updates help keep everyone in the loop.`,
              },
              type: WidgetType.TEXT,
            },
          },
        ],
      };
  }
};
