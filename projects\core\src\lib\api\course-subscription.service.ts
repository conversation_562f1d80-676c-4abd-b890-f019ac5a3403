import { Injectable, inject } from '@angular/core';
import {
  ApiRespone,
  Course,
  UserItem,
  CourseSubscription,
} from '../models';
import { Subject } from 'rxjs';
import { GlobalStateService } from '../services';
import { getId } from '../utils';

export type CourseSubscriptionRequest = {
  news: CourseSubscription[];
  deletes: CourseSubscription[];
};

const tableName = 'course_subscriptions';

@Injectable({ providedIn: 'root' })
export class CourseSubscriptionService {
  state = inject(GlobalStateService);

  get user(): UserItem {
    return this.state.user();
  }

  currentCourse: Course | null;

  buttonEvent = new Subject<string>();

  get buttonEvent$() {
    return this.buttonEvent.asObservable();
  }

  async getByOrgId(id: string): Promise<ApiRespone<CourseSubscription>> {
    const { data, error } = await this.state.supabase
      .from(tableName)
      .select('*, organization(*), course(*)')
      .eq('organization', id);
    return {
      data: <CourseSubscription[]>data ?? [],
      error: error?.message,
    };
  }

  async getCoursesByOrgId(): Promise<ApiRespone<Course>> {
    const orgId = getId(this.user.organization);
    const { data, error } = await this.state.supabase
      .from('courses_view')
      .select('*')
      .eq('organization', orgId);
    return {
      data: data as Course[],
      error: error?.message,
    };
  }

  async save({
    news,
    deletes,
  }: CourseSubscriptionRequest): Promise<ApiRespone<CourseSubscription>> {
    const user = this.user;
    if (!user) {
      return { data: [], error: 'User data not found.' };
    }

    const req = this.state.supabase.from(tableName);
    for (const item of deletes) {
      await req
        .delete()
        .eq('course', getId(item.course))
        .eq('organization', getId(item.organization));
    }
    const newItems = news.map((x) => {
      return {
        ...x,
        course: getId(x.course),
        createdName: user.firstname + ' ' + user.lastname,
        created_by: user.id,
        organization: getId(x.organization),
      } as CourseSubscription;
    });
    const { data, error } = await req.insert(newItems).select('*');
    return {
      data: <CourseSubscription[]>data ?? [],
      error: error?.message,
    };
  }
}
