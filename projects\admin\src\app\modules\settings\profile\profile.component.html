<div class="flex flex-col">
  @if (tab === 1) {
  <ng-container *ngTemplateOutlet="profile" />
  <div class="flex justify-between items-start gap-8 w-full border-t pt-10">
    <span class="text-xl">Change Password</span>
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-blue underline text-2xl"
      (click)="changePassword()"
    >
      <i class="fa-solid fa-pen-to-square"></i>
    </a>
  </div>
  } @if (tab === 2) {
  <ng-container *ngTemplateOutlet="password" />
  }
</div>

<ng-template #profile>
  <div class="flex justify-between items-start gap-8 w-full">
    <div class="flex-grow">
      <div class="flex gap-10 py-16 w-full">
        <div class="size-44 rounded-full focus:outline-none relative">
          <img
            [src]="imageUrl"
            alt=""
            srcset=""
            class="w-full h-full object-cover rounded-full"
          />
          @if (isLoading) {
          <mat-progress-bar mode="indeterminate" />
          } @if (error) {
          <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
          }
          @if (!isLoading) {
          <a
            href="javascript:void(0)"
            class="absolute bottom-0 right-0 size-10 rounded-full bg-lot-light-blue flex justify-center items-center"
          >
            <span class="material-symbols-outlined text-lot-dark text-3xl">
              upload
            </span>
            <input
              type="file"
              accept="image/*"
              (change)="onFileChange($event)"
              class="absolute inset-0 opacity-0 cursor-pointer"
            />
          </a>
          }
        </div>
        <div class="flex-grow">
          <div class="flex flex-col gap-5 w-full px-2">
            <div class="grid grid-cols-2 gap-3 items-center">
              <div class="form-input">
                <span class="text-lot-dark-gray/70">First Name</span>
                <span class="text-lot-dark">{{ user.firstname }}</span>
              </div>
              <div class="form-input">
                <span class="text-lot-dark-gray/70">Last Name</span>
                <span class="text-lot-dark">{{ user.lastname }}</span>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-3 items-center">
              <div class="form-input">
                <span class="text-lot-dark-gray/70">Phone Number</span>
                <span class="text-lot-dark">{{ user.phone }}</span>
              </div>
              <div class="form-input">
                <span class="text-lot-dark-gray/70">Email Address</span>
                <span class="text-lot-dark">{{ user.email }}</span>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-3 items-center">
              <div class="form-input">
                <span class="text-lot-dark-gray/70">Job Title</span>
                <span class="text-lot-dark">{{ user.email }}</span>
              </div>
              <div class="form-input w-full">
                <label for="role">Permission Role</label>
                <span class="text-lot-dark">{{ user.role }}</span>
              </div>
            </div>
            <div class="flex flex-col border-t pt-4 w-full">
              <div class="flex justify-between items-center border-b pb-2">
                <span class="text-sm text-lot-blue italic">
                  Assigned to: {{ userTeams.length }} team(s) |
                  {{ userGroups.length }}
                  group(s)
                </span>
              </div>
              <div class="grid grid-cols-2 gap-3 items-center mt-3">
                <div class="form-input">
                  <label for="teams">Assigned Teams</label>
                  <mat-chip-set aria-label="Groups selection">
                    @for (team of userTeams; track team) {
                    <mat-chip>
                      {{ team.teamName }}
                    </mat-chip>
                    }@empty {
                    <span class="pl-5 italic text-lot-dark-gray/90"
                      >Not yet assigned</span
                    >
                    }
                  </mat-chip-set>
                </div>

                <div class="form-input">
                  <label for="teams">Assigned Groups </label>
                  <mat-chip-set aria-label="Groups selection">
                    @for (group of userGroups; track group) {
                    <mat-chip>
                      {{ group.groupName }}
                    </mat-chip>
                    }@empty {
                    <span class="pl-5 italic text-lot-dark-gray/90"
                      >Not yet assigned</span
                    >
                    }
                  </mat-chip-set>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-blue underline text-2xl"
      (click)="editUser()"
    >
      <i class="fa-solid fa-pen-to-square"></i>
    </a>
  </div>
</ng-template>

<ng-template #password>
  <div class="flex flex-col gap-4 px-10 py-20 rounded-3xl">
    <h2 class="text-2xl font-bold text-center text-lot-dark mb-6">
      Change Password
    </h2>
    <app-password-form (data)="onSubmitPassword($event)" />
  </div>
</ng-template>
