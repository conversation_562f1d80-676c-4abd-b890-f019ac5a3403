import { inject, Injectable } from '@angular/core';
import { loadStripe, Stripe, StripeError } from '@stripe/stripe-js';
import {
  BillingPlan,
  getExpiredAt,
  getId,
  GlobalStateService,
  Organization,
  OrgSubscription,
  SubscriptionStatus,
  tryPromise,
} from '@lms/core';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  state = inject(GlobalStateService);

  stripe: Stripe;

  get stripePromise() {
    return loadStripe(this.state.envConfig.env.stripePublishableKey);
  }

  contructor() {
    loadStripe(this.state.envConfig.env.stripePublishableKey).then((stripe) => {
      this.stripe = stripe!;
    });
  }

  async proceedPayment(payload: {
    amount: number;
    currency: string;
    cardElement: any;
    stripe: Stripe;
    organization: Organization;
    plans: BillingPlan[];
  }) {
    // const [errorPayment, res] = await tryPromise(
    //   fetch(serverUrl + '/create-payment-intent', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json', // Specify the content type as JSON
    //     },
    //     body: JSON.stringify({
    //       amount: payload.amount,
    //       currency: payload.currency,
    //     }),
    //   }).then((response) => response.json())
    // );

    const { data: res, error: errorPayment } =
      await this.state.supabase.functions.invoke<{
        data: any;
        error: string;
      }>('billing/create-payment-intent', {
        body: JSON.stringify({
          amount: payload.amount,
          currency: payload.currency,
        }),
      });

    if (errorPayment || !res?.data) {
      return {
        error: (errorPayment?.message ?? res?.error) as string,
      };
    }
    const clientSecret = res?.data;
    if (!clientSecret) {
      return {
        error: 'No client secret',
      };
    }
    const { error, paymentIntent } = await payload.stripe.confirmCardPayment(
      clientSecret,
      {
        payment_method: {
          card: payload.cardElement,
          billing_details: {
            name: payload.organization.name,
          },
        },
        receipt_email:
          payload.organization.email ?? payload.organization.contact?.email,
      }
    );

    if (error) {
      return {
        error: this.handlePaymentError(error),
      };
    }

    if (paymentIntent?.status === 'succeeded') {
      await this.saveSubscription(
        payload.plans,
        payload.amount,
        getId(payload.organization)
      );
    }

    return {
      data: paymentIntent,
      error: null,
    };
  }

  private handlePaymentError(error: StripeError) {
    switch (error.type) {
      case 'card_error':
      case 'validation_error':
        return error.message;
      case 'invalid_request_error':
        return 'Invalid payment request. Please try again.';

      case 'api_connection_error':
        return 'Network error. Please check your connection.';

      case 'api_error':
        return 'Payment service unavailable. Please try again later.';

      default:
        return 'Payment failed. Please try again.';
    }
  }

  async updateSubscribeOrganization(customerId: string) {
    const mySub = this.state.mySubscription();
    const { data, error } = await this.state.supabase
      .from('org_subscription')
      .update({
        paymentId: customerId,
      })
      .eq('id', mySub.id)
      .select('*');

    this.state.mySubscription.set({
      ...mySub,
      paymentId: customerId,
    });

    return {
      data: (data ?? []) as OrgSubscription[],
      error: error?.message,
    };
  }

  async verifyCustomer(email: string) {
    // const [error, res] = await tryPromise(
    //   fetch(serverUrl + '/verify-customer', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify({ email }),
    //   }).then((response) => response.json())
    // );
    // return {
    //   data: res?.data,
    //   error: (error?.message ?? res?.error) as string,
    // };

    const { data: res, error } = await this.state.supabase.functions.invoke<{
      data: any;
      error: string;
    }>('billing/verify-customer', {
      body: JSON.stringify({ email }),
    });

    return {
      data: res?.data,
      error: error as string,
    };
  }

  async createCustomer(payload: Organization) {
    const { data: res, error } = await this.state.supabase.functions.invoke<{
      data: any;
      error: string;
    }>('billing/create-customer', {
      body: JSON.stringify({
        email: payload.email ?? payload.contact?.email,
        name: payload.name,
        phone: payload.phone ?? payload.contact?.phone,
        metadata: {
          description: payload.description,
          contact: payload.contact?.name,
        },
      }),
    });
    if (res?.data) {
      await this.updateSubscribeOrganization(getId(res.data.id));
    }
    return {
      data: res?.data,
      error: (error?.message ?? res?.error) as string,
    };
  }

  async createSubscription(payload: {
    org: Organization;
    customerId?: string;
    cardElement: any;
    plans: BillingPlan[];
    stripe: Stripe;
  }) {
    
    const plan = payload.plans.filter((p) => p.type === 'PLAN')[0];
    // const [error, res] = await tryPromise(
    //   fetch(serverUrl + '/create-subscription', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify({
    //       customerId,
    //       priceId,
    //     }),
    //   }).then((response) => response.json())
    // );
    let customerId = payload.customerId;

    if (!customerId) {
      const { data, error } = await this.createCustomer(payload.org);
      customerId = data?.id;
      if (error || !customerId) {
        return {
          error: (error ?? 'Unable to create customer') as string,
        };
      }
    }

    const { paymentMethod, error: errorMessage } =
      await payload.stripe.createPaymentMethod({
        type: 'card',
        card: payload.cardElement,
      });

    if (errorMessage) {
      return {
        error: this.handlePaymentError(errorMessage),
      };
    }

    const { data: res, error } = await this.state.supabase.functions.invoke<{
      data: {
        subscription: any;
        requiresAction: boolean;
      };
      error: string;
    }>('billing/create-subscription', {
      body: JSON.stringify({
        customerId,
        priceId: plan.priceId,
        paymentMethodId: paymentMethod.id,
        frequency: plan.frequency,
      }),
    });
    if (error || !res?.data?.subscription) {
      return {
        error: (error ?? 'Unable to create subscription') as string,
      };
    }

    if (!res?.data?.requiresAction) {
      return {
        error: (res?.error ?? 'No bill required payment.') as string,
      };
    }

    const clientSecret =
      res.data.subscription.latest_invoice.payment_intent.client_secret;

    const { error: confirmError } = await payload.stripe.confirmPayment({
      clientSecret,
      confirmParams: {
        return_url: window.location.href,
      },
    });

    if (confirmError) {
      return {
        error: this.handlePaymentError(confirmError),
      };
    }
    await this.saveSubscription(
      payload.plans,
      plan.price * 100,
      getId(payload.org)
    );
    return {
      data: {
        status: 'succeeded',
        amount: plan.price * 100,
        created: new Date().getTime(),
        id: res.data.subscription.id,
      },
      error: null,
    };
  }

  async getSubscriptions(customerId: string) {
    // const [error, res] = await tryPromise(
    //   fetch(`${serverUrl}/subscriptions/${customerId}`).then((response) =>
    //     response.json()
    //   )
    // );
    const { data: res, error } = await this.state.supabase.functions.invoke<{
      data: any;
      error: string;
    }>(`billing/subscriptions/${customerId}`);
    return {
      data: res?.data,
      error: (error?.message ?? res?.error) as string,
    };
  }

  async getBillingHistory(customerId: string) {
    if (!customerId) return { data: [] };
    // const { data: res, error } = await this.state.supabase.functions.invoke<{
    //   data: any;
    //   error: string;
    // }>(`billing/billing-history/${customerId}`);
    //***************************************** */
    // const [error, res] = await tryPromise(
    //   fetch(`http://localhost:8000/billing-history/${customerId}`).then(
    //     (response) => response.json()
    //   )
    // );
    const [error, res] = await tryPromise(
      fetch(`http://localhost:8000/billing-history`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ customerId }),
      }).then((response) => response.json())
    );
    return {
      data: res?.data ?? [],
      error: (error?.message ?? res?.error) as string,
    };
  }

  async unsubscribe(subscriptionId: string) {
    // const [error, res] = await tryPromise(
    //   fetch(serverUrl + '/unsubscribe', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //     },
    //     body: JSON.stringify({
    //       subscriptionId,
    //     }),
    //   }).then((response) => response.json())
    // );
    const { data: res, error } = await this.state.supabase.functions.invoke<{
      data: any;
      error: string;
    }>(`billing/unsubscribe`, {
      body: JSON.stringify({ subscriptionId }),
    });
    return {
      data: res?.data,
      error: (error?.message ?? res?.error) as string,
    };
  }

  async saveSubscription(
    plans: BillingPlan[],
    paidAmount: number,
    orgnaizationId: string
  ) {
    const request = plans.filter((p) => p.type === 'PLAN')[0];
    const addOns = plans.filter((p) => p.type === 'ADDON');

    const payload = {
      organization: orgnaizationId,
      plan: request.id,
      expired_at: getExpiredAt(request.frequency),
      seat_max: request.countMax,
      current: 1,
      status: SubscriptionStatus.ACTIVE,
      purchaseAmount: paidAmount / 100,
      addOns: addOns,
    };

    await this.state.supabase
      .from('org_subscription')
      .update({
        current: 0,
        expired_at: new Date().toISOString(),
        status: SubscriptionStatus.DEACTIVATED,
      })
      .eq('organization', orgnaizationId)
      .select('*');

    const { data, error } = await this.state.supabase
      .from('org_subscription')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []) as OrgSubscription[],
      error: error?.message,
    };
  }

  savePrice(plan: BillingPlan) {
    if (plan.priceId) {
      return this.updatePrice(plan);
    } else {
      return this.addPrice(plan);
    }
  }

  async updatePrice(plan: BillingPlan) {
    const [error, res] = await tryPromise(
      fetch(`http://localhost:8000/update-price`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: plan.price,
          priceId: plan.priceId,
          active: 'true',
          nickname: plan.name,
        }),
      }).then((response) => response.json())
    );
    return {
      data: res?.data ?? [],
      error: (error?.message ?? res?.error) as string,
    };
  }
  async addPrice(plan: BillingPlan) {
    const [error, res] = await tryPromise(
      fetch(`http://localhost:8000/create-price`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: plan.price,
          currency: 'usd',
          recurring: {
            interval: plan.frequency.toLowerCase().replace('ly', ''),
            interval_count: 1,
          },
          product: plan.id,
          nickname: plan.name,
        }),
      }).then((response) => response.json())
    );

    if (res?.data) {
      await this.state.supabase
        .from('billing_plans')
        .update({
          priceId: res.data.id,
        })
        .eq('id', plan.id)
        .select('*');
    }

    return {
      data: res?.data ?? [],
      error: (error?.message ?? res?.error) as string,
    };
  }
}
