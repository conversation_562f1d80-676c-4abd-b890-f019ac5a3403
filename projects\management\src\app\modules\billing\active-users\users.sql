
CREATE OR R<PERSON>LACE FUNCTION get_subscription_users(filter json)
RETURNS json SECURITY DEFINER AS $function$
DECLARE 
    user_records record;
    count_record int;
    team_group_record record;
    activity_filter text;
BEGIN
    activity_filter := filter->>'activity';

    WITH filtered_users AS (
        SELECT ur.*, au.last_sign_in_at
        FROM public.users ur 
        INNER JOIN auth.users au ON au.id = ur.id
        WHERE ur.organization::text = filter->>'organization' AND
            CASE 
                WHEN (activity_filter)::text = 'active30' THEN 
                    au.last_sign_in_at >= NOW() - INTERVAL '30 days'
                WHEN (activity_filter)::text = 'activePass' THEN 
                    au.last_sign_in_at < NOW() - INTERVAL '30 days'
                WHEN (activity_filter)::text = 'activeNone' THEN 
                    au.last_sign_in_at IS NULL
                ELSE TRUE -- no activity filter
            END
    )
    SELECT COUNT(*) INTO count_record FROM filtered_users;

    CREATE TEMP TABLE temp_users (
        id uuid,
        email text,
        firstname text,
        lastname text,
        role text,
        title text,
        phone text,
        organization uuid,
        last_sign_in_at timestamp with time zone
    );
   -- Insert data into the temporary table 
   INSERT INTO temp_users (
        id,
        email,
        firstname,
        lastname,
        role,
        title,
        phone,
        organization,
        last_sign_in_at
    )
    SELECT cr.*
    FROM (
        SELECT 
            ur.id,
            ur.email,
            ur.firstname,
            ur.lastname,
            ur.role,
            ur.title,
            ur.phone,
            ur.organization,
            au.last_sign_in_at
        FROM public.users ur 
        INNER JOIN auth.users au ON au.id = ur.id
        WHERE ur.organization::text = filter->>'organization' AND
            CASE 
                WHEN (activity_filter)::text = 'active30' THEN 
                    au.last_sign_in_at >= NOW() - INTERVAL '30 days'
                WHEN (activity_filter)::text = 'activePass' THEN 
                    au.last_sign_in_at < NOW() - INTERVAL '30 days'
                WHEN (activity_filter)::text = 'activeNone' THEN 
                    au.last_sign_in_at IS NULL
                ELSE TRUE -- no activity filter
            END
        ORDER BY au.last_sign_in_at DESC NULLS LAST
        LIMIT (filter->>'size')::bigint 
        OFFSET (filter->>'page')::bigint
    ) cr;

    SELECT json_agg(
        json_build_object(
            'id', t.id,
            'email', t.email,
            'firstname', t.firstname,
            'lastname', t.lastname,
            'role', t.role,
            'title', t.title,
            'phone', t.phone,
            'organization', t.organization,
            'last_sign_in_at', t.last_sign_in_at
        )
    ) INTO user_records
    FROM temp_users t;

    SELECT array_agg(
        json_build_object(
            'userGroupId', cr."userGroupId", 
            'userId', cr."userId", 
            'teamId', cr."teamId", 
            'teamName', cr."teamName", 
            'groupId', cr."groupId", 
            'groupName', cr."groupName"
            )
    ) INTO team_group_record
    FROM (
        SELECT 
            ug.id as "userGroupId",
            ug.user as "userId",
            tm.id as "teamId",
            tm.name as "teamName",
            gm.id as "groupId",
            gm.name as "groupName"
        FROM public.user_groups ug
            LEFT JOIN temp_users tu on tu.id = ug.user
            LEFT JOIN public.teams tm on tm.id = ug.team
            LEFT JOIN public.groups gm on gm.id = ug.group
    ) AS cr;

    DROP TABLE temp_users;

    RETURN json_build_object(
        'total', count_record,
        'users', user_records,
        'team_groups', team_group_record
    );
END;
$function$
LANGUAGE plpgsql;

