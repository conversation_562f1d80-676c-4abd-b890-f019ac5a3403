@if (!team) {
<app-resource-loader [source]="source" />
<app-team-list [data]="source.value() || []" (onItem)="action($event)" />
} @if (team && !group) {
<div class="flex flex-col">
  <a
    href="javascript:void(0)"
    class="flex items-center gap-3 text-lot-blue"
    (click)="goBack()"
  >
    <i class="fa-solid fa-arrow-left"></i> Back to All Teams
  </a>
  <div
    class="flex justify-between items-center pb-2 border-b-2 border-lot-blue mb-4"
  >
    <h4 class="text-lot-dark text-2xl font-bold">
      {{ team.name }}
    </h4>
    <div class="flex items-center gap-3">
      <span class="italic">
        {{
          type === "users"
            ? users.length
              ? users.length + " users total"
              : 0 + " user"
            : team.groups?.length
            ? team.groups?.length + " groups total"
            : 0 + " group"
        }}
      </span>
      @if (type === 'users') {
      <a
        href="javascript:void(0)"
        (click)="openUserAssigner()"
        matTooltip="Assign Users"
        class="border border-lot-ai rounded-md px-3 py-1 bg-lot-ai/70 hover:text-lot-dark/60 font-semibold"
      >
        <i class="fa-solid fa-user-plus"></i>
        Assign Users
      </a>
      }
    </div>
  </div>
  @if (type === 'users') {
  <app-user-list
    [data]="users"
    type="TEAM"
    (onItem)="actionGroup($event, 'USER')"
  />
  } @if (type !== 'users') {
  <app-team-list
    [data]="groups"
    [isTeam]="false"
    (onItem)="actionGroup($event, 'GROUP')"
  />}
</div>
} @if (team && group) {
<div class="flex flex-col">
  <a
    href="javascript:void(0)"
    class="flex items-center gap-3 text-lot-blue"
    (click)="goBack()"
  >
    <i class="fa-solid fa-arrow-left"></i> Back to All Teams
  </a>
  <div
    class="flex justify-between items-center border-b-2 border-lot-blue pb-2 mb-1"
  >
    <div class="flex items-center gap-3">
      <h4 class="text-lot-dark text-2xl font-bold">
        {{ team.name }}
      </h4>
      <span> > </span>
      <h4 class="text-lot-dark text-2xl font-bold border-b-2 border-lot-blue">
        {{ group.name }}
      </h4>
    </div>

    <div class="flex items-center gap-3">
      <span class="italic">
        {{
          group.users?.length
            ? group.users?.length + " users total"
            : 0 + " user"
        }}
      </span>
      @if (type === 'users') {
      <a
        href="javascript:void(0)"
        (click)="openUserAssigner()"
        matTooltip="Assign Users"
        class="border border-lot-ai rounded-md px-3 py-1 bg-lot-ai/70 hover:text-lot-dark/60 font-semibold"
      >
        <i class="fa-solid fa-user-plus"></i>
        Assign Users
      </a>
      }
    </div>
  </div>
  <app-user-list
    [data]="users"
    type="TEAM"
    (onItem)="actionGroup($event, 'USER')"
  />
</div>
}
