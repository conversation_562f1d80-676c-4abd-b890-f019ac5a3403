import { MatIconModule } from '@angular/material/icon';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MatDialogModule,
  MatDialog,
} from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { NgTemplateOutlet } from '@angular/common';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { markControlsDirty, CourseCoreService, stripHtml } from '@lms/core';
import { CourseNewFormComponent } from '../new-form/new-form.component';
import { Router } from '@angular/router';
import { QuillEditorComponent } from 'ngx-quill';

@Component({
  selector: 'app-upload-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatSelectModule,
    NgTemplateOutlet,
    MatChipsModule,
    MatIconModule,
    MatProgressBarModule,
    QuillEditorComponent,
  ],
  templateUrl: 'upload-form.component.html',
  styles: [
    `
      :host {
        --mdc-linear-progress-active-indicator-color: #2e7ddb;
        --mdc-linear-progress-active-indicator-height: 14px;
        --mdc-linear-progress-track-height: 14px;
      }
    `,
  ],
})
export class UploadScormFormComponent {
  readonly service = inject(CourseCoreService);
  readonly router = inject(Router);
  readonly dialog = inject(MatDialog);
  public dialogRef: MatDialogRef<UploadScormFormComponent> =
    inject(MatDialogRef);

  view: 'NONE' | 'UPLOAD' = 'NONE';

  isLoading = false;
  error?: string;

  editorStyle = {
    height: '250px',
    backgroundColor: '#ffffff',
    border: 'none',
  };

  form = new FormGroup({
    title: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl('', Validators.required),
  });

  scormFile: File | null = null;

  get f() {
    return this.form.controls;
  }

  addNewUser() {
    this.dialog.open(CourseNewFormComponent, {
      minWidth: '800px',
      data: {
        type: 'ADD',
        item: null,
      },
    });
    this.dialogRef.close();
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    const { file, error } = this.onFileLoad(input);
    this.scormFile = file;
    this.error = error;
    if (file) {
      this.readAndValidateFile(file);
    }
  }

  readAndValidateFile(file: File): void {}

  async onSubmit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    if (!this.scormFile) {
      this.error = 'Please upload a SCORM file.';
      return;
    }

    this.isLoading = true;

    const payload = {
      name: this.form.value.title,
      description: this.form.value.description,
      cover: 'assets/images/new/card-3.jpeg',
      short: stripHtml(this.form.value.description!)?.substring(0, 250),
      creator: {
        name: this.service.user.firstname + ' ' + this.service.user.lastname,
        email: this.service.user.email,
        bio: 'Course Creator',
        avatar: this.service.user.avatar,
      },
    } as any;

    const resScorm = await this.service.scormService.uploadScorm(this.scormFile!, payload.id);

    if (resScorm.error) {
      this.error = resScorm.error;
      this.isLoading = false;
      return;
    }

    const res = await this.service.add(payload);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      return;
    }
    this.router.navigate(['/lms/courses/view', res.data]);
    this.dialogRef.close();
  }

  onFileLoad(input: HTMLInputElement) {
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      if (
        file.type === 'application/x-zip-compressed' ||
        file.name.endsWith('.zip')
      ) {
        return { file };
      } else {
        return { file: null, error: 'Please upload a valid ZIP file.' };
      }
    }
    return { file: null, error: 'Please select a file.' };
  }
}
