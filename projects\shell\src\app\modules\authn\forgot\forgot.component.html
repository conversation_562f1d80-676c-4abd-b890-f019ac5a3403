@if (!submitted) {
<a
  href="javascript:void(0)"
  routerLink="/sign-in"
  class="absolute top-5 left-5 text-lot-blue text-lg flex items-center gap-2"
>
  <span class="material-symbols-outlined"> arrow_back_ios_new </span>
  Back to Login</a
>
<div class="flex flex-col justify-center items-center">
  <h1 class="text-lot-blue">Forgot your password?</h1>
  <p class="text-lot-dark text-center">Happens to the best of us.</p>
</div>
<form
  [formGroup]="form"
  (ngSubmit)="onSubmit()"
  class="flex flex-col justify-center items-center gap-8 w-full px-2 mt-10"
>
  <div class="w-full flex flex-col gap-2">
    <div class="px-5 min-w-sm w-[450px] max-w-[450px]">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="email"
            type="email"
            formControlName="email"
            autocomplete="on"
            placeholder="<EMAIL>"
          />
          <label
            for="email"
            [class.error]="
              form.controls['email'].invalid && form.controls['email'].dirty
            "
          >
            Email
          </label>
        </div>
        <app-validation-text controlName="email" />
        <app-validation-text controlName="email" validator="email">
          Invalid Email.
        </app-validation-text>
      </div>
    </div>
  </div>
  <button type="submit" class="button-primary w-fit py-1.5 px-6">
    Reset Password
  </button>

  <div class="flex justify-center items-center border-t-2 pt-8 w-full">
    <button type="button" class="button-primary-outline w-fit py-1">
      Not a User? Register here!
    </button>
  </div>
</form>
} @if (submitted) {
<div class="w-full flex flex-col gap-5 items-center">
  <h1 class="text-lot-blue text-center">Password reset link sent!</h1>
  <div class="flex flex-col">
    <img
      src="assets/images/new/Illustration-strolling.png"
      alt=""
      srcset=""
      class=""
    />
  </div>
  <p class="text-center text-lot-dark px-10">
    Just click on the link in the email you just received to reset your
    password.
  </p>
  <p class="text-center text-lot-dark px-10">
    If that doesn’t work contact your admin.
  </p>
</div>
}
