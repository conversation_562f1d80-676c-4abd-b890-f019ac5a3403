import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { Environment } from '../environment';
import { UserItem } from '../models';
import { GlobalStateService } from '../services';

@Injectable({
  providedIn: 'root',
})
export class EmailService {
  private state: GlobalStateService = inject(GlobalStateService);
  get emailTemplate() {
    return this.state.envConfig.env.emailTemplate;
  }

  get user(): UserItem {
    return this.state.user();
  }

  get learnerApp() {
    return this.state.envConfig.env.learnerAppUrl;
  }

  sendEmail(payload: {
    email?: string;
    templateId: string;
    data: { [key: string]: any };
  }) {
    return firstValueFrom(
      this.state.http.post(
        `${this.state.envConfig.env.apiEnpointConfig.EMAIL}/sendEmailWithTemplate`,
        {
          email: payload.email ?? this.user?.email,
          templateId: payload.templateId,
          templateData: {
            ...payload.data,
            receiverName: payload.data['receiverName'] ?? this.user?.firstname,
          },
        }
      )
    );
  }
}
