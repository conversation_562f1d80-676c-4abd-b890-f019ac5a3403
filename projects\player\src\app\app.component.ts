import { Component, HostListener, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from './header/header.component';
import { PlayerService } from './services/app.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, HeaderComponent],
  template: `
    <app-header />
    <router-outlet />
  `,
})
export class AppComponent {
  service = inject(PlayerService);
  // @HostListener('window:beforeunload', ['$event'])
  // beforeunloadHandler(event: any) {
  //   this.service.logExist();
  //   window.confirm('Are you sure you want to leave?');

  //   event.preventDefault();
  //   event.returnValue = 'Your data will be lost!';
  //   return false;
  // }
}
