import {
  Component,
  inject,
  Input,
  OnInit,
  TemplateRef,
  viewChild,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  Course,
  InstructorLedSession,
  LearningInstructor,
  LearningInstructorService,
  markControlsDirty,
  ToastMessageType,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import {
  ExpandableComponent,
  SortByOrder,
  ValidationTextComponent,
} from '@lms/shared';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { CourseRequisiteComponent } from './requites/requites.component';
import { MatTimepickerModule } from '@angular/material/timepicker';
import { Router } from '@angular/router';
import { provideNativeDateAdapter } from '@angular/material/core';
import { v4 as gUID } from 'uuid';

@Component({
  selector: 'app-instructor-builder',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatProgressBarModule,
    ExpandableComponent,
    SortByOrder,
    NgTemplateOutlet,
    DatePipe,
    MatDatepickerModule,
    MatTimepickerModule,
    ValidationTextComponent,
  ],
  providers: [provideNativeDateAdapter()],
  templateUrl: 'builder.component.html',
})
export class BuilderComponent implements OnInit {
  readonly service = inject(LearningInstructorService);
  readonly dialog = inject(MatDialog);
  readonly router = inject(Router);
  formRef = viewChild<TemplateRef<any>>('formTemplate');

  @Input({ required: true }) data: LearningInstructor;

  isLoading = false;
  error?: string;
  viewCourse?: Course;
  minDate = new Date();
  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    id: new FormControl(''),
    instructor: new FormControl('', Validators.required),
    location: new FormControl('-', Validators.required),
    type: new FormControl('VIRTUAL', Validators.required),
    classSize: new FormControl(1, Validators.required),
    startDate: new FormControl(new Date(), Validators.required),
    endDate: new FormControl(new Date(), Validators.required),
    startTime: new FormControl(new Date()),
    endTime: new FormControl(new Date()),
    url: new FormControl(''),
  });

  dialogRef?: MatDialogRef<any>;

  get typeLabel() {
    return this.data.type === 'LEARNINGPATH'
      ? 'Learning Path'
      : 'Instructor Led';
  }

  get isLearningPath() {
    return this.data.type === 'LEARNINGPATH';
  }

  get f() {
    return this.form.controls;
  }

  items: {
    category: string;
    order: number;
    courses: (Course & {
      order: number;
    })[];
  }[] = [];

  prerequisites: (Course & {
    order: number;
  })[] = [];

  sessions: InstructorLedSession[] = [];
  resources: {
    name: string;
    url: string;
  }[] = [];

  // courses: {
  //   id: string;
  //   name: string;
  //   cover: string;
  //   short: string;
  // }[] = [];

  ngOnInit(): void {
    this.items = this.data.items || [];
    this.prerequisites = this.data.prerequisites || [];
    this.sessions = this.data.sessions || [];
    this.resources = this.data.resources || [];
  }

  goToCourse(id: string) {
    this.router.navigate(['/lms/courses/view', id]);
  }

  addSession() {
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '900px',
    });
  }

  editSession(id: string) {
    const session = this.sessions.find((x) => x.id === id);
    if (!session) return;
    this.form.patchValue({
      id: session.id,
      name: session.name,
      instructor: session.instructor,
      location: session.location,
      type: session.type,
      classSize: session.classSize,
      startDate: new Date(session.startDate),
      endDate: new Date(session.endDate),
      startTime: new Date(session.startDate),
      endTime: new Date(session.endDate),
      url: session.url,
    });

    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '900px',
    });
  }

  async removeSession(id: string) {
    this.sessions = this.sessions.filter((x) => x.id !== id);
    await this.save({
      id: this.data.id,
      sessions: this.sessions,
    } as any);
  }

  addResource() {}

  addCourse(category?: string) {
    this.viewCourse = undefined;
    const ids = this.isLearningPath
      ? this.items
          .find((i) => i.category === category)
          ?.courses.map((x) => x.id) || []
      : this.prerequisites.map((x) => x.id);
    this.dialog
      .open(CourseRequisiteComponent, {
        minWidth: '400px',
        data: {
          category,
          ids,
          categories: this.items.map((x) => x.category),
          showCategory: this.isLearningPath,
        },
      })
      .afterClosed()
      .subscribe((res) => {
        this.refreshCourse(res.courses, category, res.category);
      });
  }

  async removeCourse(category: string, courseId: string) {
    this.viewCourse = undefined;
    if (this.isLearningPath) {
      this.items = this.items.map((x) => ({
        ...x,
        courses:
          x.category === category
            ? x.courses.filter((m) => m.id !== courseId)
            : x.courses,
      }));
      await this.save({
        id: this.data.id,
        items: this.items,
      } as any);
      return;
    }
    this.prerequisites = this.prerequisites.filter((m) => m.id !== courseId);
    await this.save({
      id: this.data.id,
      prerequisites: this.prerequisites,
    } as any);
  }

  async refreshCourse(
    courses: Course[],
    category?: string,
    newCategory?: string
  ) {
    if (!courses.length) return;
    if (this.isLearningPath) {
      if (!category) {
        this.items = [
          ...this.items,
          {
            category: newCategory || '',
            order: this.items.length + 1,
            courses: courses.map((x: Course, i: number) => ({
              ...x,
              order: i + 1,
            })),
          },
        ];
      } else {
        const categoryItem = this.items.find((i) => i.category === category);
        if (!categoryItem) return;
        categoryItem.category = newCategory || categoryItem.category;
        categoryItem.courses = [...(categoryItem?.courses ?? []), ...courses]
          .map((x: Course, i: number) => ({
            ...x,
            order: i + 1,
          }))
          .filter((v, i, s) => s.findIndex((t) => t.id === v.id) === i);
        this.items = this.items.map((x) =>
          x.category === category ? categoryItem : x
        );
      }
      await this.save({
        id: this.data.id,
        items: this.items,
      } as any);
      return;
    }
    this.prerequisites = [...this.prerequisites, ...courses]
      .map((x: Course, i: number) => ({
        ...x,
        order: i + 1,
      }))
      .filter((v, i, s) => s.findIndex((t) => t.id === v.id) === i);

    await this.save({
      id: this.data.id,
      prerequisites: this.prerequisites,
    } as any);
  }

  async save(item: any) {
    const res = await this.service.update(item);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: this.isLearningPath
        ? 'Learning Path'
        : 'Instructor Led' + ' Saved successfully',
      type: ToastMessageType.SUCCESS,
    });
  }

  async onSubmit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    const startDate = this.form.value.startDate as Date;
    const endDate = this.form.value.endDate as Date;

    startDate.setHours((this.form.value.startTime as Date).getHours());
    startDate.setMinutes((this.form.value.startTime as Date).getMinutes());

    endDate.setHours((this.form.value.endTime as Date).getHours());
    endDate.setMinutes((this.form.value.endTime as Date).getMinutes());

    if (startDate > endDate) {
      this.error = 'Start date time must be before end date time';
      return;
    }

    this.sessions.push({
      id: gUID(),
      name: this.form.value.name!,
      instructor: this.form.value.instructor!,
      location: this.form.value.location!,
      type: this.form.value.type! as 'PHYSICAL' | 'VIRTUAL',
      classSize: this.form.value.classSize! || 1,
      startDate: startDate.toISOString()!,
      endDate: endDate.toISOString()!,
      order: this.sessions.length + 1,
      url: this.form.value.url!,
    });

    await this.save({
      id: this.data.id,
      sessions: this.sessions,
    } as any);
    this.dialogRef?.close();
    this.form.reset();
    this.error = undefined;
  }
}
