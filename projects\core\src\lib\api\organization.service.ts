import { Injectable, inject } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { ApiRespone, Organization, UserItem } from '../models';
import { GlobalStateService } from '../services';

const tableName = 'organizations';

@Injectable({
  providedIn: 'root',
})
export class OrganizationService {
  state = inject(GlobalStateService);

  get user(): UserItem {
    return this.state.user();
  }
  get organization() {
    return this.state.user().organization;
  }

  async get(query = null): Promise<ApiRespone<Organization>> {
    const req = this.state.supabase.from(tableName).select('*');
    if (query) {
      req.or(`name.ilike.%${query}%,description.ilike.%${query}%`);
    }
    const { data, error } = await req;
    return {
      data: <Organization[]>data ?? [],
      error: error?.message,
    };
  }

  async delete(id: string): Promise<ApiRespone<Organization>> {
    const { data, error } = await this.state.supabase
      .from(tableName)
      .delete()
      .eq('id', id);
    return {
      data: error ? [] : ([{ id }] as any),
      error: error?.message,
    };
  }

  async saveOrganization(
    payload: Partial<Organization>
  ): Promise<ApiRespone<Organization>> {
    const user = this.user;
    if (!user) {
      return { data: [], error: 'User data not found.' };
    }
    const req = this.state.supabase.from(tableName);

    if (payload.id) {
      const { id, ...request } = {
        ...payload,
        updated_by: user.id,
        updated_at: new Date().toISOString(),
      };
      const { data, error } = await req
        .update(request)
        .eq('id', payload.id)
        .select('*');
      this.state.user.set({
        ...user,
        organization: {
          ...user.organization,
          ...data?.at(0),
        },
      });
      return {
        data: data ?? [],
        error: error?.message,
      };
    }
    payload = {
      ...payload,
      created_by: user.id,
    };
    const { data, error } = await req.insert(payload).select('*');
    return {
      data: data ?? [],
      error: error?.message,
    };
  }

  async createOrganization(
    payload: Partial<Organization>
  ): Promise<ApiRespone<Organization>> {
    const sUser = this.user;
    const options = await this.state.getOptions();
    if (!options) {
      return {
        data: [],
        error: 'Invalid Token',
      };
    }
    try {
      const res = await lastValueFrom(
        this.state.http.post<ApiRespone<Organization>>(
          `${this.state.envConfig.env.lmsCoreApi}organization/create`,
          { ...payload, created_by: sUser?.id, createdName: sUser?.username },
          options
        )
      );
      return res.error
        ? {
            data: [],
            error: res.error,
          }
        : { data: res.data };
    } catch (error: any) {
      return {
        data: [],
        error: 'Error: ' + error.message,
      };
    }
  }
  async updateOrganization(
    payload: Partial<Organization>
  ): Promise<ApiRespone<Organization>> {
    const sUser = this.user;
    const options = await this.state.getOptions();
    if (!options) {
      return {
        data: [],
        error: 'Invalid Token',
      };
    }
    try {
      const res = await lastValueFrom(
        this.state.http.put<ApiRespone<Organization>>(
          `${this.state.envConfig.env.lmsCoreApi}organization/update/${payload.id}`,
          { ...payload, updated_by: sUser?.id, updatedName: sUser?.username },
          options
        )
      );
      return res.error
        ? {
            data: [],
            error: res.error,
          }
        : { data: res.data };
    } catch (e: any) {
      return {
        data: [],
        error: e.message,
      };
    }
  }
}
