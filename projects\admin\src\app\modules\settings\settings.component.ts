import { Component, OnInit } from '@angular/core';
import { RouterLink, RouterOutlet } from '@angular/router';

@Component({
  selector: 'app-settings',
  imports: [RouterOutlet, RouterLink],
  templateUrl: './settings.component.html',
})
export class SettingsComponent implements OnInit {
  tab = 1;

  tabs = [
    {
      id: 1,
      name: 'Account Settings',
      path: '/lms/settings',
    },
    {
      id: 2,
      name: 'Profile Settings',
      path: '/lms/settings/profile',
    },
    // {
    //   id: 3,
    //   name: 'Application Settings',
    //   path: '/lms/settings/application',
    // },
    // {
    //   id: 4,
    //   // name: 'Notification Settings',
    //   name: 'Media Center Settings',
    //   path: '/lms/settings/media',
    // },
  ];
  imageUrl = 'assets/images/user-profile.jpg';

  ngOnInit(): void {
    this.tab = window.location.pathname.includes('profile') ? 2 : 1;
  }
  getImage(url: string) {
    console.log(url);
  }
}
