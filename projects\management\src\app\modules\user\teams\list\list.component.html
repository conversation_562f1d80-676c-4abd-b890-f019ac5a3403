<table class="w-full divide-y divide-gray-300">
  <thead>
    <tr>
      <th
        scope="col"
        class="py-3.5 pr-3 pl-4 text-left text-sm font-normal italic text-lot-dark-gray sm:pl-3"
      >
        Name
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Number of Users
      </th>
      <!-- <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Self Registration
      </th> -->
      <th scope="col" class="relative py-3.5 pr-4 pl-3 sm:pr-3"></th>
    </tr>
  </thead>
  <tbody class="bg-white">
    @for (item of data; track $index) {
    <tr class="even:bg-gray-50 group">
      <td
        class="py-4 pr-3 pl-4 text-base font-medium whitespace-nowrap text-lot-dark sm:pl-3"
      >
        {{ item.name }}
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.users?.length || 0 }}
      </td>
      <!-- <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.link }}
      </td> -->
      <td
        class="py-4 pr-4 pl-3 text-sm sm:pr-3 flex items-center justify-end gap-8"
      >
        @if (isTeam) {
        <a
          href="javascript:void(0)"
          (click)="action('view', item)"
          matTooltip="View Groups"
          class="text-lot-dark-gray/30 group-hover:text-lot-dark/70 hover:text-lot-dark text-xl"
        >
          <i class="fa-solid fa-users-rectangle"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="action('users', item)"
          matTooltip="View Users"
          class="text-lot-dark-gray/30 group-hover:text-lot-ai-dark/70 hover:text-lot-ai-dark text-xl"
        >
          <i class="fa-solid fa-users"></i>
        </a>
        } @if (!isTeam) {
        <a
          href="javascript:void(0)"
          (click)="action('users', item)"
          matTooltip="View Users"
          class="text-lot-dark-gray/30 group-hover:text-lot-dark/70 hover:text-lot-dark text-xl"
        >
          <i class="fa-solid fa-users"></i>
        </a>
        }
        <a
          href="javascript:void(0)"
          (click)="action('edit', item)"
          matTooltip="Edit"
          class="text-lot-dark-gray/30 group-hover:text-lot-blue/70 hover:text-lot-blue text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="action('delete', item)"
          matTooltip="Delete"
          class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </td>
    </tr>
    }
  </tbody>
</table>
