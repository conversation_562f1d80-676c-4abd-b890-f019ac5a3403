import { Routes } from '@angular/router';
import { ShellComponent } from './shell/shell.component';
import { canActivate<PERSON><PERSON><PERSON>or<PERSON>earner } from '@lms/core';

export const routes: Routes = [
  {
    path: '',
    canActivate: [canActivateAuthFor<PERSON>earner],
    canActivateChild: [canActivateAuth<PERSON>or<PERSON>earner],
    component: ShellComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/home/<USER>').then((m) => m.HomeComponent),
      },
      {
        path: 'course-library',
        loadComponent: () =>
          import('./pages/catalog/catalog.component').then(
            (m) => m.CatalogComponent
          ),
      },
      {
        path: 'transcript',
        loadComponent: () =>
          import('./pages/transcript/transcript.component').then(
            (m) => m.TranscriptComponent
          ),
      },
      {
        path: 'training/:id',
        loadChildren: () => 
          import('./pages/training/training.routes').then((m) => m.TrainingRoutes),
        // loadComponent: () =>
        //   import('./pages/training/training.component').then(
        //     (m) => m.TrainingComponent
        //   ),
      },
      {
        path: 'training/:id/:pathId',
        loadComponent: () =>
          import('./pages/training/training.component').then(
            (m) => m.TrainingComponent
          ),
      },
      {
        path: 'learning-path/:id',
        loadComponent: () =>
          import('./pages/learning-path/learning-path.component').then(
            (m) => m.LearningPathComponent
          ),
      },
      {
        path: 'instructor-led/:id',
        loadComponent: () =>
          import('./pages/instructor-led/instructor-led.component').then(
            (m) => m.InstructorLedComponent
          ),
      },
      {
        path: 'skillQuest',
        loadComponent: () =>
          import('./pages/skillquest/skillquest.component').then(
            (m) => m.SkillquestComponent
          ),
      },
      {
        path: 'settings',
        loadComponent: () =>
          import('./pages/profile/profile.component').then(
            (m) => m.ProfileComponent
          ),
      },
    ],
  },
  {
    path: '**',
    redirectTo: '/',
  },
];

export const LEARNER_ROUTES: Routes = [
  {
    path: '',
    component: ShellComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/home/<USER>').then((m) => m.HomeComponent),
      },
      {
        path: 'course-library',
        loadComponent: () =>
          import('./pages/catalog/catalog.component').then(
            (m) => m.CatalogComponent
          ),
      },
      {
        path: 'transcript',
        loadComponent: () =>
          import('./pages/transcript/transcript.component').then(
            (m) => m.TranscriptComponent
          ),
      },
      {
        path: 'training/:id',
        loadChildren: () =>
          import('./pages/training/training.routes').then(
            (m) => m.TrainingRoutes
          ),
        // loadComponent: () =>
        //   import('./pages/training/training.component').then(
        //     (m) => m.TrainingComponent
        //   ),
      },
      {
        path: 'training/:id/:pathId',
        loadComponent: () =>
          import('./pages/training/training.component').then(
            (m) => m.TrainingComponent
          ),
      },
      {
        path: 'learning-path/:id',
        loadComponent: () =>
          import('./pages/learning-path/learning-path.component').then(
            (m) => m.LearningPathComponent
          ),
      },
      {
        path: 'instructor-led/:id',
        loadComponent: () =>
          import('./pages/instructor-led/instructor-led.component').then(
            (m) => m.InstructorLedComponent
          ),
      },
      {
        path: 'skillQuest',
        loadComponent: () =>
          import('./pages/skillquest/skillquest.component').then(
            (m) => m.SkillquestComponent
          ),
      },
      {
        path: 'settings',
        loadComponent: () =>
          import('./pages/profile/profile.component').then(
            (m) => m.ProfileComponent
          ),
      },
    ],
  }
];
