import { Routes } from '@angular/router';
import { AppComponent } from './app.component';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./main/main.component').then((m) => m.MainComponent),
  },
  {
    path: ':token/:id/:source',
    loadComponent: () =>
      import('./main/main.component').then((m) => m.MainComponent),
  },
];


export const PLAY_ROUTES: Routes = [
  {
    path: '',
    component: AppComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./main/main.component').then((m) => m.MainComponent),
      },
      {
        path: ':token/:id/:source',
        loadComponent: () =>
          import('./main/main.component').then((m) => m.MainComponent),
      },
    ],
  },
];
