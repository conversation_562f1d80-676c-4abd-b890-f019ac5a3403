import { <PERSON><PERSON><PERSON>cyPipe, DatePipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { MatPaginatorModule } from '@angular/material/paginator';
import { OrgSubscription } from '@lms/core';

@Component({
  selector: 'app-billing-history',
  imports: [MatPaginatorModule, DatePipe, CurrencyPipe],
  templateUrl: './history.component.html',
})
export class BillingHistoryComponent {
  @Input() data: OrgSubscription[] = [];

}
