import { Component, inject, OnInit, resource, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { AccountStatus, UsersCoreService } from '@lms/core';
import { ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-course-co-writer',
  imports: [FormsModule, ResourceHeaderComponent],
  templateUrl: './co-writer.component.html',
})
export class CourseCoWriterComponent implements OnInit {
  dialogRef: MatDialogRef<CourseCoWriterComponent> = inject(MatDialogRef);
  service = inject(UsersCoreService);

  filter = signal<string | undefined>(undefined);

  userSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      const { data, error, count } = await this.service.getUserOnlyByQuery(
        request,
        [AccountStatus.ADMINISTRATOR, AccountStatus.MANAGER]
      );
      if (error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return {
        data: data.map((x) => ({
          ...x,
          selected: false,
        })),
        count,
      };
    },
  });

  users: { id: string; name: string; image: string; selected: boolean }[] = [];

  ngOnInit(): void {
    this.filter.set('');
    this.userSource.reload();
  }

  onSelectionChange(checked: boolean, item: any) {
    if (checked) {
      this.users.push({
        id: item.id,
        name: item.name,
        image: item.image,
        selected: true,
      });
    } else {
      this.users = this.users.filter((user) => user.id !== item.id);
    }
  }

  saveSelection() {
    this.dialogRef.close({
      users: this.users,
    });
  }
}
