-- FUNCTION: public.get_last_sign_in_date()

-- DROP FUNCTION IF EXISTS public.get_last_sign_in_date();

CREATE OR REPLACE FUNCTION public.get_last_sign_in_date()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
 begin

   IF new.payload::json->>'action'::varchar = 'login' THEN
         update public.users set "last_sign_in_at" = NOW() where id = CAST(new.payload::json->>'actor_id'::varchar AS uuid);
   END IF;

   return new;
end; 
$BODY$;

ALTER FUNCTION public.get_last_sign_in_date()
    OWNER TO postgres;

GRANT EXECUTE ON FUNCTION public.get_last_sign_in_date() TO PUBLIC;

GRANT EXECUTE ON FUNCTION public.get_last_sign_in_date() TO anon;

GRANT EXECUTE ON FUNCTION public.get_last_sign_in_date() TO authenticated;

GRANT EXECUTE ON FUNCTION public.get_last_sign_in_date() TO postgres;

GRANT EXECUTE ON FUNCTION public.get_last_sign_in_date() TO service_role;


-- Create the trigger
CREATE TRIGGER update_last_sign_in_date
    AFTER UPDATE
    ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.get_last_sign_in_date();



    WITH filtered_users AS (
        SELECT ur.*, au.last_sign_in_at
        FROM public.users ur 
        INNER JOIN auth.users au ON au.id = ur.id
        WHERE ur.organization::text = filter->>'organization'
        AND CASE 
            WHEN activity_filter = 'active30' THEN 
                au.last_sign_in_at >= NOW() - INTERVAL '30 days'
            WHEN activity_filter = 'activePass' THEN 
                au.last_sign_in_at < NOW() - INTERVAL '30 days'
            WHEN activity_filter = 'activeNone' THEN 
                au.last_sign_in_at IS NULL
            ELSE TRUE -- no activity filter
        END
    )
    SELECT COUNT(*) INTO count_record FROM filtered_users;