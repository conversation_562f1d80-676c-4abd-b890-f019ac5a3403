# Usage of AI edge functions

Quick tips on how to call the AI Edge functions from **Supabase**.

All endpoints take the headers:
```ts
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer <logged-in-user-token>' \
```

## Text generation

### body

#### URL
```
https://ebongdaiwwvolarwnhvk.supabase.co/functions/v1/ai-texgen
```
#### Payload
```ts
// Payload
interface ITexgen {
  query: string;
  module_id?: string;
  course_id?: string;
  backend?: "xai" | "oai";
}
```
#### Response
```ts
interface ResponseData{
    data?: string[];
    error?: [{code:  string, message: string}]
}
```

## Image generation

### body

#### URL
```
https://ebongdaiwwvolarwnhvk.supabase.co/functions/v1/ai-imgen
```
#### Payload
```ts
// Payload
interface IImgen {
  query: string;
  module_id?: string;
  course_id?: string;
  backend?: "xai" | "oai";
}
```
#### Response
```ts
interface ResponseData{
    data?: string[]; // base64 encoded image file
    error?: [{code:  string, message: string}]
}
```