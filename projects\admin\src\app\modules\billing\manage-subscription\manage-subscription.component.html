<div class="flex flex-col">
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  }
  <div class="flex justify-between items-center">
    <h2 class="text-xl font-semibold mb-4">My CurrentSubscription Plan</h2>
  </div>
  @if (error) {
  <p class="text-red-500">{{ error }}</p>
  }

  <div class="flex justify-between gap-12">
    @if (plan) {
    <div
      class="cursor-pointer flex justify-center flex-col items-start flex-1 rounded-xl p-6 gap-0 w-[258px] h-60 hover:bg-lot-light-blue/30 hover:border border-lot-blue transition-colors duration-200 ease-linear bg-lot-light-blue/10 border"
    >
      <div class="flex justify-between w-full">
        <span class="text-lot-blue text-2xl">
          <i class="{{ plan.icon }}"></i>
        </span>
        <span class="text-green-600 text-2xl">
          <i class="fa-solid fa-circle-check"></i>
        </span>
      </div>
      <div class="flex justify-between w-full">
        <div class="flex flex-col items-start gap-3">
          <h2 class="text-2xl font-bold text-lot-dark">{{ plan.name }}</h2>
          <p class="text-sm">
            Up to
            {{ plan.countMax }}
            users
          </p>
          <div class="flex flex-col gap-1 mt-10">
            <p class="text-base text-lot-dark font-semibold">
              {{ plan.price | currency }}
            </p>
            <span class="text-lot-dark-gray text-base">
              {{
                plan.frequency === "MONTHLY"
                  ? "Billed monthly"
                  : "Billed annually"
              }}
            </span>
          </div>
        </div>

        <div class="flex flex-col">
          <span class="text-lg"
            >Start Date: {{ subscription.created_at | date : "longDate" }}</span
          >
          <span class="text-lg text-lot-ai-dark font-semibold"
            >Expires: {{ subscription.expired_at | date : "longDate" }}</span
          >
          <span
            class="text-xl"
            [class.text-lot-danger]="remaingSeats <= 1"
            [class.text-lot-dark-gray]="remaingSeats > 1"
            >{{ subscription.used_seat }}/{{ subscription.plan.countMax }}
            seats remaining
          </span>
        </div>
      </div>
    </div>
    }
  </div>
  <div class="mt-8">
    <h2 class="text-xl font-semibold mb-4">Billing History</h2>
    <app-resource-loader [source]="historySource" />
    <table class="min-w-full bg-white border border-gray-300">
      <thead>
        <tr class="bg-gray-200">
          <th class="px-4 py-2 border">Date</th>
          <th class="px-4 py-2 border">Amount</th>
          <th class="px-4 py-2 border">Status</th>
        </tr>
      </thead>
      <tbody>
        @for (invoice of historySource.value() || []; track invoice.id){
        <tr class="hover:bg-gray-50">
          <td class="px-4 py-2 border">{{ invoice.created * 1000 | date }}</td>
          <td class="px-4 py-2 border">${{ invoice.amount_due / 100 }}</td>
          <td class="px-4 py-2 border">{{ invoice.status }}</td>
        </tr>
        } @empty {
        <tr>
          <td colspan="3" class="text-center py-4">No billing history found</td>
        </tr>
        }
      </tbody>
    </table>
  </div>
  <div class="mt-8">
    <h2 class="text-xl font-semibold mb-4">Subscriptions</h2>
    <app-resource-loader [source]="subscriptionSource" />
    <table class="min-w-full bg-white border border-gray-300">
      <thead>
        <tr class="bg-gray-200">
          <th class="px-4 py-2 border">Plan</th>
          <th class="px-4 py-2 border">Status</th>
          <th class="px-4 py-2 border">Actions</th>
        </tr>
      </thead>
      <tbody>
        @for (subscription of subscriptionSource.value() || []; track
        subscription.id){
        <tr class="hover:bg-gray-50">
          <td class="px-4 py-2 border">{{ subscription.plan.nickname }}</td>
          <td class="px-4 py-2 border">{{ subscription.status }}</td>
          <td class="px-4 py-2 border">
            <button
              (click)="unsubscribe(subscription.id)"
              class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              Unsubscribe
            </button>
          </td>
        </tr>
        } @empty {
        <tr>
          <td colspan="3" class="text-center py-4">
            No subscription history found
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>
</div>
