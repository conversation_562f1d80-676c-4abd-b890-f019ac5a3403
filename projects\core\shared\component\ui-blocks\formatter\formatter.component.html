<div class="max-w-lg w-full mx-auto p-6 bg-white rounded-xl shadow-md">
  <div class="flex justify-between items-center gap-2 border-b pb-2 mb-2">
    <span>Format</span>
    <a href="javascript:void(0)" (click)="close()">
      <i class="fa-solid fa-xmark text-xl"></i>
    </a>
  </div>

  <div class="flex flex-col gap-2">
    <div class="flex flex-col">
      <h3 class="text-sm font-medium mb-2">Content width</h3>
      <div class="inline-flex rounded-md shadow-xs w-full">
        @for (item of ['S', 'M', 'L', 'XL']; track $index; let last=$last; let
        first=$first) {
        <button
          type="button"
          (click)="update(item, 'width')"
          class="py-3 px-4 inline-flex items-center gap-x-2 -mt-px -ms-px text-sm font-medium focus:z-10 border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-lot-blue focus:text-white"
          [class.rounded-s-lg]="first"
          [class.rounded-e-lg]="last"
        >
          {{ item }}
        </button>
        }
      </div>
    </div>

    <div class="flex flex-col">
      <h3 class="text-sm font-medium mb-2">Padding</h3>
      <div class="inline-flex rounded-md shadow-xs w-full">
        @for (item of ['S', 'M', 'L', 'XL']; track $index; let last=$last; let
        first=$first) {
        <button
          type="button"
          (click)="update(item, 'padding')"
          class="py-3 px-4 inline-flex items-center gap-x-2 -mt-px -ms-px text-sm font-medium focus:z-10 border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-lot-blue focus:text-white"
          [class.rounded-s-lg]="first"
          [class.rounded-e-lg]="last"
        >
          {{ item }}
        </button>
        }
      </div>
    </div>
  </div>

  <div class="mb-6 w-full mt-2">
    <!-- <h3 class="text-sm font-medium mb-2">Background color</h3> -->
    <div class="flex flex-col gap-3 rounded-md shadow-xs w-full">
      <div>
        <label for="hs-color-input" class="block text-sm font-medium mb-2">
          Pick Background Color
        </label>
        <input
          type="color"
          class="p-1 h-10 w-14 block bg-white border border-gray-200 cursor-pointer rounded-lg disabled:opacity-50 disabled:pointer-events-none"
          id="hs-color-input"
          (change)="updateColor($any($event.target).value, 'bg')"
          [value]="currentBgColor"
          title="Choose your color"
        />
      </div>

      <div>
        <label for="hs-color-input" class="block text-sm font-medium mb-2">
          Pick Text Color
        </label>
        <input
          type="color"
          class="p-1 h-10 w-14 block bg-white border border-gray-200 cursor-pointer rounded-lg disabled:opacity-50 disabled:pointer-events-none"
          id="hs-color-input"
          (change)="updateColor($any($event.target).value, 'text')"
          [value]="currentColor"
          title="Choose your color"
        />
      </div>

    </div>
  </div>
</div>
