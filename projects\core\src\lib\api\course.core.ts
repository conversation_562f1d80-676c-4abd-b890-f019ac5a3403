import { inject, Injectable, resource, signal } from '@angular/core';
import {
  Course,
  Enrollment,
  EnrollmentView,
  getId,
  GlobalStateService,
  Lesson,
  mapCollection,
  Module,
  Organization,
  ScormCloundService,
  TeamGroupItemView,
  tryPromise,
  UserItem,
  UserTeamGroupItem,
} from '@lms/core';
import { CourseQuery } from '../queries';
import { OverlayRef } from '@angular/cdk/overlay';
import { Subject } from 'rxjs';
import { LessonQuery } from '../queries/lesson.query';

type CourseFilter = {
  payload: {
    query?: string;
    sorting?: string[];
    type: 'MY' | 'LIBRARY' | 'TEMPLATE';
  };
  paging: {
    page: number;
    size: number;
  };
};

@Injectable({
  providedIn: 'root',
})
export class CourseCoreService {
  state = inject(GlobalStateService);
  scormService = inject(ScormCloundService);

  filter = signal<CourseFilter | undefined>(undefined);

  viewType = signal<
    'COURSE' | 'LEARNINGPATH' | 'MODULE' | 'SETTINGS' | 'LESSON'
  >('COURSE');

  totalCount = signal<number>(0);
  courseId = signal<string | undefined>(undefined);

  formatterData?: {
    index: number;
    content: any;
    overlayRef?: OverlayRef;
  };
  formatterTrigger = new Subject<{
    index: number;
    content: any;
    overlayRef?: OverlayRef;
  }>();

  enrollTrigger = new Subject<{
    type: 'COURSE' | 'LESSON' | 'SETTINGS' | 'PREVIEW';
    course: Course;
  }>();

  get user(): UserItem {
    return this.state.user();
  }
  get organization(): Organization {
    return this.state.user().organization as Organization;
  }

  coursesSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request) {
        return {
          data: [] as Course[],
          total: 0,
        };
      }
      const res = await this.getCourses(request);
      if (res.error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return {
        data: res.data,
        total: res.count,
      };
    },
  });

  courseSource = resource({
    request: () => this.courseId(),
    loader: async ({ request }) => {
      if (!request) {
        return;
      }
      const [error, res] = await tryPromise(this.queryCourse(request));
      if (error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return res.at(0);
    },
  });

  async getCourses(filter: CourseFilter) {
    if (filter.payload.type === 'MY') {
      const req = this.state.supabase
        .from('courses')
        .select('*', { count: 'exact' })
        .eq('organization', getId(this.user?.organization))
        .range(0, 10);

      if (filter.payload.query?.trim()?.length) {
        req.or(
          `title.ilike.%${filter.payload.query}%,description.ilike.%${filter.payload.query}%,objective.ilike.%${filter.payload.query}%,subject.ilike.%${filter.payload.query}%`
        );
      }
      const res = await req;
      return {
        data: (res.data || []) as Course[],
        count: res.count ?? 0,
        error: res?.error?.message,
      };
    }

    if (['LIBRARY', 'TEMPLATE'].includes(filter.payload.type)) {
      const res = await this.state.supabase
        .from('course_subscriptions')
        .select('*, course!inner(*)', { count: 'exact' })
        .eq('organization', getId(this.user?.organization))
        .or(`course->>"type".ilike.%${filter.payload.type}%`)
        .range(0, 10);

      return {
        data: (res.data || []).map((x) => x.course) as Course[],
        count: res.count ?? 0,
        error: res?.error?.message,
      };
    }

    return {
      data: [] as Course[],
      count: 0,
      error: 'Invalid filter',
    };
  }

  async getOne(id: string): Promise<{ data?: Course; error?: string }> {
    const res = await this.state.supabase
      .from('courses')
      .select('*, organization(*)')
      .eq('id', id)
      .limit(1)
      .single();
    if (res.data) {
      return { data: <Course>res.data };
    }
    return {
      error: res.error?.message,
    };
  }

  async queryCourse(id: string) {
    const { data } = await this.state.graphql.rawRequest(CourseQuery, {
      organizationId: getId(this.user?.organization),
      id,
    });
    if (!data) {
      return [] as Course[];
    }
    const items = mapCollection(data, 'coursesCollection').map(
      (x) =>
        ({
          ...x,
          coWriters:
            typeof x['coWriters'] === 'string'
              ? JSON.parse(x['coWriters'])
              : x['coWriters'],
          requisites:
            typeof x['requisites'] === 'string'
              ? JSON.parse(x['requisites'])
              : x['requisites'],
          creator:
            typeof x['creator'] === 'string'
              ? JSON.parse(x['creator'])
              : x['creator'],
          modules: (mapCollection(x, 'modulesCollection') as Module[]).map(
            (w) =>
              ({
                ...w,
                lessons: (
                  mapCollection(w, 'lessonsCollection') as Lesson[]
                ).map((z) => ({
                  ...z,
                  contents:
                    typeof z.contents === 'string'
                      ? JSON.parse(z.contents)
                      : z.contents,
                })),
                module: w.id,
              } as Module)
          ),
        } as Course)
    ) as Course[];
    return items;
  }

  async queryLesson(id: string) {
    const res = await this.state.graphql.rawRequest(LessonQuery, { id });
    if (!res.data) {
      return;
    }
    const data = mapCollection(res.data, 'lessonsCollection').at(0);
    return !data ? undefined : {
      lesson: {
        id: data['id'],
        name: data['name'],
        module: data['module'],
        order: data['order'],
        contents:
          typeof data['contents'] === 'string'
            ? JSON.parse(data['contents'])
            : data['contents'],
      } as Lesson,
      module: data['modules'] as Module,
      course: data['modules']['courses'] as Course,
    };
  }

  async add(payload: Course) {
    const user = this.user;
    payload = {
      ...payload,
      created_by: user.id,
      creator: {
        name: this.user.firstname + ' ' + this.user.lastname,
        email: this.user.email,
        bio: 'Course Creator',
        avatar: this.user.avatar ?? 'assets/images/user-profile.jpg',
      },
      organization: getId(user?.organization),
    };

    const { data, error } = await this.state.supabase
      .from('courses')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async update(item: Course) {
    const user = this.user;

    const { id, ...payload } = {
      ...item,
      updated_by: user.id,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.state.supabase
      .from('courses')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async delete(id: string, isScorm?: boolean) {
    if (isScorm) {
      await this.scormService.deleteScorm(id);
    }
    const { error } = await this.state.supabase
      .from('courses')
      .delete()
      .eq('id', id);
    return {
      data: !error,
      error: error?.message,
    };
  }

  async addModule(payload: Module) {
    const user = this.user;
    payload = {
      ...payload,
      created_by: user.id,
    };

    const { data, error } = await this.state.supabase
      .from('modules')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async saveModule(item: Module) {
    const user = this.user;
    const { id, ...payload } = {
      ...item,
      updated_by: user.id,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.state.supabase
      .from('modules')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async addLesson(payload: Lesson) {
    const user = this.user;
    payload = {
      ...payload,
      created_by: user.id,
    };

    const { data, error } = await this.state.supabase
      .from('lessons')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0)?.id as string,
      error: error?.message,
    };
  }

  async saveLesson(item: Lesson) {
    const user = this.user;
    const { id, ...payload } = {
      ...item,
      updated_by: user.id,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await this.state.supabase
      .from('lessons')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0) as Lesson,
      error: error?.message,
    };
  }

  async deleteModule(id: string) {
    const { error } = await this.state.supabase
      .from('modules')
      .delete()
      .eq('id', id);
    return {
      data: !error,
      error: error?.message,
    };
  }

  async deleteLesson(id: string) {
    const { error } = await this.state.supabase
      .from('lessons')
      .delete()
      .eq('id', id);
    return {
      data: !error,
      error: error?.message,
    };
  }

  async getEnrollment(courseId: string) {
    const { data, error } = await this.state.supabase
      .from('course_enrollments')
      .select(
        '*, teams(id, name), groups(id, name), users(id, firstname, lastname,email)'
      )
      .eq('course', courseId);

    return {
      data: {
        users:
          data
            ?.filter((x) => !!x.user)
            .map((x) => ({
              ...x.users,
              enrollId: x.id,
              name: `${x.users.firstname} ${x.users.lastname}`,
              username: `${x.users.firstname} ${x.users.lastname}`,
            })) ?? [],
        teams:
          data
            ?.filter((x) => !!x.team)
            .map((x) => ({ ...x.teams, enrollId: x.id, teamOnly: !x.group })) ??
          [],
        groups:
          data
            ?.filter((x) => !!x.group)
            .map((x) => ({ ...x.groups, team: x.teams, enrollId: x.id })) ?? [],
      } as EnrollmentView,
      error: error?.message,
    };
  }

  async getEnrollmentByTeamGroup(teamIds: string[]) {
    const { data } = await this.state.supabase
      .from('course_enrollments')
      .select('*')
      .in('team', teamIds);
    return (data || []) as Enrollment[];
  }

  async getUsersInTeamsAndGroups(useIds: string[]) {
    if (!useIds.length) {
      return [] as TeamGroupItemView[];
    }
    const { data } = await this.state.supabase
      .from('user_groups')
      .select(
        'user(id, firstname, lastname), team(id, name), group(id, name, team)'
      )
      .in('user', useIds);

    return ((data || []) as any[] as TeamGroupItemView[]).filter(
      (u) => getId(u.team) || getId(u.group)
    );
  }

  async saveEnrollment(payload: { items: Enrollment[]; toDelete: string[] }) {
    const errors: string[] = [];
    if (payload.toDelete.length) {
      const resDel = await Promise.all(
        payload.toDelete.map(
          async (id) =>
            await this.state.supabase
              .from('course_enrollments')
              .delete()
              .eq('id', id)
        )
      );
      errors.push(...resDel.map((r) => r.error?.message!));
    }

    const news = payload.items
      .filter((x) => !x.id && (!!x.course || !!x.user || !!x.group))
      .map(({ id, ...x }) => ({
        ...x,
        created_by: this.user.id,
      }));

    if (news.length) {
      const res = await this.state.supabase
        .from('course_enrollments')
        .insert(news);
      errors.push(res.error?.message!);
    }

    return errors.filter(Boolean);
  }
}
