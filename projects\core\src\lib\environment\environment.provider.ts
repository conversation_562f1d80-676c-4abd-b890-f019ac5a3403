import { inject, provideAppInitializer } from "@angular/core";
import { ConfigurationLoader } from "./config.service";

export function loadConfiguration(configService: ConfigurationLoader) {
  return () => configService.loadConfiguration();
}

export const envProvider = provideAppInitializer(() => {
  const initializerFn = loadConfiguration(inject(ConfigurationLoader));
  return initializerFn();
});
