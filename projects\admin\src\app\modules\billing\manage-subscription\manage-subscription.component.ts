import { Component, inject, resource } from '@angular/core';
import { <PERSON><PERSON><PERSON>cyPipe, DatePipe } from '@angular/common';
import { PaymentService } from '../../../core/components/plans/payment.service';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-manage-subscription',
  imports: [
    DatePipe,
    MatProgressBarModule,
    CurrencyPipe,
    ResourceHeaderComponent,
  ],
  templateUrl: './manage-subscription.component.html',
})
export class ManageSubscriptionComponent {
  service = inject(PaymentService);

  subscription = this.service.state.mySubscription()!;

  get customerId() {
    return this.subscription?.paymentId;
  }

  get plan() {
    return this.subscription?.plan;
  }

  get remaingSeats() {
    return (
      this.subscription?.plan.countMax - (this.subscription.used_seat || 7)
    );
  }
  get hasAiSubscription() {
    return this.subscription?.addOns?.some((x) =>
      x.name.toLowerCase().includes('ai')
    );
  }

  error?: string | null = null;
  isLoading = false;

  historySource = resource({
    request: () => this.service.state.mySubscription(),
    loader: async ({ request }) => {
      const { data, error } = await this.service.getBillingHistory(
        request.paymentId
      );
      return data || [];
    },
  });

  subscriptionSource = resource({
    request: () => this.service.state.mySubscription(),
    loader: async ({ request }) => {
      const { data, error } = await this.service.getSubscriptions(
        request.paymentId
      );
      return data || [];
    },
  });

  // async verifyCustomer(email: string) {
  //   const { data, error } = await this.service.verifyCustomer(email);

  //   if (error || !data) {
  //     this.error = error;
  //     return;
  //   }

  //   if (data) {
  //     this.loadBillingHistory();
  //     this.loadSubscriptions();
  //     return;
  //   }
  // }

  async unsubscribe(subscriptionId: string) {
    const { error } = await this.service.unsubscribe(subscriptionId);
    if (error) {
      this.error = error;
      return;
    }
    this.subscriptionSource.reload();
  }
}
