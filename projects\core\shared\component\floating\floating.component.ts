import { Component, input, output } from '@angular/core';

export type OptionItem = { id: string; name: string, parent?: string };

@Component({
  selector: "app-floating",
  standalone: true,
  imports: [],
  templateUrl: "./floating.component.html",
})
export class FloatingComponent {
  with = input<string>("fit");
  icon = input<string>("");
  title = input<string>("");
  options = input<OptionItem[]>([]);

  selected = output<OptionItem>();
  current = "";

  select(item: OptionItem) {
    this.current = item.name;
    this.selected.emit(item);
  }
}
