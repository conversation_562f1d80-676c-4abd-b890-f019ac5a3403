import { Component, inject, OnInit } from '@angular/core';
import { BillingService } from '@lms/core';
import { BillingHistoryComponent } from './history/history.component';
import { DatePipe } from '@angular/common';
import { ResourceHeaderComponent } from '@lms/shared';
import { BillingActiveUsersComponent } from './active-users/active-users.component';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  imports: [
    DatePipe,
    ResourceHeaderComponent,
    BillingActiveUsersComponent,
    BillingHistoryComponent,
  ],
})
export class BillingComponent implements OnInit {
  service = inject(BillingService);
  readonly dialog = inject(MatDialog);

  subscription = this.service.state.mySubscription()!;
  source = this.service.historySource;

  view: 'BILLING' | 'USERS' | 'PAYMENT' = 'BILLING';

  enableStripe = false;

  get remaingSeats() {
    return (
      this.subscription?.plan.countMax - (this.subscription.used_seat || 7)
    );
  }
  get hasAiSubscription() {
    return this.subscription?.addOns?.some((x) =>
      x.name.toLowerCase().includes('ai')
    );
  }  
  
  get hasSubscriptionPayment() {
    return !!this.subscription?.paymentId;
  }

  ngOnInit(): void {
    this.service.userId.set(this.service.user.id);
  }

  async subscribe(): Promise<void> {
    // this.dialog.open(PlansComponent, {
    //   minWidth: '600px',
    //   width: '900px',
    //   maxWidth: '1024px',
    // });
  }
}
