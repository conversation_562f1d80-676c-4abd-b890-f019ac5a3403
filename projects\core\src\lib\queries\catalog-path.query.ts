export const PathCatalogQuery = `
query getCatalogPath($id: UUID!, $teams: [UUID!], $dueDate: Datetime, $limit: Int) {
  instructor_enrollmentsCollection(
    first: $limit,
    orderBy: {created_at: AscNullsLast, dueDate: AscNullsFirst }
    filter: { or: [{user: {eq: $id}}, {team: {in: $teams}}] }
  ) {
    edges {
      node {
        id
        user
        team
        group
        created_at
        dueDate
        instructor
        learning_instructors {
          id
          name
          type
          short
          cover
        }
        user_trackings_comboCollection {
          edges {
            node {
              id
              enrollment
              type
              status
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      } 
    }
  }
}
`;

export const PathCatalogDueFirstQuery = `
query getCatalogPath($id: UUID!, $teams: [UUID!], $dueDate: Datetime, $limit: Int) {
  instructor_enrollmentsCollection(
    first: $limit,
    orderBy: { dueDate: AscNullsLast }
    filter: { or: [{user: {eq: $id}}, {team: {in: $teams}}]}
  ) {
    edges {
      node {
        id
        user
        team
        group
        created_at
        dueDate
        instructor
        learning_instructors {
          id
          name
          type
          short
          cover
        }
        user_trackings_comboCollection {
          edges {
            node {
              id
              enrollment
              type
              status
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      } 
    }
  }
}
`;