<a
  href="javascript:void(0)"
  class="bg-lot-blue/5 border border-lot-blue py-1 px-3 rounded-md text-xs"
  cdkOverlayOrigin
  #trigger="cdkOverlayOrigin"
  matTooltip="Generate your content with AI"
  matTooltipPosition="above"
  (click)="isOpen = true"
>
  <i class="fa-solid fa-wand-magic-sparkles text-lot-blue mr-2"></i>
  <span class="font-bold text-lot-blue">Write with AI</span>
</a>

<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="trigger"
  [cdkConnectedOverlayOpen]="isOpen"
  (detach)="isOpen = false"
>
  <div
    class="flex flex-col bg-white rounded-2xl border-gray-200 border w-[380px] gap-2 p-5"
  >
    <div class="flex justify-between">
      <span class="font-semibold">Write with AI</span>
      <a
        href="javascript:void(0)"
        class="text-lot-danger"
        (click)="isOpen = false"
      >
        <i class="fa-regular fa-circle-xmark"></i>
      </a>
    </div>
    @if (isLoading) {
    <mat-progress-bar mode="indeterminate" />
    }

    <div class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5">
      <input
        type="search"
        name="prompt"
        id="prompt"
        (input)="prompt.set($any($event.target).value)"
        class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
        placeholder="Enter your prompt here..."
      />
      <a
        href="javascript:void(0)"
        (click)="generateAI()"
        class="bg-lot-ai-dark text-white p-2 rounded-md"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="currentColor"
          class="size-6 -rotate-45"
        >
          <path
            d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
          />
        </svg>
      </a>
    </div>
  </div>
</ng-template>
