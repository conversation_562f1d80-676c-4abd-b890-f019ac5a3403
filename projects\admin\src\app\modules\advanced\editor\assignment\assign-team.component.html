@if (isLoading) {
<mat-progress-bar mode="indeterminate" />
} @if (error) {
<p class="text-center font-semibold text-lot-danger">{{ error }}</p>
} @if (readToSave) {
<p class="mb-4 font-semibold text-lot-warning">
  *You have pending changes, click <b>Save Enrollment</b> button above to save
  your data.
</p>
}
<div class="grid grid-cols-3 gap-5 h-full">
  <div class="flex flex-col gap-2 p-4 rounded-xl border">
    <h4 class="text-lot-dark font-semibold mb-2">
      ({{ teamItems.length }}) - Assigned Teams
    </h4>
    <div
      class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-2"
    >
      <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
      <input
        type="search"
        name="search"
        id="search"
        [formControl]="teamControl"
        [matAutocomplete]="teamTmp"
        class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
        placeholder="Search Teams"
      />
      <mat-autocomplete
        autoActiveFirstOption
        #teamTmp="matAutocomplete"
        (optionSelected)="selectItem($event.option.value, 'TEAM')"
        [displayWith]="displayFn"
      >
        @for (option of teamOptions(); track option) {
        <mat-option [value]="option">{{ option.name }}</mat-option>
        }
      </mat-autocomplete>
    </div>
    <ng-container
      [ngTemplateOutlet]="listView"
      [ngTemplateOutletContext]="{ items: teamItems, type: 'TEAM' }"
    />
  </div>
  <div class="flex flex-col gap-2 p-4 rounded-xl border">
    <h4 class="text-lot-dark font-semibold mb-2">
      ({{ groups.length }}) - Assigned Groups
    </h4>
    <div
      class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-2"
    >
      <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
      <input
        type="search"
        name="search"
        id="searchGroup"
        [formControl]="groupControl"
        [matAutocomplete]="groupTmp"
        class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
        placeholder="Search Groups"
      />
      <mat-autocomplete
        autoActiveFirstOption
        #groupTmp="matAutocomplete"
        (optionSelected)="selectItem($event.option.value, 'GROUP')"
        [displayWith]="displayFn"
      >
        @for (option of groupOptions(); track option) {
        <mat-option [value]="option">{{ option.name }}</mat-option>
        }
      </mat-autocomplete>
    </div>
    <ng-container
      [ngTemplateOutlet]="listView"
      [ngTemplateOutletContext]="{ items: groups, type: 'GROUP' }"
    />
  </div>
  <div class="flex flex-col gap-2 p-4 rounded-xl border">
    <h4 class="text-lot-dark font-semibold mb-2">
      ({{ users.length }}) - Assigned Users
    </h4>
    <div
      class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-2"
    >
      <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
      <input
        type="search"
        name="search"
        id="search"
        [formControl]="userControl"
        [matAutocomplete]="userTmp"
        class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
        placeholder="Search Users"
      />
      <mat-autocomplete
        autoActiveFirstOption
        #userTmp="matAutocomplete"
        (optionSelected)="selectItem($event.option.value, 'USER')"
        [displayWith]="displayFn"
      >
        @for (option of userOptions(); track option) {
        <mat-option [value]="option">{{ option.name }}</mat-option>
        }
      </mat-autocomplete>
    </div>
    <ng-container
      [ngTemplateOutlet]="listView"
      [ngTemplateOutletContext]="{ items: users, type: 'USER' }"
    />
  </div>
</div>

<ng-template #listView let-items="items" let-type="type">
  <ul
    class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
  >
    @for (item of items; track item) {
    <li class="flex justify-between items-center gap-3 border-b py-2 group">
      @if(type === 'GROUP'){
      <p><span class="text-lot-dark-gray text-sm">{{ item.team?.name }}</span>: {{ item.name }}</p>
      } @else {
      <span>{{ item.name }}</span>
      }
      <div class="flex items-center gap-2">
        @if(item.tag){
        <span
          class="px-1 border rounded-md text-xs capitalize"
          [class.border-lot-ai]="item.tag === 'NEW'"
          [class.border-lot-danger]="item.tag === 'DELETE'"
          [class.text-lot-ai]="item.tag === 'NEW'"
          [class.text-lot-danger]="item.tag === 'DELETE'"
          >{{ item.tag }}
        </span>
        } @if(item.tag !== 'DELETE'){
        <a
          href="javascript:void(0)"
          (click)="removeItem(item.id, type)"
          class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
        }
      </div>
    </li>
    } @empty {
    <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
    }
  </ul>
</ng-template>

<ng-template #dueDateTmp>
  <div class="flex flex-col gap-6 px-10 py-10">
    <h1 class="text-lot-blue text-xl font-bold">Set Enrollment Due Date</h1>

    <div class="max-w-xs">
      <div class="form-lot-input">
        <div class="field">
          <div class="flex items-center">
            <input
              type="text"
              id="dueDate"
              class="relative w-full"
              [matDatepicker]="picker"
              [min]="minDate"
              [formControl]="dueDateControl"
            />
            <mat-datepicker-toggle
              matIconSuffix
              [for]="picker"
              class="absolute right-0"
            />
          </div>

          <mat-datepicker #picker />
          <label for="dueDate"> Due Date (Optional) </label>
        </div>
      </div>
    </div>

    <button type="button" (click)="submit()" class="button-primary w-fit">
      Save Or Continue
    </button>
  </div>
</ng-template>
