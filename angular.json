{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"admin": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/admin", "sourceRoot": "projects/admin/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/admin", "index": "projects/admin/src/index.html", "browser": "projects/admin/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/admin/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/admin/public"}, {"glob": "**/*", "input": "projects/core/assets", "output": "./assets/"}, {"glob": "env.json", "input": "projects/core/src/lib/environment", "output": "./"}], "styles": ["node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss", "node_modules/@fortawesome/fontawesome-free/scss/solid.scss", "node_modules/@fortawesome/fontawesome-free/scss/regular.scss", "node_modules/@fortawesome/fontawesome-free/scss/brands.scss", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.bubble.css", "node_modules/quill/dist/quill.snow.css", "projects/core/assets/tailwind.css", "projects/core/assets/theme.scss", "projects/admin/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["projects/core", "projects/core/assets", "node_modules/"]}, "scripts": ["node_modules/mammoth/mammoth.browser.min.js", "node_modules/papaparse/papaparse.js"], "allowedCommonJsDependencies": ["websocket", "@supabase/realtime-js", "cross-fetch", "dayjs"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "980kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "2mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "admin:build:production"}, "development": {"buildTarget": "admin:build:development", "port": 3001, "open": true}}, "defaultConfiguration": "development"}}}, "shell": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/shell", "sourceRoot": "projects/shell/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/shell", "index": "projects/shell/src/index.html", "browser": "projects/shell/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/shell/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/shell/public"}, {"glob": "**/*", "input": "projects/core/assets", "output": "./assets/"}, {"glob": "env.json", "input": "projects/core/src/lib/environment", "output": "./"}], "styles": ["node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss", "node_modules/@fortawesome/fontawesome-free/scss/solid.scss", "node_modules/@fortawesome/fontawesome-free/scss/regular.scss", "node_modules/@fortawesome/fontawesome-free/scss/brands.scss", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.bubble.css", "node_modules/quill/dist/quill.snow.css", "projects/core/assets/tailwind.css", "projects/core/assets/theme.scss", "projects/shell/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["projects/core", "projects/core/assets", "node_modules/"]}, "scripts": ["node_modules/mammoth/mammoth.browser.min.js", "node_modules/papaparse/papaparse.js"], "allowedCommonJsDependencies": ["websocket", "@supabase/realtime-js", "cross-fetch", "dayjs"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "980kb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "2mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "shell:build:production"}, "development": {"buildTarget": "shell:build:development", "port": 3000, "open": true}}, "defaultConfiguration": "development"}}}, "auth": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/auth", "sourceRoot": "projects/auth/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/auth", "index": "projects/auth/src/index.html", "browser": "projects/auth/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/auth/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/auth/public"}], "styles": ["projects/auth/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "auth:build:production"}, "development": {"buildTarget": "auth:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/auth/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/auth/public"}], "styles": ["projects/auth/src/styles.scss"], "scripts": []}}}}, "learner": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/learner", "sourceRoot": "projects/learner/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/learner", "index": "projects/learner/src/index.html", "browser": "projects/learner/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/learner/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/learner/public"}, {"glob": "**/*", "input": "projects/core/assets", "output": "./assets/"}, {"glob": "env.json", "input": "projects/core/src/lib/environment", "output": "./"}], "styles": ["node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss", "node_modules/@fortawesome/fontawesome-free/scss/solid.scss", "node_modules/@fortawesome/fontawesome-free/scss/regular.scss", "node_modules/@fortawesome/fontawesome-free/scss/brands.scss", "projects/core/assets/scss/theme.scss", "projects/core/assets/tailwind.css", "projects/core/assets/theme.scss", "projects/learner/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["projects/core", "projects/core/assets", "node_modules/"]}, "scripts": ["node_modules/mammoth/mammoth.browser.min.js", "node_modules/papaparse/papaparse.js"], "allowedCommonJsDependencies": ["websocket", "@supabase/realtime-js", "cross-fetch", "dayjs"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "900kb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "900kb", "maximumError": "1mb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "learner:build:production"}, "development": {"buildTarget": "learner:build:development", "port": 3002, "open": true}}, "defaultConfiguration": "development"}}}, "core": {"projectType": "library", "root": "projects/core", "sourceRoot": "projects/core/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/core/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/core/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/core/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/core/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "player": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/player", "sourceRoot": "projects/player/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/player", "index": "projects/player/src/index.html", "browser": "projects/player/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/player/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/player/public"}, {"glob": "**/*", "input": "projects/core/assets", "output": "./assets/"}, {"glob": "env.json", "input": "projects/core/src/lib/environment", "output": "./"}], "styles": ["node_modules/@fortawesome/fontawesome-free/scss/fontawesome.scss", "node_modules/@fortawesome/fontawesome-free/scss/solid.scss", "node_modules/@fortawesome/fontawesome-free/scss/regular.scss", "node_modules/@fortawesome/fontawesome-free/scss/brands.scss", "projects/core/assets/tailwind.css", "projects/core/assets/theme.scss", "projects/player/src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["projects/core", "projects/core/assets", "node_modules/"]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "900kB", "maximumError": "1MB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "player:build:production"}, "development": {"buildTarget": "player:build:development", "port": 3002, "open": true}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/player/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/player/public"}], "styles": ["projects/player/src/styles.scss"], "scripts": []}}}}, "management": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/management", "sourceRoot": "projects/management/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/management", "index": "projects/management/src/index.html", "browser": "projects/management/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/management/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/management/public"}], "styles": ["projects/management/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "management:build:production"}, "development": {"buildTarget": "management:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/management/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/management/public"}], "styles": ["projects/management/src/styles.scss"], "scripts": []}}}}}}