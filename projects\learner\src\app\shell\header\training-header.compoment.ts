import { Component, inject } from '@angular/core';
import { LearningPathItem, TrainingService } from '@lms/core';
import { RouterLink } from '@angular/router';
import { ProgressComponent } from '@lms/shared';

@Component({
  selector: 'app-training-header',
  imports: [ProgressComponent, RouterLink],
  template: `
    <div class="flex justify-between w-full">
      <div class="flex items-center gap-8">
        <span class="h-6  w-[1px] bg-white"></span>
        @if (source.isLoading()) {
        <span class="text-white">Loading...</span>
        } @if (source.value(); as course) {
        <div class="flex flex-col">
          <h2 class="text-white">
            {{ course.name || 'How to Make a Good Sale!' }}
          </h2>
          <!-- @if(pathSource.value(); as info){
          <span class="text-lot-gray text-sm font-light"
            >Part of {{ info.name }}</span
          >
          } -->
        </div>
        <a
          [routerLink]="['/course-library']"
          class="bg-transparent hover:bg-lot-light-gray/20 font-semibold py-1 px-2 rounded-md border-2 border-white text-center"
        >
          <img src="assets/images/new/mdi_share.svg" alt="" srcset="" />
        </a>
        <span class="h-6 w-[1px] bg-white"></span>
        <div class="flex flex-col">
          <!-- <app-progress [status]="course.status" [progress]="course.lea.progress" />
          <span class="text-white">{{
            course.progress
              ? course.progress + '% complete - Keep Going'
              : '0% complete - Time to Start!'
          }}</span> -->
        </div>
        }
      </div>
    </div>
  `,
})
export class TrainingHeaderComponent {
  trainingService = inject(TrainingService);
  source = this.trainingService.course;
  pathSource = this.trainingService.userTracking;

  get path() {
    return this.pathSource.value()?.learningPath as LearningPathItem;
  }

  get course() {
    return this.source.value();
  }
}
