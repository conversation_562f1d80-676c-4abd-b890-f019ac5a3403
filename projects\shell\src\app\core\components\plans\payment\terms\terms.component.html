<div mat-dialog-title>
  <h1 class="text-3xl font-bold">
    {{ terms.header.title }}
  </h1>
</div>
<mat-dialog-content>
  <div class="flex flex-col gap-4 text-lot-dark px-4">
    <p class="text-lg" [innerHTML]="terms.header.content"></p>
    <div class="flex flex-col gap-4 max-h-[300px] overflow-y-auto">
      <ul class="flex flex-col gap-4">
        @for (term of terms.sections; track term.id) {
        <li>
          <h2 class="text-xl font-bold">{{ term.title }}</h2>
          @if (term.content.description) {
          <p [innerHTML]="term.content.description"></p>
          } @if (term.content.items?.length) {
          <ul class="list-disc ml-6">
            @for (item of term.content.items; track $index) {
            <li [innerHTML]="item"></li>
            }
          </ul>
          }
        </li>
        }
      </ul>
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions>
  <div class="flex gap-3 justify-end items-center p-4">
    <button
      (click)="dialogRef.close('CANCEL')"
      type="button"
      class="button-primary-outline w-fit"
    >
      Cancel
    </button>
    <button
      (click)="dialogRef.close('ACCEPT')"
      type="button"
      class="button-primary w-fit py-1.5"
    >
      I agree
    </button>
  </div>
</mat-dialog-actions>
