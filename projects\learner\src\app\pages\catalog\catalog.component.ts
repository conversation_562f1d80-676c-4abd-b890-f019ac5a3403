import { Component, inject, signal } from '@angular/core';
import { CatalogFilterComponent } from './filter/filter.component';
import { CatalogContentComponent } from './content/content.component';
import { CatalogHeaderComponent } from './header/header.component';
import { CatalogFilterEvent, LearningService } from '@lms/core';

@Component({
  selector: 'lms-catalog',
  templateUrl: './catalog.component.html',
  imports: [
    CatalogHeaderComponent,
    CatalogFilterComponent,
    CatalogContentComponent,
  ],
})
export class CatalogComponent {
  learningService = inject(LearningService);
  search = signal<string>('');
  sort = signal<string>('titleASC');
  view = signal<string>('grid');

  dataSource = this.learningService.catalogSource;

  onSearch(term: string): void {
    this.learningService.catalogFilter.update((x) => ({
      ...x,
      load: 1,
      query: term,
    }));
  }

  onFilter(item: CatalogFilterEvent){
    this.learningService.catalogFilter.update((x) => ({
      ...x,
      ...item
    }));
  }
}
