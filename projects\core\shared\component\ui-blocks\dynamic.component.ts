import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { LessonWidgetTypes, WidgetType } from './ui-block.utils';

@Component({
  selector: 'app-dynamic-content',
  template: ` <ng-template #dynamicContent /> `,
})
export class DynamicContentComponent implements OnDestroy {
  @Input({ required: true }) data: {
    widgetType: WidgetType;
    content: any;
  };
  @Output() track = new EventEmitter();

  @ViewChild('dynamicContent', { read: ViewContainerRef })
  dynamicContent!: ViewContainerRef;

  ngAfterViewInit(): void {
    if (!this.data.content || !this.dynamicContent) return;
    
    this.dynamicContent.clear();
    const componentType = LessonWidgetTypes.filter(
      (x) => x.id === this.data.widgetType
    )[0].component as any;

    const componentRef =
      this.dynamicContent.createComponent<any>(componentType);
    componentRef.setInput('content', this.data.content);
    componentRef.instance?.view?.subscribe((data: any) => {
      this.track.emit(data);
    });
  }

  ngOnDestroy(): void {
    this.dynamicContent?.clear();
    this.dynamicContent?.detach();
  }
}
