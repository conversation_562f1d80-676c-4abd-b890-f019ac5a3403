import { Routes } from '@angular/router';
import { SignInComponent } from './modules/authn/sign-in.component';
import {
  canActivateAdminAuth,
  canActivateAuth,
  canActivateAuthForLearner,
  canActivateOneAuth,
  canActivateSubscription,
} from '@lms/core';

export const routes: Routes = [
  {
    path: 'sign-in',
    component: SignInComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./modules/authn/login/login.component').then(
            (c) => c.LoginComponent
          ),
      },
    ],
  },
  {
    path: 'verify/:email',
    component: SignInComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./modules/authn/verify/verify.component').then(
            (c) => c.VerifyEmailComponent
          ),
      },
    ],
  },
  {
    path: 'reset-password',
    component: SignInComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./modules/authn/forgot/forgot.component').then(
            (c) => c.ForgotComponent
          ),
      },
    ],
  },
  {
    path: 'change-password',
    component: SignInComponent,
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            './modules/authn/change-password/change-password.component'
          ).then((c) => c.ChangePasswordComponent),
      },
    ],
  },
  {
    path: 'subscription',
    canActivate: [canActivateSubscription],
    loadComponent: () =>
      import('./modules/subscription/subscription.component').then(
        (c) => c.SubscriptionComponent
      ),
  },
  {
    path: 'sign-up',
    loadComponent: () =>
      import('./modules/sign-up/sign-up.component').then(
        (c) => c.SignUpComponent
      ),
  },
  {
    path: 'lms',
    canActivate: [canActivateAdminAuth],
    loadChildren: () =>
      import('../../../admin/src/app/app.routes').then((c) => c.ADMIN_ROUTES),
  },
  {
    path: 'myTraining',
    canActivate: [canActivateAuthForLearner],
    loadChildren: () =>
      import('../../../learner/src/app/app.routes').then(
        (c) => c.LEARNER_ROUTES
      ),
  },
  {
    path: 'go-training',
    canActivate: [canActivateAuth],
    loadChildren: () =>
      import('../../../player/src/app/app.routes').then((c) => c.PLAY_ROUTES),
  },
  {
    path: 'management',
    // canActivate: [canActivateOneAuth],
    loadChildren: () =>
      import('../../../management/src/app/app.routes').then(
        (c) => c.MANAGEMENT_ROUTES
      ),
  },
  {
    path: '',
    redirectTo: '/lms',
    pathMatch: 'full',
  },
  // {
  //   path: '**',
  //   redirectTo: '/',
  //   pathMatch: 'full',
  // },
];
