import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { UserItem } from '@lms/core';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';


export type UserAction = 'edit' | 'delete' | 'view' | 'page' | 'team';

@Component({
  selector: 'app-user-list',
  imports: [MatMenuModule, MatIconModule, MatPaginatorModule],
  templateUrl: './list.component.html',
})
export class UserListComponent {
  @Input() data: UserItem[] = [];
  @Input() type: 'USER' | 'TEAM' = 'USER';
  @Input() total = 0;
  @Input() pageSize = 10;

  @Output() onItem = new EventEmitter<{
    type: UserAction;
    data: UserItem;
    page?: number;
  }>();

  action(type: UserAction, item: UserItem, page?: PageEvent) {
    if (type === 'page' && page) {
      this.onItem.emit({ type, data: item, page: page.pageIndex + 1 });
      return;
    }
    this.onItem.emit({ type, data: item });
  }
}
