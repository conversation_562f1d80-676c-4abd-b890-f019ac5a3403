import { Injectable, inject } from '@angular/core';
import { FileStorageService, GlobalStateService } from '@lms/core';

export enum MediaBucket {
  AVATARS = 'lot-avatars',
  IMAGES = 'mediaimages', // 'lot-images',
  VIDEOS = 'lot-videos',
  DOCUMENTS = 'lot-documents',
  MEDIA = 'lot-media',
}

@Injectable({
  providedIn: 'root',
})
export class MediaCenterService {
  state = inject(GlobalStateService);
  storageService = inject(FileStorageService);

  // async uploadFileOld(file: File, type: 'image' | 'video' | 'document') {
  //   const bucket = this.getBucketName(type);
  //   const path = `${Date.now()}-${file.name}`;

  //   try {
  //     const { data, error } = await this.storage.upload(bucket, path, file);
  //     if (error) throw error;

  //     // Add the new resource to your database/state management
  //     const newResource = {
  //       id: Date.now(),
  //       type,
  //       url: data?.fullPath, // Adjust based on your storage service response
  //       name: file.name,
  //     };

  //     return newResource;
  //   } catch (error) {
  //     console.error('Upload failed:', error);
  //     throw error;
  //   }
  // }

  async uploadFile(file: File, bucket: MediaBucket, folder?: string) {
    try {
      const uniqueFileName = `${Date.now()}-${file.name}`;
      const filePath = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;

      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
        });

      if (error) return { data: null, error: error.message };

      // Get the public URL for the uploaded file
      const {
        data: { publicUrl },
      } = this.state.supabase.storage.from(bucket).getPublicUrl(data.path);

      return {
        data: {
          url: publicUrl,
          fullPath: filePath,
          type: this.getFileType(file.name),
        },
        error: undefined,
      };
    } catch (error: any) {
      console.error('Upload error:', error);
      return { data: null, error: error.message };
    }
  }

  async uploadMultipleFiles(
    files: File[],
    bucket: MediaBucket,
    folder?: string
  ) {
    const uploads = files.map((file) => this.uploadFile(file, bucket, folder));
    const results = await Promise.all(uploads);

    const successful = results.filter((result) => !result.error);
    const failed = results.filter((result) => result.error);

    return {
      data: successful.map((s) => s.data),
      errors: failed.map((f) => f.error),
    };
  }

  async removeFile(
    bucket: MediaBucket,
    filePath: string
  ): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error('Remove error:', error);
      return { data: null, error };
    }
  }

  // Remove multiple files
  async removeMultipleFiles(bucket: MediaBucket, filePaths: string[]) {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .remove(filePaths);

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error('Remove multiple error:', error);
      return { data: null, error };
    }
  }

  // Move/Rename a file
  async moveFile(
    bucket: MediaBucket,
    fromPath: string,
    toPath: string
  ): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .move(fromPath, toPath);

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error('Move error:', error);
      return { data: null, error };
    }
  }

  // Copy a file
  async copyFile(
    bucket: MediaBucket,
    fromPath: string,
    toPath: string
  ): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .copy(fromPath, toPath);

      if (error) throw error;

      return { data, error: null };
    } catch (error) {
      console.error('Copy error:', error);
      return { data: null, error };
    }
  }

  async listFiles(bucket: MediaBucket, folder?: string) {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .list(folder || '', {
          limit: 100,
          offset: 0,
          sortBy: { column: 'name', order: 'asc' },
        });

      if (error) {
        throw error;
      }

      // Transform the data to include full URLs
      const filesWithUrls = await Promise.all(
        (data || []).map(async (file) => {
          const {
            data: { publicUrl },
          } = this.state.supabase.storage
            .from(bucket)
            .getPublicUrl(`${folder ? `${folder}/` : ''}${file.name}`);

          return {
            ...file,
            url: publicUrl,
            fullPath: `${folder ? `${folder}/` : ''}${file.name}`,
            type: this.getFileType(file.name),
          };
        })
      );

      return { data: filesWithUrls, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }

  private async getSignedUrl(
    bucket: MediaBucket,
    path: string,
    expiresIn = 3600
  ) {
    try {
      const { data, error } = await this.state.supabase.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn);

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  }
  private getFileType(
    fileName: string
  ): 'image' | 'video' | 'document' | 'other' {
    const extension = fileName.toLowerCase().split('.').pop();

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
    const videoExtensions = ['mp4', 'webm', 'ogg', 'mov'];
    const documentExtensions = [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'txt',
    ];

    if (imageExtensions.includes(extension!)) return 'image';
    if (videoExtensions.includes(extension!)) return 'video';
    if (documentExtensions.includes(extension!)) return 'document';
    return 'other';
  }
}
