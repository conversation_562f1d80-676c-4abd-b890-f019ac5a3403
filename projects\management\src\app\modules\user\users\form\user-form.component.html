<div class="flex flex-col gap-5 px-10 py-10">
  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (view === 'EDIT') {
  <div class="flex flex-col">
    @if (!self) {
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-blue underline italic"
      (click)="view = 'ADD'"
    >
      Add New Users
    </a>
    }
    <h1 class="text-2xl font-semibold">Edit: {{ data.item?.email }}</h1>
    @if (error) {
    <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
    }
  </div>
  <ng-container *ngTemplateOutlet="editUser" />

  } @if (view === 'ADD') {
  <h1 class="text-2xl font-semibold">
    {{ title }}
  </h1>
  @if (error) {
  <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
  }
  <ng-container *ngTemplateOutlet="newUser" />
  }
</div>

<ng-template #newUser>
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="flex flex-col gap-5">
    <div class="flex flex-col min-h-72" formArrayName="groups">
      @for (item of groups.controls; track $index; let i= $index; let
      first=$first ; let last = $last) {
      <div
        [formGroupName]="i"
        class="flex justify-between gap-3 items-center border-lot-gray py-1"
        [class.border-y]="i === 0"
        [class.border-b]="i !== 0"
      >
        <div class="flex gap-3 items-center w-full">
          <div class="flex flex-col px-2 w-2/3">
            <label for="{{ 'email' + i }}" class="sr-only">user email</label>
            <input
              [id]="'email' + i"
              type="email"
              formControlName="email"
              name="email"
              placeholder="Enter an email here"
              autocomplete="new-password"
              class="bg-transparent rounded-md border-none px-5 py-2.5 w-full text-lot-dark placeholder:text-lot-dark-gray outline-none"
            />
            @if (item.get('error')?.value) {
            <span class="pl-2 text-xs text-lot-danger"
              >Please enter valid email</span
            >
            }
          </div>
          <div class="flex flex-col w-1/3">
            <label for="{{ 'role' + i }}" class="sr-only">user role</label>
            <mat-select formControlName="role">
              <mat-option value="learner" seleced>Learner</mat-option>
              <mat-option value="admin">Admin</mat-option>
              <mat-option value="manager">Manager</mat-option>
            </mat-select>
          </div>
          <div class="flex flex-col w-1/3">
            <label for="{{ 'team' + i }}" class="sr-only">user team</label>
            <mat-select
              formControlName="team"
              (valueChange)="onTeam($event, teamSource.value() || [], i)"
            >
              <mat-option> -- </mat-option>
              @for (item of teamSource.value() || []; track item) {
              <mat-option [value]="item.id">
                {{ item.name }}
              </mat-option>
              }
            </mat-select>
          </div>
          <div class="flex flex-col w-1/3">
            <label for="{{ 'group' + i }}" class="sr-only">user group</label>
            <mat-select formControlName="group">
              <mat-option> -- </mat-option>
              @for (item of groupOptions; track item) {
              <mat-option [value]="item.id">
                {{ item.name }}
              </mat-option>
              }
            </mat-select>
          </div>
        </div>

        <div class="flex gap-3">
          @if (first) {
          <button
            class="button-primary w-fit"
            (click)="addUser(item)"
            type="button"
          >
            Add
          </button>
          } @if (!first) {
          <a
            href="javascript:void(0)"
            (click)="editNewUser(i)"
            class="text-lot-blue/70 hover:text-lot-blue text-xl"
          >
            <i class="fa-solid fa-pen-to-square"></i>
          </a>
          <a
            href="javascript:void(0)"
            (click)="removeUser(i)"
            class="text-lot-danger/70 hover:text-lot-danger text-xl"
          >
            <i class="fa-solid fa-trash"></i>
          </a>
          }
        </div>
      </div>
      }
    </div>
    <div class="flex items-center gap-3 justify-end">
      <button
        class="button-primary-outline w-fit"
        (click)="dialogRef.close()"
        type="button"
      >
        Cancel and Close
      </button>
      <button
        class="button-primary w-32"
        type="submit"
        [disabled]="form.invalid"
      >
        Save {{ count }} Users
      </button>
    </div>
  </form>
</ng-template>

<ng-template #editUser>
  <form
    [formGroup]="userForm"
    (ngSubmit)="onSubmitEdit()"
    class="flex flex-col gap-5 w-full"
  >
    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-input">
        <label for="firstname">First Name</label>
        <input
          id="firstname"
          type="text"
          formControlName="firstname"
          name="firstname"
          placeholder="Enter an email here"
          autocomplete="new-password"
        />
        @if (userForm.get('firstname')?.invalid) {
        <span class="pl-2 text-xs text-lot-danger"
          >Email address is required</span
        >
        }
      </div>
      <div class="form-input">
        <label for="lastname">Last Name</label>
        <input
          id="lastname"
          type="text"
          formControlName="lastname"
          name="lastname"
          placeholder="Enter your lastname"
          autocomplete="new-password"
        />
        @if (userForm.get('lastname')?.invalid) {
        <span class="pl-2 text-xs text-lot-danger">LastName is required</span>
        }
      </div>
    </div>
    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-input">
        <label for="phone">Phone Number</label>
        <input
          id="phone"
          type="tel"
          formControlName="phone"
          name="phone"
          mask="(*************"
          placeholder="Enter phone number"
          autocomplete="new-password"
        />
      </div>
      <div class="form-input">
        <label for="email">Email Address</label>
        <input
          id="email"
          type="email"
          formControlName="email"
          name="email"
          placeholder="Enter an email here"
          autocomplete="new-password"
        />
        @if (userForm.get('email')?.invalid) {
        <span class="pl-2 text-xs text-lot-danger"
          >Email address is required</span
        >
        }
      </div>
    </div>
    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-input">
        <label for="title">Job Title</label>
        <input
          id="title"
          type="text"
          formControlName="title"
          name="title"
          placeholder="Enter title"
          autocomplete="new-password"
        />
      </div>
      @if (!self) {
      <div class="form-input w-full">
        <label for="role">Permission Role</label>
        <mat-select formControlName="role">
          <mat-option value="learner" seleced>Learner</mat-option>
          <mat-option value="admin">Admin</mat-option>
          <mat-option value="manager">Manager</mat-option>
        </mat-select>
      </div>
      } @else {
      <div class="form-input w-full">
        <label for="role">Permission Role</label>
        <div class="font-semibold text-xl px-4 flex justify-between">
          <span>{{ userForm.get("role")?.value }}</span>
          <span>[Self]</span>
        </div>
      </div>
      }
    </div>
    <div class="flex flex-col border-t pt-4 w-full">
      @if (editTeamGroup) {
      <div class="grid grid-cols-2 gap-3 items-center">
        <div class="form-input">
          <label for="team">Select Team</label>
          <mat-select
            (valueChange)="onTeam($event, teamSource.value() || [], -1)"
          >
            <mat-option> -- </mat-option>
            @for (item of teamSource.value() || []; track item) {
            <mat-option [value]="item.id">
              {{ item.name }}
            </mat-option>
            }
          </mat-select>
        </div>
        <div class="form-input">
          <label for="group">Select Groups</label>
          <mat-select (valueChange)="onGroup($event, groupOptions)" multiple>
            <mat-option> -- </mat-option>
            @for (item of groupOptions; track item) {
            <mat-option [value]="item.id">
              {{ item.name }}
            </mat-option>
            }
          </mat-select>
        </div>
      </div>
      } @if (!editTeamGroup) {
      <div class="flex justify-between items-center border-b pb-2">
        <span class="text-sm text-lot-blue italic">
          Assigned to: {{ userTeams.length }} team(s) |
          {{ groupOptions.length }}
          group(s)
        </span>
        @if (!self) {
        <a
          href="javascript:void(0)"
          (click)="editTeamGroup = true"
          class="text-lot-blue/70 hover:text-lot-blue text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
        }
      </div>
      }
      <div class="grid grid-cols-2 gap-3 items-center mt-3">
        <div class="form-input">
          <label for="teams">Assigned Teams</label>
          <mat-chip-set aria-label="Groups selection">
            @for (team of userTeams; track team) {
            <mat-chip>
              <div class="flex items-center gap-1">
                <span>{{ team.teamName }}</span>
                <a
                  href="javascript:void(0)"
                  (click)="removeChips(team.teamId, 'TEAM')"
                >
                  <i class="fa-solid fa-circle-xmark"></i>
                </a>
              </div>
            </mat-chip>
            }@empty {
            <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
              >Not yet assigned</span
            >
            }
          </mat-chip-set>
        </div>

        <div class="form-input">
          <label for="teams">Assigned Groups </label>
          <mat-chip-set aria-label="Groups selection">
            @for (group of userGroups; track group) {
            <mat-chip>
              <div class="flex items-center gap-1">
                <span>{{ group.groupName }}</span>
                <a
                  href="javascript:void(0)"
                  (click)="removeChips(group.groupId, 'GROUP')"
                >
                  <i class="fa-solid fa-circle-xmark"></i>
                </a>
              </div>
            </mat-chip>
            }@empty {
            <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
              >Not yet assigned</span
            >
            }
          </mat-chip-set>
        </div>
      </div>
    </div>
    <div class="flex justify-end gap-4 mt-10">
      <button
        class="button-primary-outline w-fit"
        (click)="dialogRef.close()"
        type="button"
      >
        Cancel and Close
      </button>
      <button class="button-primary w-32 py-1.5" type="submit">Save</button>
    </div>
  </form>
</ng-template>
