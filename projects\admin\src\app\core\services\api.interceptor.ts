import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpResponse,
  HttpHandler,
  HttpEvent,
  HttpErrorResponse,
} from '@angular/common/http';

import { Observable, throwError } from 'rxjs';
import { map, catchError, finalize } from 'rxjs/operators';
import {
  Environment,
  GlobalStateService,
  LoadingService,
  NotificationService,
} from '@lms/core';

@Injectable()
export class ApiInterceptor implements HttpInterceptor {
  constructor(
    private state: GlobalStateService,
    private env: Environment,
    private loader: LoadingService,
    private notification: NotificationService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttpHandler
  ): Observable<HttpEvent<any>> {
    const token: string | undefined = this.state.getAuthUser()?.token;

    this.loader.show();
    if (token && request.url.startsWith(this.env.lmsCoreApi)) {
      request = request.clone({
        headers: request.headers.set('Authorization', 'Bearer ' + token),
      });
    }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (
          request.url.includes(`${this.env.apiEnpointConfig.UPLOAD}/Upload`) &&
          error.status === 201
        ) {
          return throwError(() => error?.error?.text);
        }
        let errorMsg = '';
        if (error.error instanceof ErrorEvent) {
          errorMsg = `Error: ${error.error.message}`;
        } else {
          errorMsg = `Error Code: ${error.status},  Message: ${error.message}`;
        }
        this.loader.hide();
        if (errorMsg) {
          this.notification.error(errorMsg);
        }
        return throwError(() => errorMsg);
      }),
      finalize(() => {
        this.loader.hide();
      })
    );
  }
}
