import { Component, inject } from '@angular/core';
import { UsersComponent } from './users/users.component';
import { TeamsComponent } from './teams/teams.component';
import { MatDialog } from '@angular/material/dialog';
import { TeamFormComponent } from './teams/form/team-form.component';
import { UploadFormComponent } from './users/upload/upload-form.component';
import { TeamCoreService } from '@lms/core';

@Component({
  selector: 'app-user-container',
  imports: [UsersComponent, TeamsComponent],
  templateUrl: './user-container.component.html',
})
export class UserComponent {
  readonly service = inject(TeamCoreService);
  readonly dialog = inject(MatDialog);
  tab = 1;

  type = this.service.viewType;

  add() {
    if (this.tab === 1) {
      this.dialog.open(UploadFormComponent, {
        minWidth: '800px',
      });
      return;
    }
    this.dialog.open(TeamFormComponent, {
      width: '600px',
      data: {
        type: this.type() === 'GROUP' ? 'GROUP' : 'TEAM',
        item: this.type() === 'GROUP' ? this.service.viewTeam() : null,
      },
    });
  }

  setTab(tab: number) {
    this.tab = tab;
    this.service.viewTeam.set(null);
    this.service.viewType.set(tab === 1 ? 'USER' : 'TEAM');
  }
}
