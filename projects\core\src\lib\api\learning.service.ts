import { Injectable, inject, resource, signal } from '@angular/core';
import { GlobalStateService } from '../services';
import { CatalogFilterEvent, LearningType, TraceStatus, UserTrackingItem } from '../models';
import { getId, mapCollection } from '../utils';
import dayjs from 'dayjs';
import { CourseCatalogDueFirstQuery, CourseCatalogQuery } from '../queries';
import {
  PathCatalogDueFirstQuery,
  PathCatalogQuery,
} from '../queries/catalog-path.query';

@Injectable({
  providedIn: 'root',
})
export class LearningService {
  state = inject(GlobalStateService);

  get user() {
    return this.state.user();
  }

  catalogFilter = signal<CatalogFilterEvent>({
    load: 0,
    type: LearningType.COURSE,
    venu: 'online',
    mandatory: 'true',
    status: TraceStatus.ALL,
  });

  homePageLoad = signal<number>(0);

  assignedSource = resource({
    request: () => this.homePageLoad(),
    loader: async ({ request }) => {
      if (!request) {
        return {
          noDueItems: [] as UserTrackingItem[],
          dueItems: [] as UserTrackingItem[],
        };
      }
      const res = await this.getMyDashboard();

      if (res.error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }

      return {
        noDueItems: res.data.noDueItems,
        dueItems: res.data.dueItems,
      };
    },
  });

  catalogSource = resource<UserTrackingItem[], CatalogFilterEvent>({
    request: () => this.catalogFilter(),
    loader: async ({ request }) => {
      if (!request.load) {
        return [] as UserTrackingItem[];
      }
      const res = request.type === LearningType.COURSE
        ? await this.getMyCourses(request.query)
        : await this.getMyLearningPaths(request.query);
      // if (res.error) {
      //   throw new Error('Unable to fetch data. Please refresh this page');
      // }
      return this.filterCatalogData(request, res);
    },
  });

  filterCatalogData(request: CatalogFilterEvent, items: UserTrackingItem[]) {
    let data: UserTrackingItem[] = items;

    if (request.query?.trim()?.length) {
      data = items.filter(
        (x) =>
          x.item.name.includes(request.query!) ||
          x.item.short.includes(request.query!)
      );
    }
    // if (request.type === 'courses') {
    //   data = data.concat(items.filter((x) => x.type === 'COURSE'));
    // }
    // if (request.type === 'quiz') {
    //   data = data.concat(items.filter((x) => x.type === 'QUIZ'));
    // }
    // if (request.mandatory === 'true') {
    //   data = data.filter((x) => !!x.dueDate);
    // }
    // if (request.mandatory === 'false') {
    //   data = data.filter((x) => !x.dueDate);
    // }
    if (request.venu === 'online') {
      //data = data.filter((x) => !!x.venue);
    }
    if (request.venu === 'physical') {
      //data = data.filter((x) => !x.venue);
    }
    // if (request.status === 'DUE') {
    //   data = data.filter((x) => x.status === 'DUE');
    // }
    // if (request.status === 'COMPLETED') {
    //   data = data.filter((x) => x.status === 'COMPLETED');
    // }
    // if (request.status === 'IN_PROGRESS') {
    //   data = data.filter((x) => x.status === 'IN_PROGRESS');
    // }
    // if (request.status === 'PAST') {
    //   data = data.filter((x) => x.status === 'PAST');
    // }
    return data;
  }

  resetFilter() {
    this.catalogFilter.set({
      load: 0,
      type: LearningType.COURSE,
      venu: 'online',
      mandatory: 'true',
      status: TraceStatus.ALL,
    });
  }

  async getMyDashboard(pageSize = 4) {
    const dueRes = await Promise.all([
      this.getMyCourses(undefined, true, {
        page: 0,
        size: pageSize,
      }),
      this.getMyLearningPaths(undefined, true, {
        page: 0,
        size: pageSize,
      }),
    ]).then((res) => {
      return res
        .map((x) => x.flat())
        .flat()
        .sort((a, b) => {
          if (a.dueDate && b.dueDate) {
            return dayjs(a.dueDate).diff(dayjs(b.dueDate));
          }
          return 0;
        });
    });
    const noDueRes = await Promise.all([
      this.getMyCourses(undefined, false, {
        page: 0,
        size: pageSize,
      }),
      this.getMyLearningPaths(undefined, false, {
        page: 0,
        size: pageSize,
      }),
    ]).then((res) => {
      return res
        .map((x) => x.flat())
        .flat()
        .filter((c) => !c.dueDate)
        .sort((a, b) => {
          if (a.created_at && b.created_at) {
            return dayjs(a.created_at).diff(dayjs(b.created_at));
          }
          return 0;
        });
    });

    return {
      data: {
        noDueItems: noDueRes as UserTrackingItem[],
        dueItems: dueRes as UserTrackingItem[],
      },
      error: undefined,
    };
  }

  async getMyCourses(
    query?: string,
    hasDueDate = false,
    paging = {
      page: 0,
      size: 100,
    }
  ) {
    const teams = this.user?.teams?.map((x) => x.teamId) ?? [];
    teams.push(...(this.user?.groups?.map((x) => x.teamId) ?? []));

    const { data } = await this.state.graphql.rawRequest(
      hasDueDate ? CourseCatalogDueFirstQuery : CourseCatalogQuery,
      {
        id: getId(this.user),
        teams: teams,
        limit: paging.size,
      }
    );
    if (!data) {
      return [] as UserTrackingItem[];
    }
    const items = mapCollection(data, 'course_enrollmentsCollection').map(
      (x) =>
        ({
          ...mapCollection(x, 'user_trackingsCollection').at(0),
          enrolledDate: x['created_at'],
          enrollment: x['id'],
          dueDate: x['dueDate'],
          item: x['courses'],
          course: x['courses'],
          type: 'COURSE',
          status: this.setTrackingStatus(x as UserTrackingItem),
        } as any)
    ) as UserTrackingItem[];
    return items;
  }

  async getMyLearningPaths(
    query?: string,
    hasDueDate = false,
    paging = {
      page: 0,
      size: 4,
    }
  ) {
    const teams = this.user?.teams?.map((x) => x.teamId) ?? [];
    teams.push(...(this.user?.groups?.map((x) => x.teamId) ?? []));

    const { data } = await this.state.graphql.rawRequest(
      hasDueDate ? PathCatalogDueFirstQuery : PathCatalogQuery,
      {
        id: getId(this.user),
        teams: teams,
        limit: paging.size,
      }
    );
    if (!data) {
      return [] as UserTrackingItem[];
    }
    const items = mapCollection(data, 'instructor_enrollmentsCollection').map(
      (x) =>
        ({
          ...mapCollection(x, 'user_trackings_comboCollection').at(0),
          enrolledDate: x['created_at'],
          enrollment: x['id'],
          dueDate: x['dueDate'],
          item: x['learning_instructors'],
          type: x['learning_instructors']['type'],
          status: this.setTrackingStatus(x as UserTrackingItem),
        } as any)
    ) as UserTrackingItem[];
    return items;
  }

  // async getMyAssignments() {
  //   const { data, error } = await this.state.supabase.rpc(
  //     'get_my_assignments',
  //     {
  //       filter: {
  //         userId: getId(this.user),
  //       },
  //     }
  //   );
  //   return {
  //     dueItems: (data?.dueCourses['json_agg'] ?? []).map(
  //       (c: any) => c as UserTrackingItem
  //     ) as UserTrackingItem[],
  //     noDueItems: (data?.courses['json_agg'] ?? []).map(
  //       (c: any) => c as UserTrackingItem
  //     ) as UserTrackingItem[],
  //     error: error?.message,
  //   };
  // }

  // async getMyLearningInstructor() {
  //   const teams = this.user?.teams?.map((x) => x.teamId) ?? [];
  //   teams.push(...(this.user?.groups?.map((x) => x.teamId) ?? []));

  //   const { data } = await this.state.graphql.rawRequest(CourseCatalogQuery, {
  //     id: getId(this.user),
  //     teams: teams,
  //     limit: 4,
  //   });
  // }

  // async getAssignments() {
  //   const studentId = this.user?.id;
  //   if (!studentId) {
  //     return {
  //       data: {
  //         noDueItems: [] as UserTrackingItem[],
  //         dueItems: [] as UserTrackingItem[],
  //       },
  //       error: 'Please refresh this page',
  //     };
  //   }
  //   const [resDue, resNoDue] = await Promise.all([
  //     await this.state.supabase
  //       .from('user_trackings')
  //       .select(
  //         '*, course_enrollments(created_at, courses(id,name,short,description,cover,venue,duration))'
  //       )
  //       .lte('dueDate', dayjs().add(7, 'days').toDate().toISOString())
  //       .order('dueDate', { ascending: false })
  //       .eq('user', studentId)
  //       .limit(4),
  //     await this.state.supabase
  //       .from('user_trackings')
  //       .select(
  //         '*, course_enrollments(created_at, courses(id,name,short,description,cover,venue,duration))'
  //       )
  //       .or('dueDate.is.null')
  //       .order('created_at', { ascending: false })
  //       .eq('user', studentId)
  //       .limit(4),
  //   ]);

  //   const dataNoDue = (resNoDue.data ?? []).map(
  //     (c) =>
  //       ({
  //         ...c,
  //         course: c.enrollments.courses,
  //         enrolledDate: c.enrollments.created_at,
  //       } as UserTrackingItem)
  //   );
  //   const dataDue = (resDue.data ?? []).map(
  //     (c) =>
  //       ({
  //         ...c,
  //         course: c.enrollments.courses,
  //         enrolledDate: c.enrollments.created_at,
  //       } as UserTrackingItem)
  //   );
  //   return {
  //     data: {
  //       noDueItems: dataNoDue,
  //       dueItems: dataDue,
  //     },
  //     error: resDue.error?.message ?? resNoDue.error?.message,
  //   };
  // }

  async getUserAssignedGroups(userId: string) {
    const res = await this.state.supabase
      .from('user_groups')
      .select('team, group')
      .eq('user', userId);

    return {
      teams: (res.data?.map((x) => x.team).filter(Boolean) || []) as string[],
      groups: (res.data?.map((x) => x.group).filter(Boolean) || []) as string[],
    };
  }

  calculateDuration(startDate: Date, endDate: Date) {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    if (start.isAfter(end)) {
      return -1;
    }
    return end.diff(start, 'day');
  }
  setTrackingStatus(item: UserTrackingItem) {
    if (!item.dueDate) return item.status;

    const today = dayjs();
    const dueDate = dayjs(item.dueDate);

    if (dueDate.isBefore(today)) {
      return TraceStatus.PAST;
    }

    if (dueDate.diff(today, 'day') <= 5) {
      return TraceStatus.DUE;
    }
    return item.status;
  }
}
