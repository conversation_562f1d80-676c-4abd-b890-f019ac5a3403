name: Main CI and CD

# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the main branch
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:

      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2

      # Runs a single command using the runners shell
      - name: Installing packages
        run: npm i --legacy-peer-deps

      # Runs a set of commands using the runners shell
      - name: Run a build
        run: npm run ci

      # - name: Copy Player To Admin
      #   shell: bash
      #   run: mkdir -p dist/admin/browser/course-player/browser && cp -r dist/player/browser/ dist/admin/browser/course-player/

      # - name: Copy Player To Learner
      #   shell: bash
      #   run: mkdir -p dist/learner/browser/course-player/browser && cp -r dist/player/browser/ dist/learner/browser/course-player/

      - name: Detele Config File - Admin App
        uses: JesseTG/rm@v1.0.2
        with:
          path: dist/shell/browser/env.json

      # - name: Detele Config File - Learner App
      #   uses: JesseTG/rm@v1.0.2
      #   with:
      #     path: dist/learner/browser/env.json

      # Copy Verkademy Configuration
      - name: Re-create Config File - Admin App
        id: create-vk-admin-json
        uses: jsdaniell/create-json@1.1.2
        with:
          name: "env.json"
          json: ${{ secrets.ENV_VK_STG_CONFIG_JSON }}
          dir: 'dist/shell/browser/'

      # - name: Re-create Config File - Learner App
      #   id: create-vk-learner-json
      #   uses: jsdaniell/create-json@1.1.2
      #   with:
      #     name: "env.json"
      #     json: ${{ secrets.ENV_VK_STG_CONFIG_JSON }}
      #     dir: 'dist/learner/browser/'

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-2

      # Copy dist file to Verkademy S3
      # Runs a set of commands using the runners shell
      # - name: Copy Files to VK Admin App
      #   run: aws s3 sync ./dist/admin/browser s3://verkadalmsadmin

      # # Runs a set of commands using the runners shell
      # - name: Copy Files to VK Learner
      #   run: aws s3 sync ./dist/learner/browser s3://verkadalmslearner

      # Copy LearnOrTeach Configuration
      # - name: Detele Config File - LMS Admin App
      #   uses: JesseTG/rm@v1.0.2
      #   with:
      #     path: dist/admin/browser/env.json

      # - name: Detele Config File - LMS Player
      #   uses: JesseTG/rm@v1.0.2
      #   with:
      #     path: dist/player/browser/env.json

      # - name: Detele Config File - LMS Learner App
      #   uses: JesseTG/rm@v1.0.2
      #   with:
      #     path: dist/learner/browser/env.json

      # - name: Re-create Config File - LMS Admin App
      #   id: create-lot-admin-json
      #   uses: jsdaniell/create-json@1.1.2
      #   with:
      #     name: "env.json"
      #     json: ${{ secrets.ENV_CONFIG_JSON }}
      #     dir: 'dist/admin/browser/'
          
      # - name: Re-create Config File - LMS Player
      #   id: create-lot-player-json
      #   uses: jsdaniell/create-json@1.1.2
      #   with:
      #     name: "env.json"
      #     json: ${{ secrets.ENV_CONFIG_JSON }}
      #     dir: 'dist/player/browser/'

      # - name: Re-create Config File - LMS Learner App
      #   id: create-lot-learner-json
      #   uses: jsdaniell/create-json@1.1.2
      #   with:
      #     name: "env.json"
      #     json: ${{ secrets.ENV_CONFIG_JSON }}
      #     dir: 'dist/learner/browser/'

      - name: Remove from S3 lms-web-new
        uses: vitorsgomes/s3-rm-action@master
        with:
          args: --recursive
        env:
          AWS_S3_BUCKET: lms-web-new
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2
          
      # - name: Remove from S3 learner-staging-app
      #   uses: vitorsgomes/s3-rm-action@master
      #   with:
      #     args: --recursive
      #   env:
      #     AWS_S3_BUCKET: learner-staging-app
      #     AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      #     AWS_REGION: us-east-2
      # Copy dist file to LearnOrTeach S3
      # Runs a set of commands using the runners shell
      - name: Copy Files to LMS Admin App
        run: aws s3 sync ./dist/shell/browser s3://lms-web-new

      # Runs a set of commands using the runners shell
      # - name: Copy Files to LMS Learner
      #   run: aws s3 sync ./dist/learner/browser s3://learner-staging-app

      # # Runs a set of commands using the runners shell
      # - name: Copy Files to LMS Player
      #   run: aws s3 sync ./dist/player/browser s3://lms-lot-player

      # AWS Invalidate CloudFront
      - name: Invalidate CloudFront ADMIN VK
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.ADMIN_VK_STG_DISTRIBUTION }}
          PATHS: "/*"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2

      - name: Invalidate CloudFront ADMIN LMS
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ secrets.ADMIN_LMS_STG_DISTRIBUTION }}
          PATHS: "/*"
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: us-east-2

      # - name: Invalidate CloudFront LEARNER VK
      #   uses: chetan/invalidate-cloudfront-action@v2
      #   env:
      #     DISTRIBUTION: ${{ secrets.LEARNER_VK_STG_DISTRIBUTION }}
      #     PATHS: "/*"
      #     AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      #     AWS_REGION: us-east-2

      # - name: Invalidate CloudFront LEARNER LMS
      #   uses: chetan/invalidate-cloudfront-action@v2
      #   env:
      #     DISTRIBUTION: ${{ secrets.LEARNER_LMS_STG_DISTRIBUTION }}
      #     PATHS: "/*"
      #     AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      #     AWS_REGION: us-east-2
          
