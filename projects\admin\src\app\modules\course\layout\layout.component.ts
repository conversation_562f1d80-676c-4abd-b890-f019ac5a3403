import { Component, inject, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { CourseCoreService, ToastMessageType } from '@lms/core';
import { UploadScormFormComponent } from '../components/upload/upload-form.component';
import { Router, RouterOutlet } from '@angular/router';
import { InstructorFormComponent } from '../../advanced/components/form/form.component';

@Component({
  selector: 'app-course-container',
  imports: [RouterOutlet],
  templateUrl: 'layout.component.html',
})
export class CourseLayoutComponent implements OnInit {
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  readonly router = inject(Router);
  tab = 1;

  type = this.service.viewType;

  get viewSingle() {
    return (
      window.location.pathname.includes('view') ||
      window.location.pathname.includes('lesson-builder')
    );
  }

  ngOnInit(): void {
    this.tab = window.location.pathname.includes('advanced') ? 2 : 1;
    this.service.viewType.set(this.tab === 1 ? 'COURSE' : 'LEARNINGPATH');
  }

  add() {
    if (this.tab === 1) {
      this.dialog.open(UploadScormFormComponent, {
        minWidth: '800px',
      });
      return;
    }
    this.dialog.open(InstructorFormComponent, {
      width: '600px',
    });
  }

  setTab(tab: number) {
    this.tab = tab;
    this.router.navigate([tab === 1 ? '/lms/courses' : '/lms/advanced']);
    this.service.viewType.set(tab === 1 ? 'COURSE' : 'LEARNINGPATH');
  }
}
