import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  resource,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  getId,
  GroupItem,
  Team,
  TeamCoreService,
  ToastMessageType,
  UsersCoreService,
} from '@lms/core';
import { markControlsDirty } from '@lms/core';

@Component({
  selector: 'app-group-form',
  imports: [FormsModule, ReactiveFormsModule],
  templateUrl: './group-form.component.html',
})
export class GroupFormComponent implements OnInit {
  readonly service = inject(TeamCoreService);
  userService = inject(UsersCoreService);

  @Input() data: {
    item?: GroupItem;
    team?: Team;
  };

  @Output() close = new EventEmitter<{
    action: 'BACK' | 'SAVE' | 'CANCEL';
  }>();

  isLoading = false;
  error?: string;

  form = new FormGroup({
    name: new FormControl('', Validators.required),
    groups: new FormArray([
      new FormGroup({
        name: new FormControl(''),
        error: new FormControl(false),
      }),
    ]),
  });

  get f() {
    return this.form.controls;
  }

  get groups() {
    return this.form.controls['groups'] as FormArray;
  }

  teamSource = resource({
    loader: () => this.service.queryTeams(),
  });

  groupOptions: GroupItem[] = [];

  ngOnInit(): void {
    if (!this.data.item?.id) {
      this.form.get('name')?.clearValidators();
      this.form.get('name')?.updateValueAndValidity();
    }
    if (this.data.item) {
      this.form.patchValue({
        name: this.data.item.name,
      });
    }
  }

  addGroup(form: any) {
    if (!form.get('name')?.value?.trim()) {
      form.get('error')?.setValue(true);
      return;
    }
    form.get('error')?.setValue(false);
    this.groups.push(
      new FormGroup({
        name: new FormControl(''),
        error: new FormControl(false),
      })
    );
  }

  removeGroup(index: number) {
    this.groups.removeAt(index);
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.isLoading = true;
    let payload = [
      {
        id: this.data.item?.id,
        name: this.form.value.name,
      },
    ] as any[];
    if (!this.data.item?.id && this.data.team?.id) {
      payload = this.form
        .getRawValue()
        .groups.filter((x: any) => !!x.name)
        .map(
          (x: any) =>
            ({
              name: x.name,
              organization: getId(this.service.organization),
              isdefault: false,
              team: this.data.team?.id,
            } as GroupItem & any)
        );
    }
    const res = await this.service.saveGroup(payload);
    this.isLoading = false;
    if (res.error.length) {
      this.error = res.error[0];
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error[0],
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Group saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.close.emit({ action: 'SAVE' });
  }
}
