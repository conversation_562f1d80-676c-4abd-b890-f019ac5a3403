<div class="flex flex-col gap-6">
  <app-resource-loader [source]="courseSource" />
</div>

@if (!isPlaying && course(); as info) {
<div class="flex gap-6 mt-10">
  <div class="flex-1">
    <div class="flex flex-col gap-5">
      <div
        class="relative w-full h-[480px] opacity-85 transition-all hover:opacity-100 text-white rounded-3xl flex justify-center items-center group"
      >
        <img
          [src]="info.cover"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <a
          href="javascript:void(0)"
          (click)="launchCourse(info, tracking())"
          class="absolute rounded-3xl inset-0 bg-lot-dark/40 bg-opacity-50 text-white flex flex-col justify-center items-center group-hover:top-[40%] group-hover:left-[40%] group-hover:size-40 group-hover:bg-white group-hover:text-lot-dark"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="size-16"
          >
            <path
              fill-rule="evenodd"
              d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="font-bold">
            {{
              tracking()?.id
                ? tracking()?.status === "COMPLETED"
                  ? "Review Course"
                  : "Continue"
                : "Start Course"
            }}
          </span>
        </a>
      </div>
      <div
        class="text-sm font-medium text-gray-500 border-b border-gray-200"
      >
      <h3 class="text-lot-dark text-2xl flex items-center gap-2 mb-2">
        <span class="material-symbols-outlined">book_5</span>
        {{info.name}}</h3>
      <div class="flex w-full justify-between items-center gap-5">
        <app-progress class="w-2/3"
          [status]="tracking()?.status!"
          [progress]="tracking()?.progress || 1"
        />
        <p>{{
          tracking()?.progress
            ? tracking()?.progress + "% complete - Keep Going"
            : "0% complete - Time to Start!"
        }}</p>
      </div>
        <ul class="flex flex-wrap -mb-px">
          @for (item of tabs(); track $index) {
          <li class="me-2">
            <a
              href="javascript:void(0)"
              (click)="tab = item.id"
              class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
              [class.text-lot-dark]="tab === item.id"
              [class.border-lot-blue]="tab === item.id"
            >
              {{ item.name }}
            </a>
          </li>
          }
        </ul>
      </div>

      <div class="flex mb-20 max-w-screen-md">
        @if (tab === 1) {
        <app-ui-html-wrapper
          class="text-justify whitespace-nowrap text-wrap break-all w-full"
          [content]="info.description!"
        />
        } @if (tab === 2) {
        <ng-container
          *ngTemplateOutlet="authorView; context: { $implicit: info.creator }"
        />
        } @if (tab === 3) {
        <ng-container *ngTemplateOutlet="ratingView; context: { rating: 0 }" />
        } @if (tab === 4) {
        <div class="flex flex-col gap-1 w-[389px] md:w-[440px]">
          @for (item of [1,2,3,4]; track $index) {
          <div
            class="flex items-center justify-between p-4 border-b border-lot-gray"
          >
            <div class="flex items-center">
              <span
                class="material-symbols-outlined mr-4 border border-lot-dark-gray p-3 rounded-lg"
                >description</span
              >
              <div>
                <p class="font-semibold">File Title {{ item }}</p>
                <p class="text-sm text-gray-500">PDF</p>
              </div>
            </div>
            <button class="button-primary">View</button>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>
  <div class="w-[389px]">
    <div
      class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
    >
      <ul class="flex flex-wrap">
        @for (item of [1,2]; track $index) {
        <li class="me-2">
          <a
            href="javascript:void(0)"
            (click)="extraTab = item"
            class="inline-block font-bold text-lg p-2 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
            [class.text-lot-dark]="extraTab === item"
            [class.border-lot-blue]="extraTab === item"
          >
            {{ item === 1 ? "LearnMate" : "SkillQuest" }}
          </a>
        </li>
        }
      </ul>
    </div>

    @if (extraTab === 1) {
    <div
      class="bg-gradient-to-r from-lot-ai-dark to-lot-ai rounded-xl p-[2px] mt-6"
    >
      <div
        class="flex flex-col gap-3 h-[554px] px-4 py-5 rounded-[9px] bg-lot-light-gray"
      >
        <div class="flex-grow">
          <div class="flex flex-col justify-center items-center gap-3">
            <p class="text-center">Any questions about this course?</p>
            <div class="flex flex-col items-center">
              <span class="italic font-semibold">Simply ask the</span>
              <span class="text-lot-ai-dark text-2xl font-bold">LearnMate</span>
              <span class="text-lot-ai-dark text-2xl font-bold"
                >AI Assistant
              </span>
            </div>
          </div>
        </div>
        <div class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5">
          <input
            type="search"
            name="search"
            id="search"
            class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
            placeholder="Search by Name"
          />
          <a
            href="javascript:void(0)"
            class="bg-lot-ai-dark text-white p-2 rounded-md"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="size-6 -rotate-45"
            >
              <path
                d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
              />
            </svg>
          </a>
        </div>
      </div>
    </div>

    } @if (extraTab === 2) {}
  </div>
</div>
} @if(isPlaying) {
<div class="flex flex-col gap-8 justify-center items-center py-20">
  <div class="flex flex-col justify-center items-center gap-3 mt-20">
    <span class="text-lot-dark text-center text-2xl"
      >Course training in progress ...</span
    >
    <div class="flex gap-5">
      @for (item of [1,2,3,4]; track $index) {
      <span class="size-6 bg-lot-blue rounded-full animate-pulse"></span>
      }
    </div>
  </div>
  <button
    type="button"
    (click)="exit()"
    class="button-primary-outline w-fit px-6"
  >
    EXIT
  </button>
</div>
} 

@if(!course()) {
<p class="text-center py-4">No active loads currently.</p>
}

<ng-template #ratingView let-rating>
  <div class="flex flex-col gap-5 w-full">
    <div class="flex flex-col gap-2">
      <span class="text-lot-dark-gray font-semibold">Your Rating</span>
      <app-ui-start-rating
        size="lg"
        [readOnly]="disableFeedback"
        [rate]="rating || 0"
        (rating)="rating.set($event)"
      />
    </div>

    <div class="rounded-md border border-lot-gray p-6">
      <textarea
        name="comment"
        id="comment"
        [disabled]="disableFeedback"
        (input)="comments.set($any($event.target).value)"
        class="bg-transparent border-0 w-ful placeholder:text-lot-dark outline-none pl-1"
        placeholder="Write a Review"
        rows="6"
      ></textarea>
    </div>
    @if (!disableFeedback) {
    <button type="button" (click)="saveFeedback()" class="button-primary py-2 w-fit">Post your review</button>
    }
  </div>
</ng-template>

<ng-template #authorView let-author>
  <div class="flex flex-col gap-5">
    <div class="flex gap-5">
      <div
        class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
      >
        <img
          [src]="author.avatar"
          alt=""
          srcset=""
          class="w-full h-full object-cover"
        />
      </div>
      <div>
        <h3 class="font-semibold text-lot-blue text-xl">{{ author.name }}</h3>
        <p class="text-xs text-lot-dark">{{ author.email }}</p>
      </div>
    </div>
    <div>
      <p>
        {{ author.bio }}
      </p>
    </div>
  </div>
</ng-template>
