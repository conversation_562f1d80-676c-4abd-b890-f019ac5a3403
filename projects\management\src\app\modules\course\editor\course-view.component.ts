import { Component, inject, input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { CourseCoreService, Lesson, Module } from '@lms/core';
import { ResourceHeaderComponent } from '@lms/shared';
import { CourseBuilderComponent } from './builder/builder.component';
import { CourseSettingsComponent } from './settings/settings.component';
import { Router, RouterLink } from '@angular/router';
import { CourseAssignmentComponent } from './assignment/assign-team.component';

@Component({
  selector: 'app-course-view',
  imports: [
    ResourceHeaderComponent,
    CourseBuilderComponent,
    CourseSettingsComponent,
    NgTemplateOutlet,
    CourseAssignmentComponent,
  ],
  templateUrl: 'course-view.component.html',
})
export class CourseViewComponent implements OnInit, OnDestroy {
  readonly router = inject(Router);
  readonly service = inject(CourseCoreService);
  id = input<string>('');

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'Course Structure',
    },
    {
      id: 2,
      name: 'Course Settings',
    },
    {
      id: 3,
      name: 'Assign Audience',
    },
  ];

  get view() {
    return this.service.state.viewType();
  }

  get isSettings() {
    return this.service.viewType() === 'SETTINGS';
  }

  content?: { module: Module; lesson: Lesson };

  source = this.service.courseSource;

  isPreview = false;
  windowRef: any;

  ngOnInit(): void {
    this.service.courseId.set(this.id());
  }

  back() {
    this.router.navigate(['/lms/courses']);
  }

  preview(id: string) {
    // if (this.tab === 3) {
    //   this.service.enrollTrigger.next({
    //     course: this.source.value()!,
    //   });
    //   return;
    // }
    
    this.isPreview = true;
    const query = new URLSearchParams({
      id,
      source: 'ADMIN',
    });

    // sessionStorage.setItem('_preview_', query.toString());
    // this.windowRef = window.open(
    //   `${this.service.state.envConfig.env.playerUrl}?${query.toString()}`,
    //   'LearnOrTeach',
    //   'toolbar=no,location=no,status=no,menubar=0,scrollbars=no,left=100,top=100,height=800,width=1024'
    // );
    
    this.windowRef = window.open(
      `go-training?${query.toString()}`,
      // `${
      //   this.service.state.envConfig.env.playerUrl ||
      //   'http://lms-lot-player.s3-website.us-east-2.amazonaws.com'
      // }?${query.toString()}`,
      // `http://lms-lot-player.s3-website.us-east-2.amazonaws.com/?${query.toString()}`,
      // `course-player?${query.toString()}`,
      'LearnOrTeach',
      'popup'
    );
    this.windowRef?.focus();
  }

  onView(item: { module: Module; lesson: Lesson }) {
    this.content = item;
    this.service.state.viewType.set('LESSON');
  }

  setTab(tab: number) {
    this.tab = tab;
    if (tab === 2) {
      this.service.viewType.set('SETTINGS');
    }
  }
  exit() {
    this.windowRef?.close();
    this.isPreview = false;
  }
  ngOnDestroy(): void {
    this.service.state.viewType.set('COURSE');
    this.exit();
  }
}
