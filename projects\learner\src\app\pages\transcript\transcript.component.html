<div class="flex flex-col gap-6">
  <div class="flex flex-col gap-5 bg-white rounded-3xl p-7">
    <div class="flex justify-between">
      <h2 class="text-lot-dark">Transcript for <PERSON></h2>
      <button class="button-primary">Download</button>
    </div>
    <div class="flex justify-between">
      <div class="flex flex-col">
        <div
          class="relative w-10 h-10 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
        >
          <svg
            class="absolute w-12 h-12 text-gray-400 -left-1"
            fill="currentColor"
            viewBox="0 0 20 20"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
              clip-rule="evenodd"
            ></path>
          </svg>
        </div>
        <h3 class="font-semibold text-lot-blue text-xl"><PERSON></h3>
        <p class="text-xs text-lot-dark">jane.d&#64;learnorteach.com</p>
      </div>
      <div class="flex gap-5">
        <div
          class="w-[170px] py-4 flex flex-col justify-center items-center bg-lot-light-blue/10 rounded-md text-center gap-0"
        >
          <span class="font-bold text-[40px] text-lot-blue py-0 leading-none"
            >5</span
          >
          <span class="text-sm text-lot-dark leading-none"
            >certificates earned</span
          >
        </div>
        <div
          class="w-[170px] py-4 flex flex-col justify-center items-center gap-0 bg-lot-light-blue/10 rounded-md text-center"
        >
          <span class="font-bold text-[40px] text-lot-blue py-0 leading-none"
            >12</span
          >
          <span class="text-sm text-lot-dark py-0 leading-none"
            >courses completed</span
          >
        </div>
      </div>
    </div>
  </div>

  <div class="flex flex-col gap-5 mt-10">
    <h2 class="font-semibold">Certificates ({{ certificates.length }})</h2>

    <div class="relative overflow-x-auto">
      <table class="w-full text-left rtl:text-right">
        <thead class="text-sm text-lot-dark-gray italic border-b">
          <tr>
            <th scope="col" class="py-3 font-normal">Certificate name</th>
            <th scope="col" class="px-6 py-3 font-normal">Valid From</th>
            <th scope="col" class="px-6 py-3 font-normal">Expires On</th>
            <th scope="col" class="px-6 py-3 font-normal"></th>
          </tr>
        </thead>
        <tbody>
          @for (item of certificates; track $index) {
          <tr class="border-b border-gray-200 text-lot-dark">
            <td scope="row" class="py-4 font-medium whitespace-nowrap w-full">
              {{ item.name }}
            </td>
            <td class="p-4 min-w-44">
              <div class="flex flex-col text-sm">
                <span class="font-semibold">{{
                  item.created_at | date : "longDate"
                }}</span>
                <span>{{ item.created_at | date : "h:mm a" }}</span>
              </div>
            </td>
            <td class="p-4 min-w-44">
              <div class="flex flex-col text-sm">
                <span class="font-semibold">{{
                  item.expiredDate | date : "longDate"
                }}</span>
                <span>{{ item.expiredDate | date : "h:mm a" }}</span>
              </div>
            </td>
            <td class="p-4 flex items-center justify-center gap-2">
              <button class="button-primary">View</button>
              <button class="button-primary-outline">Download</button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>

  <div class="flex flex-col gap-5 mt-10 mb-24">
    <h2 class="font-semibold">
      Courses & Learning Paths ({{ courses.length }})
    </h2>

    <div class="relative overflow-x-auto">
      <table class="w-full text-left rtl:text-right">
        <thead class="text-sm text-lot-dark-gray italic border-b">
          <tr>
            <th scope="col" class="py-3 font-normal">Name</th>
            <th scope="col" class="p-4 font-normal">Status</th>
            <th scope="col" class="p-4 font-normal">Score</th>
            <th scope="col" class="p-4 font-normal">Enrolment Date</th>
            <th scope="col" class="p-4 font-normal">Completion Date</th>
            <th scope="col" class="p-4 font-normal"></th>
          </tr>
        </thead>
        <tbody>
          @for (item of courses; track $index) {
          <tr class="border-b border-gray-200 text-lot-dark">
            <td scope="row" class="py-4 font-medium whitespace-nowrap w-full">
              {{ item.name }}
            </td>
            <td class="p-4">
              <span>{{ item.status }}</span>
            </td>
            <td class="p-4">
              <span>{{ item.score }}%</span>
            </td>
            <td class="p-4 min-w-44">
              <div class="flex flex-col text-sm">
                <span class="font-semibold">{{
                  item.created_at | date : "longDate"
                }}</span>
                <span>{{ item.created_at | date : "h:mm a" }}</span>
              </div>
            </td>
            <td class="p-4 min-w-44">
              <div class="flex flex-col text-sm">
                <span class="font-semibold">{{
                  item.completionDate | date : "longDate"
                }}</span>
                <span>{{ item.completionDate | date : "h:mm a" }}</span>
              </div>
            </td>
            <td class="p-4 flex items-center justify-center gap-2">
              <button class="button-primary">View</button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
