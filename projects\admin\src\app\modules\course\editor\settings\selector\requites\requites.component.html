<div class="flex flex-col gap-5 p-6">
  <app-resource-loader [source]="courseSource" />
  @if (courseSource.value(); as item) {
  <div class="flex flex-col gap-2 p-4 rounded-xl border">
    <h4 class="text-lot-dark font-semibold mb-2">
      ({{ item.data.length }}/{{ item.count }}) - courses
    </h4>
    <div
      class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-2"
    >
      <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
      <input
        type="search"
        name="search"
        id="search"
        (input)="filter.set($any($event.target).value)"
        class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
        placeholder="Search Course"
      />
    </div>
    <ul
      class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
    >
      @for (item of item.data; track item) {
      <li class="flex justify-start items-center gap-3 border-b py-2 group">
        <input
          type="checkbox"
          [(ngModel)]="item.selected"
          (change)="onSelectionChange($any($event.target).checked, item)"
        />
        <span>{{ item.name }}</span>
      </li>
      } @empty {
      <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
      }
    </ul>
  </div>
  }
  <div class="flex justify-end items-center gap-3">
    <button
      class="button-primary-outline w-fit"
      (click)="dialogRef.close()"
      type="button"
    >
      Cancel
    </button>
    <button
      class="button-primary w-fit py-1.5"
      type="button"
      [disabled]="!courses.length"
      (click)="saveSelection()"
    >
      Add{{ courses.length > 1 ? ` (${courses.length}) ${data === 'PRE' ? 'Pre-requisites' : 'Post-requisites'}` : (data === 'PRE' ? ' Pre-requisite' : ' Post-requisite') }}
    </button>
  </div>
</div>
