import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatPaginatorModule } from '@angular/material/paginator';
import { AccountStatus, UserItem, UsersCoreService } from '@lms/core';
import { FloatingComponent, ResourceHeaderComponent } from '@lms/shared';
import { UserFormComponent } from '../../user/users/form/user-form.component';
import { UserSubComponent } from './list/list.component';

@Component({
  selector: 'app-billing-active-users',
  imports: [ResourceHeaderComponent, FloatingComponent, UserSubComponent],
  templateUrl: './active-users.component.html',
})
export class BillingActiveUsersComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  service = inject(UsersCoreService);

  source = this.service.subscribeUsersSource;
  isLoading = false;

  tab = 1;
  sortBy = [
    { id: 'loginASC', name: 'Last Login' },
    { id: 'nameASC', name: 'A to Z' },
    { id: 'nameDESC', name: 'Z to A' },
    { id: 'createdDateASC', name: 'Oldest' },
    { id: 'createdDateDESC', name: 'Latest' },
  ];
  search = signal<string | undefined>(undefined);
  sort = signal<string | undefined>(undefined);

  users = computed(() => (this.source.value()?.users || []) as UserItem[]);
  total = computed(() => this.source.value()?.total || 0);

  ngOnInit(): void {
    this.service.filterSub.set({
      type: 'active30',
      paging: {
        page: 1,
        size: 10,
      },
    });
  }

  onTab(tab: number){
    this.tab = tab;
    this.service.filterSub.set({
      type: tab === 1 ? 'active30' : tab === 2 ? 'activePass' : 'activeNone',
      paging: {
        page: 1,
        size: 10,
      },
    });
  }
  action({ type, data }: { type: 'edit' | 'delete' | 'view'; data: UserItem }) {
    if (type === 'edit') {
      this.dialog.open(UserFormComponent, {
        minWidth: '800px',
        data: {
          type: 'EDIT',
          item: data,
        },
      });
    }
  }

  exportToCsv() {
    // this.docService.exportCSV([
    //   'Name',
    //   'Email',
    //   'Role',
    //   'Last Active',
    // ], this.dataSource.data.map((s) => ({
    //   'name': s.name,
    //   'email': s.email,
    //   'role': s.role,
    //   'date': dayjs(s.date).format('LLL'),
    // })));
  }

  exportToPdf() {
    // this.printPdf = true;
    // setTimeout(async () => {
    //   await this.docService.exportToPDF(this.reportDashboard.nativeElement);
    //   this.printPdf = false;
    // }, 500);
  }
}
