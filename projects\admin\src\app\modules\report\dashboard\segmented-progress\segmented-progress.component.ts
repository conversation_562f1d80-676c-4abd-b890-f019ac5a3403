import { NgClass } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';

export interface Segment {
  label: string;
  value: number; // Percentage, e.g., 55 for 55%
  colorClass: string; // Tailwind CSS class for background color, e.g., 'bg-emerald-500'
}

@Component({
  selector: 'app-segmented-progress-bar',
  imports: [],
  template: ` <div class="w-full">
    <div class="flex {{ barHeight }} {{ roundedClass }} gap-1 overflow-hidden">
      @for (segment of segments; track $index) {
      <div
        class="h-full {{
          segment.colorClass
        }}"
        style="width: {{ segment.value }}%"
        [title]="segment.label + ': ' + segment.value + '%'"
      ></div>
      }
    </div>

    <div class="mt-4 space-y-2">
      @for (segment of segments; track $index) {
      <div class="flex items-center justify-between text-gray-700">
        <div class="flex items-center">
          <span
            class="inline-block w-3 h-3 {{
              segment.colorClass
            }} rounded-full mr-2"
          ></span>
          <span>{{ segment.label }}</span>
        </div>
        <span class="font-semibold">{{ segment.value }}%</span>
      </div>
      }
    </div>
  </div>`,
})
export class SegmentedProgressBarComponent implements OnInit {
  @Input() segments: Segment[] = [];
  @Input() barHeight: string = 'h-5';
  @Input() roundedClass: string = 'rounded-full';

  ngOnInit(): void {
    const total = this.segments.reduce(
      (sum, segment) => sum + segment.value,
      0
    );
    if (total !== 100) {
      console.warn(
        `Segment values do not sum to 100%. Current total: ${total}%`
      );
    }
  }

  getSegmentWidth(value: number): string {
    return `w-[${value}%]`;
  }
}
