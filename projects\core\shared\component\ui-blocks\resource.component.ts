import {
  Component,
  ElementRef,
  EventEmitter,
  inject,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { UIBlockData, WidgetType } from './ui-block.utils';

@Component({
  selector: 'app-block-resource',
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg {{ metaClass }}"
      [style]="styleForm"
      (click)="selectBlock()"
    >
      <!-- Audio Player -->
      @if (content.type === 'audio') {

      <div class="flex flex-col items-center justify-center gap-6 w-full">
        <audio
          controls
          class="w-full h-12 [&::-webkit-media-controls-panel]:bg-lot-blue [&::-webkit-media-controls-current-time-display]:text-white [&::-webkit-media-controls-time-remaining-display]:text-white [&::-webkit-media-controls-timeline]:text-white [&::-webkit-media-controls-play-button]:text-white [&::-webkit-media-controls-mute-button]:text-white rounded-lg"
        >
          <source [src]="url" type="audio/mp3" />
          Your browser does not support the audio element.
        </audio>
        <div class="text-center max-w-2xl">
          @if (content.title) {
          <h3 class="text-lg font-semibold mb-2">{{ content.title }}</h3>
          } @if (content.description) {
          <div
            class="text-lot-dark-gray break-words"
            [innerHTML]="content.description"
          ></div>
          }
        </div>
      </div>
      } @if (content.type === 'video') {
      <div class="flex flex-col items-center justify-center gap-6 w-full">
        @if (content.file.isEmbedded) {
        <iframe
          [src]="url"
          class="w-full aspect-video rounded-lg"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen
        ></iframe>
        } @else {
        <div class="relative w-full">
          <video
            #videoPlayer
            [controls]="isPlaying"
            (timeupdate)="onTimeUpdate($event)"
            class="w-full rounded-lg"
            [class.cursor-pointer]="!isPlaying"
          >
            <source [src]="url" />
            Your browser does not support the video element.
          </video>

          @if (!isPlaying) {
          <div
            (click)="playVideo()"
            class="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg cursor-pointer"
          >
            <div
              class="size-16 rounded-full bg-lot-blue flex items-center justify-center"
            >
              <i class="fas fa-play text-white text-xl"></i>
            </div>
          </div>
          }
        </div>
        }
        <div class="text-center max-w-2xl">
          @if (content.title) {
          <h3 class="text-lg font-semibold mb-2">{{ content.title }}</h3>
          } @if (content.description) {
          <div
            class="text-lot-dark-gray break-words"
            [innerHTML]="content.description"
          ></div>
          }
        </div>
      </div>
      }

      <!-- File Download -->
      @if (content.type === 'file') {
      <div
        class="flex items-center justify-between p-3 border border-lot-gray rounded-lg hover:bg-lot-gray/10 transition-colors"
      >
        <div class="flex items-center gap-3">
          <i class="fas fa-file text-lot-blue text-xl"></i>
          <div class="flex flex-col">
            <span class="font-medium">{{ content.file.name }}</span>
            <span class="text-lot-dark-gray text-sm">{{
              content.file.size
            }}</span>
          </div>
        </div>
        <a
          [href]="content.file.url"
          click="downloadFile()"
          download
          class="flex items-center gap-2 text-lot-blue hover:underline"
        >
          <span>Download</span>
          <i class="fas fa-download"></i>
        </a>
      </div>
      }
    </div>
  `,
})
export class UIBlockResourceComponent {
  private sanitizer = inject(DomSanitizer);

  @ViewChild('videoPlayer') videoPlayer!: ElementRef<HTMLVideoElement>;

  isPlaying = false;
  lastTime = 0;

  @Input() content: {
    readyForLecture: boolean;
    type: 'audio' | 'video' | 'file';
    file: {
      name: string;
      url: string;
      size: string;
      type: string;
      isEmbedded?: boolean;
      preventSkip?: boolean;
    };
    title?: string;
    description?: string;
    requirePass: boolean;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  get url() {
    return this.getSafeUrl(this.content.file.url);
  }

  @Output() view = new EventEmitter<UIBlockData>();

  playVideo() {
    this.isPlaying = true;
    this.videoPlayer.nativeElement.play();
  }

  onTimeUpdate(event: Event) {
    if (!this.content.file.preventSkip) {
      this.view.emit({
        widgetType: WidgetType.VIDEO,
        content: this.content,
        userAnswers: { completed: true },
      });
      return;
    }

    const video = event.target as HTMLVideoElement;
    const skipAttempt = video.currentTime - this.lastTime > 1.5;

    if (skipAttempt) {
      video.currentTime = this.lastTime;
    }

    this.lastTime = video.currentTime;
    const isNearEnd = video.currentTime >= video.duration * 0.95;

    if (isNearEnd) {
      this.view.emit({
        widgetType: WidgetType.VIDEO,
        content: this.content,
        userAnswers: { completed: true },
      });
    }
  }

  getSafeUrl(url: string): SafeResourceUrl {
    //for au
    return this.sanitizer.bypassSecurityTrustResourceUrl(url);
  }

  downloadFile() {
    this.view.emit({
      widgetType: WidgetType.RESOURCE,
      content: this.content,
      userAnswers: { completed: true },
    });
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit({ widgetType: WidgetType.RESOURCE, content: this.content });
  }
}

export const getResourceTemplate = (type: number) => {
  switch (type) {
    case 0: // Audio Resource
      return {
        uiLabel: 'Audio Resource Block',
        type: 'audio',
        file: {
          url: 'https://github.com/rafaelreis-hotmart/Audio-Sample-files/raw/master/sample.mp3',
        },
        title:
          'First Aid, CPR, and AED Training – Life-Saving Skills for the Workplace',
        description: ` This audio lesson, brought to you by Learn Or Teach, equips you with critical knowledge to respond effectively during medical emergencies. Learn how to administer First Aid, perform CPR, and operate an AED with confidence. Ideal for employees, safety teams, and anyone looking to enhance workplace readiness. <br/>
        Visit <a href="www.learnorteach.com">www.learnorteach.com</a> to explore more training solutions.
        `,
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };

    case 1: // PDF Document
      return {
        uiLabel: 'PDF Document Block',
        type: 'file',
        description: `This downloadable guide provides a concise, easy-to-follow reference for First Aid, CPR, and AED procedures. Perfect for on-the-job use or as a companion to your training, it includes step-by-step instructions, key reminders, and emergency contact tips. Provided by Learn Or Teach — your partner in workplace safety and training.
        `,
        file: {
          name: 'First Aid, CPR, and AED – Quick Reference Guide',
          url: 'assets/docs/syllabus.pdf',
          size: '1.2 MB',
          type: 'application/pdf',
        },
        meta: {
          width: 100,
          padding: 4,
          // background: 'lot-blue',
          // color: 'white',
        },
      };

    default: // Default Configuration
      return {
        uiLabel: 'ZIP Archive Block',
        type: 'file',
        file: {
          name: 'First Aid, CPR, and AED – Quick References',
          url: 'assets/downloads/project.zip',
          size: '5.7 MB',
          type: 'application/zip',
        },
        meta: {
          width: 100,
          padding: 4,
          background: 'lot-dark',
          color: 'white',
        },
      };
  }
};

export const getVideoTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Video Upload Block',
        type: 'video',
        file: {
          name: 'Video Upload Block',
          url: 'https://drive.google.com/file/d/18xb-N1svOgeYFhGM7qc7b-TibjXkI0g6/view?usp=sharing',
          size: '15.8 MB',
          type: 'video/mp4',
          isEmbedded: false,
          preventSkip: true,
        },
        meta: {
          margin: 2,
          background: 'lot-white',
          color: 'lot-blue',
        },
      };

    default: // Default Configuration
      return {
        uiLabel: 'YouTube Link Block',
        type: 'video',
        file: {
          name: 'YouTube Video Block',
          url: 'https://www.youtube.com/embed/w3GqOV-8Txk',
          size: 'Streaming',
          type: 'video/youtube',
          isEmbedded: true,
        },
        meta: {
          padding: 8,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };
  }
};
