<div class="flex flex-col w-full">
  <div
    class="text-sm font-medium text-center flex justify-between items-center"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="setTab(item.id)"
          class="inline-block font-bold text-sm p-2 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
          [class.text-lot-dark]="tab === item.id"
          [class.border-transparent]="tab !== item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
    <div class="flex items-center gap-4">
      <app-floating
        [with]="'fit'"
        [title]="'Sort By'"
        [options]="sortBy"
        (selected)="sort.set($event.id)"
      />
      <div class="flex-1">
        <div class="rounded-md border border-lot-blue flex gap-2 px-5 py-2.5">
          <span class="material-symbols-outlined text-lot-blue">search</span>
          <input
            type="search"
            name="search"
            id="search"
            (input)="search.set($any($event.target).value)"
            class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
            placeholder="Search Courses"
          />
        </div>
      </div>
    </div>
  </div>
  <div class="w-full mt-3">
    <app-resource-loader [source]="source" />
    <app-course-list [total]="totalCount()" [data]="courses()" />
  </div>
</div>
