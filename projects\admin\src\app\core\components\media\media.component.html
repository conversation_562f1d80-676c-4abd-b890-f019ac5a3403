<div class="flex flex-col gap-4 px-10 pt-5 w-full">
  <div class="flex justify-between items-center">
    <h1 class="text-lot-dark text-2xl font-bold">Media Center</h1>
    <button type="button" (click)="view = 'UPLOAD'" class="button-primary">
      Upload
    </button>
  </div>
  <div class="flex gap-5 w-full border-t py-5 max-h-[620px]">
    <section class="flex flex-col w-44 overflow-hidden">
      @for (item of tabs; track $index) {
      <a
        href="javascript:void(0)"
        (click)="tab = item.id"
        class="flex items-center gap-5 hover:bg-lot-light-blue/50 px-5 py-3 rounded-md {{
          tab === item.id ? 'bg-lot-light-blue/50' : ''
        }}"
      >
        <i class="{{ item.icon }} text-xl text-lot-blue"></i>
        <span class="text-sm">{{ item.name }}</span>
      </a>
      }
    </section>
    <div class="flex-grow overflow-y-auto scrollbar px-10 pt-5 pb-10">
      <div class="flex flex-col gap-8">
        @if (view === 'FILE') {
        <ng-container *ngTemplateOutlet="fileView" />
        } @if (view === 'UPLOAD') {
        <ng-container *ngTemplateOutlet="fileUploader" />
        }
      </div>
    </div>
  </div>
</div>

<ng-template #fileView>
  <div class="grid grid-cols-4 gap-4 mt-6">
    @for (item of filterResources('image'); track item.id) {
    <div
      class="relative group cursor-pointer rounded-lg overflow-hidden"
      [class.ring-2]="isSelected(item)"
      [class.ring-lot-primary]="isSelected(item)"
      (click)="toggleSelection(item)"
    >
      <img [src]="item.url" class="w-full h-48 object-cover" />
      <div
        class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity"
      >
        @if (isSelected(item)) {
        <div class="flex items-center justify-center h-full">
          <i class="fas fa-check text-white text-2xl"></i>
        </div>
        }
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #fileUploader>
  <div class="p-4">
    @if (!mediaFile) {
      @if (error) {
        <p class="text-red-500 my-5">{{ error }}</p>
      }
    <app-file-loader
      [type]="tab"
      [reset]="true"
      (sendFile)="onFileUploaded($event)"
    />
    } @if (mediaFile) {
    <div class="flex flex-col gap-2">
      <div class="mb-4">
        <h3 class="text-md font-medium mb-2">Preview uploaded File:</h3>
        <div class="border border-gray-200 rounded p-2">
          @if (selectedType === 'image') {
          <img [src]="previewUrl" alt="Preview" class="max-h-40 mx-auto" />
          } @if (selectedType === 'video') {
          <video controls class="max-h-40 mx-auto">
            <source [src]="previewUrl" [type]="mediaFile.type" />
          </video>
          } @if (!['image', 'video'].includes(selectedType)) {
          <div class="flex items-center justify-center p-4 bg-gray-100">
            <span class="text-4xl">📄</span>
            <span class="ml-2">{{ mediaFile.name }}</span>
          </div>
          }
        </div>
      </div>

      <button (click)="save()" class="button-primary w-fit">Save</button>
    </div>
    }
  </div>
</ng-template>
