import { Certificate } from './certification.model';
import { Common } from './common.model';
import {
  LearningType,
  TraceStatus,
  VisitedLesson,
} from './course-assign.model';
import { Course } from './course.model';

export type LearningInstructor = Common & {
  name: string;
  short?: string;
  description: string;
  cover?: string;
  items: {
    category: string;
    order: number;
    courses: (Course & { order: number })[];
  }[];
  prerequisites: (Course & { order: number })[];
  sessions: InstructorLedSession[];
  creator: {
    name: string;
    email: string;
    bio: string;
    avatar: string;
  };
  resources: {
    name: string;
    url: string;
  }[];
  type: 'INSTRUCTORLED' | 'LEARNINGPATH';
  organization: string;
};

export type InstructorLedSession = {
  id: string;
  name: string;
  url: string;
  location: string;
  startDate: string;
  endDate: string;
  classSize: number;
  instructor: string;
  order: number;
  type: 'PHYSICAL' | 'VIRTUAL';
  enrolledDate?: string;
};

export type InstructorEnrollment = {
  instructor: string;
  user?: string;
  group?: string;
  team?: string;
  dueDate: string;
} & Common;
