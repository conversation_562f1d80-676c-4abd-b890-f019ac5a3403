<div class="flex flex-col h-full max-h-[calc(100vh-100px)] overflow-y-hidden">
  <app-resource-loader [source]="courseSource" />

  @if (courseSource.value()?.course; as item) {

  <div class="w-full flex flex-col h-full">
    <div
      class="w-full overflow-x-hidden border-t flex flex-col h-full scrollbar"
    >
      <main class="w-full h-full flex-grow px-2">
        <div class="flex justify-between gap-6 mt-2 min-h-full">
          <div class="flex-grow {{ viewChat ? 'w-3/4' : 'w-full' }}">
            @if (view === 'COURSE') {
            <ng-container
              *ngTemplateOutlet="courseView; context: { item: item }"
            />} @if (view === 'MODULE') {
            <ng-container *ngTemplateOutlet="courseModule" />
            } @if (view === 'LESSON') {
            <ng-container *ngTemplateOutlet="courseLecture" />
            } @if (view === 'END') {
            <ng-container *ngTemplateOutlet="courseEnd" />
            }
          </div>
          @if (viewChat) {
          <div class="w-1/4 pr-4">
            <div
              class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
            >
              <ul class="flex flex-wrap">
                @for (item of [1,2]; track $index) {
                <li class="me-2">
                  <a
                    href="javascript:void(0)"
                    (click)="extraTab = item"
                    class="inline-block font-bold text-lg p-2 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
                    [class.text-lot-dark]="extraTab === item"
                    [class.border-lot-blue]="extraTab === item"
                  >
                    {{ item === 1 ? "LearnMate" : "SkillQuest" }}
                  </a>
                </li>
                }
              </ul>
            </div>

            @if (extraTab === 1) {
            <div
              class="bg-gradient-to-r from-lot-ai-dark to-lot-ai rounded-xl p-[2px] mt-6"
            >
              <div
                class="flex flex-col gap-3 h-[554px] px-4 py-5 rounded-[9px] bg-lot-light-gray"
              >
                <div class="flex-grow">
                  <div class="flex flex-col justify-center items-center gap-3">
                    <p class="text-center">Any questions about this course?</p>
                    <div class="flex flex-col items-center">
                      <span class="italic font-semibold">Simply ask the</span>
                      <span class="text-lot-ai-dark text-2xl font-bold"
                        >LearnMate</span
                      >
                      <span class="text-lot-ai-dark text-2xl font-bold"
                        >AI Assistant
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5"
                >
                  <input
                    type="search"
                    name="search"
                    id="search"
                    class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
                    placeholder="Search by Name"
                  />
                  <a
                    href="javascript:void(0)"
                    class="bg-lot-ai-dark text-white p-2 rounded-md"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      class="size-6 -rotate-45"
                    >
                      <path
                        d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
                      />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            } @if (extraTab === 2) {}
          </div>
          }
        </div>
      </main>
    </div>
  </div>

  } @else {
  <p class="text-center py-20">Course Preview in progress ...</p>
  <div class="flex justify-center items-center gap-5">
    @for (item of [1,2,3,4]; track $index) {
    <span class="size-6 bg-lot-blue rounded-full animate-pulse"></span>
    }
  </div>
  <a
    href="javascript:void(0)"
    class="text-lot-blue text-center"
    (click)="exit()"
  >
    EXIT
  </a>
  }
</div>

<ng-template #authorView let-author>
  <div class="flex flex-col gap-5">
    <div class="flex gap-5">
      <div
        class="relative size-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600"
      >
        <img
          [src]="author.avatar"
          alt=""
          srcset=""
          class="w-full h-full object-cover"
        />
      </div>
      <div>
        <h3 class="font-semibold text-lot-blue text-xl">{{ author.name }}</h3>
        <p class="text-xs text-lot-dark">{{ author.email }}</p>
      </div>
    </div>
    <div>
      <p>
        {{ author.bio }}
      </p>
    </div>
  </div>
</ng-template>

<ng-template #courseView let-item="item">
  <div class="w-full bg-white rounded-3xl px-7 py-14">
    <div class="flex flex-col gap-5">
      <div
        class="relative w-full h-[480px] opacity-85 transition-all hover:opacity-100 text-white rounded-3xl flex justify-center items-center"
      >
        <img
          src="{{ item.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
      </div>

      <div
        class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
      >
        <ul class="flex flex-wrap -mb-px">
          @for (item of tabs(); track $index) {
          <li class="me-2">
            <a
              href="javascript:void(0)"
              (click)="tab = item.id"
              class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
              [class.text-lot-dark]="tab === item.id"
              [class.border-lot-blue]="tab === item.id"
            >
              {{ item.name }}
            </a>
          </li>
          }
        </ul>
      </div>

      <div class="flex mb-20 px-4 overflow-y-auto">
        @if (tab === 1) {
        <div class="flex flex-col gap-8 w-full break-words max-w-screen-2xl">
          <app-ui-html-wrapper
            class="text-justify w-full"
            [content]="item.description!"
          />

          <div class="w-full border-t pt-5">
            <h3 class="text-lot-blue text-xl font-bold">Course Navigation</h3>
            @for (item of navigations(); track $index; let last=$last) {
            <div
              class="border-gray-200 w-full max-w-4xl justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark flex flex-col"
              [class.border-b]="!last"
            >
              <a
                class="flex justify-between items-center gap-2 uppercase"
                href="javascript:void(0)"
                [class.text-lot-ai-dark]="item.type === 'TOP'"
                [class.hover:text-lot-ai-dark]="item.type === 'TOP'"
                [class.hover:text-lot-blue]="item.type !== 'TOP'"
                [class.text-lot-blue]="item.current"
                [class.font-semibold]="item.current"
                [class.pointer-events-none]="!item.visited && !item.current"
                (click)="gotTo(item)"
              >
                <div class="flex items-center gap-2">
                  <span class="material-symbols-outlined">{{ item.icon }}</span>
                  {{ item.name }}
                </div>
                @if (item.visited) {
                <i class="fa-solid fa-circle-check text-lot-ai"></i>
                }@if (!item.visited && !item.current) {
                <i class="fa-solid fa-lock"></i>
                }
              </a>

              @if (item.items?.length) {
              <div
                class="flex flex-col gap-2 ml-2 py-2 px-5 font-[500] text-base leading-10 text-lot-dark"
              >
                @for (subItem of item.items; track $index) {
                <a
                  class="hover:text-lot-blue flex justify-between items-center gap-2 border-t"
                  [class.text-lot-blue]="subItem.current"
                  [class.font-semibold]="subItem.current"
                  [class.pointer-events-none]="
                    !subItem.visited && !subItem.current
                  "
                  href="javascript:void(0)"
                  (click)="gotTo(subItem)"
                >
                  <div class="flex items-center gap-2">
                    <span class="material-symbols-outlined">{{
                      subItem.icon
                    }}</span>
                    {{ subItem.name }}
                  </div>
                  @if (subItem.visited) {
                  <i class="fa-solid fa-circle-check text-lot-ai"></i>
                  }@if (!subItem.visited && !subItem.current) {
                  <i class="fa-solid fa-lock"></i>
                  }
                </a>
                }
              </div>
              }
            </div>
            }
          </div>
        </div>

        } @if (tab === 2 ) {
        <ng-container
          *ngTemplateOutlet="authorView; context: { $implicit: item.author }"
        />
        } @if (tab === 3) {
        <div class="flex flex-col gap-1 w-[440px]">
          @for (item of [1,2]; track $index) {
          <div
            class="flex items-center justify-between p-4 border-b border-lot-gray"
          >
            <div class="flex items-center">
              <span
                class="material-symbols-outlined mr-4 border border-lot-dark-gray p-3 rounded-lg"
                >description</span
              >
              <div>
                <p class="font-semibold">File Title {{ item }}</p>
                <p class="text-sm text-gray-500">PDF</p>
              </div>
            </div>
            <button class="button-primary">View</button>
          </div>
          }
        </div>
        }
      </div>
    </div>
  </div>
</ng-template>

<ng-template #courseLecture>
  <div
    class="flex flex-col justify-between gap-10 items-center w-full h-full min-h-full"
  >
    <div class="flex-grow w-full max-w-7xl">
      <div class="flex flex-col justify-center items-center gap-2 w-full p-10">
        @if (lessons.length) {
        <div class="flex border-b py-8 w-full">
          <h2 class="text-lot-dark text-4xl font-bold">
            {{ currentLessonIndex + 1 }} - {{ lessons[0].name }} <br />
            <!-- <small class="text-xs text-lot-dark-gray">{{
              currentModule?.name
            }}</small> -->
          </h2>
        </div>
        } @for (item of lectures; track item; let i = $index) {
        <app-dynamic-content
          [data]="item"
          (track)="getTrack($event)"
          class="w-full animate-fade-up animate-delay-900 my-12"
        />
        }
      </div>
    </div>
    <button
      (click)="next()"
      [disabled]="!enableNext"
      class="button-primary w-full py-5 text-lg"
    >
      <span
        >{{ currentLessonIndex + 1 }}/{{ currentModule?.lessons?.length }}</span
      >
      -- Continue
    </button>
  </div>
</ng-template>

<ng-template #courseModule>
  <div class="flex flex-col gap-2 w-full p-10">
    @if (view === 'MODULE' && course && currentModule) {
    <div
      class="flex flex-col justify-center items-center gap-5 max-w-screen-2xl h-[calc(100vh-180px)] animate-fade-up"
    >
      <a
        href="javascript:void(0)"
        class="relative w-full h-full rounded-3xl flex justify-center items-center"
      >
        <img
          src="{{ course.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <div
          class="absolute rounded-3xl inset-0 bg-white/60 bg-opacity-75 transition-opacity hover:bg-transparent hover:opacity-100 flex justify-center items-end px-10"
        >
          <div
            class="flex flex-col gap-3 justify-start items-start bg-lot-dark text-white rounded-md w-full p-10 h-fit mb-10 animate-fade-up animate-delay-900"
          >
            <h2 class="text-4xl font-bold">
              {{ currentModuleIndex + 1 }} - {{ currentModule.name }}
            </h2>
            <div class="w-full border-b pb-2">
              <span class="text-lg font-bold">
                {{ course.name }}
              </span>
            </div>

            <button
              (click)="continue(currentModule)"
              class="button-primary py-3 px-10"
            >
              Continue
            </button>
          </div>
        </div>
      </a>
    </div>
    }
  </div>
</ng-template>

<ng-template #courseEnd>
  <div class="flex flex-col">
    <div class="flex flex-col gap-5">
      <div
        class="flex flex-col items-center justify-center h-[calc(100vh-200px)] bg-gradient-to-r from-lot-ai-dark to-lot-ai text-white rounded-xl p-8"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-24 w-24 mb-6 text-yellow-300"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <h2 class="text-4xl font-bold mb-4">Congratulations!</h2>
        <p class="text-xl mb-8">You've successfully completed the course.</p>

        @if (isLearner) {
        <ng-container *ngTemplateOutlet="reviewFeedback" />
        }
        <div class="mt-12 text-center">
          <p class="mb-4">Ready for your next challenge?</p>
          <button
            class="button-primary-outline py-2 px-4"
            type="button"
            (click)="exit()"
          >
            EXIT
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #reviewFeedback>
  <div class="flex gap-4">
    <button
      (click)="viewSummary()"
      class="button-primary py-3 px-6"
      type="button"
    >
      Download Certificate
    </button>
    <button
      class="button-primary py-3 px-6"
      type="button"
      cdkOverlayOrigin
      #feedbackTrigger="cdkOverlayOrigin"
      (click)="openFeedback = true"
    >
      Share Your feedback
    </button>
  </div>
  <ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="feedbackTrigger"
    [cdkConnectedOverlayOpen]="openFeedback"
    cdkConnectedOverlayHasBackdrop="true"
    (detach)="openFeedback = false"
  >
    <div
      class="flex flex-col bg-white rounded-2xl border-gray-200 border w-[380px] gap-2 p-5"
    >
      <div class="flex justify-between">
        <span class="font-semibold">Reviews and Feedback</span>
        <a
          href="javascript:void(0)"
          class="text-lot-danger"
          (click)="openFeedback = false"
        >
          <i class="fa-regular fa-circle-xmark"></i>
        </a>
      </div>
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      }
      <div class="flex flex-col gap-5 w-full">
        <div class="flex flex-col gap-2">
          <span class="text-lot-dark-gray font-semibold">Your Rating</span>
          <app-ui-start-rating
            size="lg"
            [readOnly]="disableFeedback"
            [rate]="rating() || 0"
            (rating)="rating.set($event)"
          />
        </div>

        <div class="rounded-md border border-lot-gray p-6">
          <textarea
            name="comment"
            id="comment"
            [disabled]="disableFeedback"
            (input)="comments.set($any($event.target).value)"
            class="bg-transparent border-0 w-ful placeholder:text-lot-dark outline-none pl-1"
            placeholder="Write a Review"
            rows="6"
          ></textarea>
        </div>
        @if (!disableFeedback) {
        <button
          type="button"
          (click)="saveFeedback()"
          class="button-primary py-2 w-fit"
        >
          Post your review
        </button>
        }
      </div>
    </div>
  </ng-template>
</ng-template>
