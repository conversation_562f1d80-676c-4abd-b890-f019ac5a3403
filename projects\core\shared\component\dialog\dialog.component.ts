import { NgClass, NgIf } from '@angular/common';
import { Component, Inject, Input, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface DialogConfig {
  type: 'ERROR' | 'WARNING' | 'INFO' | 'SUCCESS' | 'DELETE';
  title: string;
  message: string;
  confirmButton?: string;
  cancelButton?: string;
  cancelClass?: string;
  confirmClass?: string;
  hideModalIcon?: boolean;
  showConfirmArrowIcon?: boolean;
}

export interface ConfigItem {
  type: string;
  title: string;
  message: string;
  confirmButton: string;
  cancelButton: string;
  cancelClass?: string;
  confirmClass?: string;
  hideModalIcon?: boolean;
  showConfirmArrowIcon?: boolean;
}

@Component({
    selector: 'app-lms-dialog',
    imports: [NgClass, NgIf],
    templateUrl: './dialog.component.html',
})
export class DialogComponent {
  dialogConfigs: ConfigItem[] = [
    {
      type: 'SUCCESS',
      title: 'Successful!',
      message: '',
      confirmButton: 'Ok',
      cancelButton: '',
      cancelClass: '',
      confirmClass: '',
      hideModalIcon: false,
      showConfirmArrowIcon: false,
    },
    {
      type: 'WARNING',
      title: 'Successful!',
      message: '',
      confirmButton: 'Ok',
      cancelButton: 'Cancel',
      cancelClass: '',
      confirmClass: '',
      hideModalIcon: false,
      showConfirmArrowIcon: false,
    },
    {
      type: 'DELETE',
      title: 'Successful!',
      message: '',
      confirmButton: 'Remove',
      cancelButton: 'Cancel',
      cancelClass: '',
      confirmClass: '',
      hideModalIcon: false,
      showConfirmArrowIcon: false,
    },
    {
      type: 'INFO',
      title: '',
      message: '',
      confirmButton: 'Ok',
      cancelButton: '',
      cancelClass: '',
      confirmClass: '',
      hideModalIcon: false,
      showConfirmArrowIcon: false,
    },
  ];

  config: ConfigItem;

  constructor(
    public dialogRef: MatDialogRef<DialogComponent>,
    @Inject(MAT_DIALOG_DATA)
    public data: DialogConfig
  ) {
    const param = this.dialogConfigs.find((x) => x.type === data.type);
    if (param) {
      this.config = param;
      this.config.title = data.title;
      this.config.message = data.message;
      if (data.confirmButton) {
        this.config.confirmButton = data.confirmButton;
      }
      if (data.cancelButton) {
        this.config.cancelButton = data.cancelButton;
      }
      if (data.title) {
        this.config.title = data.title;
      }
      if (data.cancelClass) {
        this.config.cancelClass = data.cancelClass;
      }
      if (data.confirmClass) {
        this.config.confirmClass = data.confirmClass;
      }
      if (data.hideModalIcon) {
        this.config.hideModalIcon = data.hideModalIcon;
      }
      if (data.showConfirmArrowIcon) {
        this.config.showConfirmArrowIcon = data.showConfirmArrowIcon;
      }
    }
  }
}
