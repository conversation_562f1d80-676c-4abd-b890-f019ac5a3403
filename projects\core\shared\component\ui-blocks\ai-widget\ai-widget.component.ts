import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  signal,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { LessonWidgetTypes, WidgetType } from '../ui-block.utils';
import { NgTemplateOutlet } from '@angular/common';
import { AIService, getFileFromBase64, getFileFromUrl, MediaBucket, MediaCenterService } from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { getImageTemplate } from '../image.component';

@Component({
  selector: 'app-block-ai',
  imports: [NgTemplateOutlet, MatProgressBarModule],
  templateUrl: './ai-widget.component.html',
})
export class UIBlockAIComponent implements OnInit {
  viewContainerRef = inject(ViewContainerRef);
  aiService = inject(AIService);
  mediaService = inject(MediaCenterService);

  @Input({ required: true }) data: {
    id: string;
    content: Record<string, any>;
  };

  @Output() view = new EventEmitter();

  @ViewChild('widgetHost', { read: ViewContainerRef, static: true })
  widgetHostRef!: ViewContainerRef;

  isLoading = false;
  error?: string;
  textStyles = ['paragraph', 'list', 'card'];
  imageStyles = ['image', 'slides'];

  selectedStyle?: 'paragraph' | 'list' | 'card' | 'image' | 'slides';
  prompt = signal('');
  aiImage?: string;

  ngOnInit(): void {
    this.selectedStyle = this.data.id === 'IMAGE' ? 'image' : 'paragraph';
  }

  async generateAI() {
    let prompt = this.prompt();
    if (this.selectedStyle === 'paragraph' && prompt) {
      this.isLoading = true;
      prompt = `Generate only paragraphs about: ${prompt} and respond in plain html format`;
      const res = await this.aiService.generateText(prompt, 'html');
      this.isLoading = false;
      this.error = res.error;
      const widget = LessonWidgetTypes.find((x) => x.id === WidgetType.TEXT);
      this.viewWidget(widget, {
        description: res.data['paragraph'] || res.data,
        meta: {
          background: 'lot-blue',
          color: 'white',
        },
      });
    }
    if (this.selectedStyle === 'list' && prompt) {
      this.isLoading = true;
      prompt = `Generate a list with only heading and items each item is description in plain html about: ${prompt} and respond in JSON format`;
      const res = await this.aiService.generateText(prompt, 'json');
      this.isLoading = false;
      this.error = res.error;
      const widget = LessonWidgetTypes.find((x) => x.id === WidgetType.LIST);
      this.viewWidget(widget, {
        heading: res.data['heading'],
        // items: res.data['items'].map((x: any) => ({
        //   text:
        //     typeof x === 'string'
        //       ? x
        //       : `${x['title'] ?? x['name'] + ': ' + x['description'] ?? ''}`,
        // })),
        items: res.data['items'].map((x: any) => ({
          text: typeof x === 'string' ? x : x['description'],
        })),
        style: 'disc',
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      });
    }
    if (this.selectedStyle === 'card' && prompt) {
      this.isLoading = true;
      prompt = `Generate only heading in plain text and description in plain html about: ${prompt} and respond in JSON format`;
      const res = await this.aiService.generateText(prompt, 'json');
      this.isLoading = false;
      this.error = res.error;
      const widget = LessonWidgetTypes.find((x) => x.id === WidgetType.TEXT);
      this.viewWidget(widget, {
        heading: res.data['heading'],
        description: res.data['description'],
        meta: {
          width: 100,
          padding: 2,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      });
    }
    if (this.selectedStyle === 'image' && prompt) {
      this.isLoading = true;
      const res = await this.aiService.generateImage(prompt);
      this.isLoading = false;
      this.error = res.error;
      const widget = LessonWidgetTypes.find((x) => x.id === WidgetType.IMAGE);
      this.aiImage = res.data?.at(0);
      const content = getImageTemplate(4);
      if (!this.aiImage) return;
      await this.saveAiImage();
      this.viewWidget(widget, {
        ...content,
        caption: 'Portrait of ' + prompt,
        url: this.aiImage,
        meta: {
          width: 100,
          padding: 2,
          margin: 1,
          background: 'lot-light-gray',
        },
      });
    }
  }

  viewWidget(widget: any, template: any) {
    this.widgetHostRef.clear();
    const componentRef = this.widgetHostRef.createComponent<any>(
      widget.component as any
    );
    componentRef.setInput('content', template);

    componentRef.instance?.view?.subscribe((data: any) => {
      this.view.emit({ data, widget });
    });
    const element = componentRef.location.nativeElement;
    element.className = '';
    element.classList.add(
      'block',
      'p-4',
      'mb-2',
      'transition-all',
      'bg-lot-light-gray',
      'border-lot-gray',
      'border'
    );

    const div = document.createElement('div') as HTMLElement;
    div.className = 'w-full border-b pb-4 text-lot-blue text-lg';
    div.textContent = 'Select result';
    element.prepend(div);
  }

  async saveAiImage() {
    if (!this.aiImage) return;
    const file = await getFileFromBase64(this.aiImage);
    if (!file) return;
    const folder = this.mediaService.state
      .user()
      .organization?.name.replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.error = error;
      return;
    }
    this.aiImage = data?.url ?? this.aiImage;
  }
}
