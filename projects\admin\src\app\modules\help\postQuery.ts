export const GET_All_POST_QUERY = `
query GetAllPostQuery {
    posts {
        id
        date
        content {
            html
            text
        }
        coverImage {
            url
        }
        title
        id
        postCategory {
          id
          name
        }
    }
  	postCategories {
        name
    }
}`;

export const POST_QUERY = `
query GetPost($id: ID) {
  post(where: {id: $id}) {
    content {
      html
    }
    coverImage {
      url
    }
    stage
    tags
    title
    slug
  }
  }`;

export const GET_All_POST_CATEGORIES_QUERY = `
query GetAllPostCategoriesQuery {
  postCategories {
        name
    }
}`;

export const GET_All_POST_BY_CATEGORIES_QUERY = `
query GetAllPostCategoriesQuery {
  postCategories {
    name
    posts {
      created_at
      updated_at
      title
      content {
        html
      }
      author {
        name
      }
      coverImage {
        url
      }
      postCategory {
        name
      }
    }
  }
}`;

export interface BlogPost {
  created_at: string;
  title: string;
  updated_at: string;
  content: {
    html: string;
  };
  author: {
    name: string;
  };
  postCategory: {
    name: string;
  }[];

  coverImage: {
    url: string;
  };
}

export interface BlogCategory {
  name: string;
  posts: Array<BlogPost>;
}
