import { Component, computed, inject, OnInit, signal } from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { Course, CourseCoreService } from '@lms/core';
import { ListComponent } from '../components/list/list.component';
import { FloatingComponent, ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-course-container',
  imports: [
    CommonModule,
    MatPaginatorModule,
    MatTableModule,
    ReactiveFormsModule,
    ListComponent,
    ResourceHeaderComponent,
    FloatingComponent,
  ],
  templateUrl: 'container.component.html',
})
export class CourseContainerComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  service = inject(CourseCoreService);

  source = this.service.coursesSource;

  courses = computed(() => (this.source.value()?.data || []) as Course[]);

  totalCount = computed(() => this.source.value()?.total || 0);

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'Your Courses',
    },
    {
      id: 2,
      name: 'Course Templates',
    },
    {
      id: 3,
      name: 'Course Library',
    },
  ];
  sortBy = [
    { id: 'nameASC', name: 'A to Z' },
    { id: 'nameDESC', name: 'Z to A' },
    { id: 'createdDateASC', name: 'Oldest' },
    { id: 'createdDateDESC', name: 'Latest' },
  ];
  search = signal<string | undefined>(undefined);
  sort = signal<string | undefined>(undefined);

  ngOnInit(): void {
    this.service.filter.set({
      payload: {
        query: '',
        sorting: [],
        type: 'MY',
      },
      paging: {
        page: 1,
        size: 10,
      },
    });
  }

  setTab(tab: number) {
    this.tab = tab;
    if (tab === 1) {
      this.service.filter.set({
        payload: {
          query: '',
          sorting: [],
          type: 'MY',
        },
        paging: {
          page: 1,
          size: 10,
        },
      });
    }
    if (tab === 2) {
      this.service.filter.set({
        payload: {
          query: '',
          sorting: [],
          type: 'TEMPLATE',
        },
        paging: {
          page: 1,
          size: 10,
        },
      });
    }
    if (tab === 3) {
      this.service.filter.set({
        payload: {
          query: '',
          sorting: [],
          type: 'LIBRARY',
        },
        paging: {
          page: 1,
          size: 10,
        },
      });
    }
  }

  onPage(page: number) {}
}
