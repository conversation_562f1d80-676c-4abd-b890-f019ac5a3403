# SSO

Definitions:
- Identity Provider (IdP):  An identity provider is a service that manages user accounts at a company or organization. It can verify the identity of a user and exchange that information with your Supabase project and other applications.
- Service Provider (SP): It is the software that is asking for user information from an identity provider. In our case (i.e. using Supabase), it is the project's Auth server.

## Salesforce as IP

> Note: the followings have to be done for any new SSO request by an Organization.

### Link between a Supabase organization and a Salesforce's

We are using the table **organization_sso** to link any SAML SSO provider with the associated LMS organization in Supabase. The table will allow us to:
- add metadata 
- to know which user to use as **"createdBy"** when an SSO user is created.

### Salesforce Setup
1. Enable IdP in Salesforce: [link to enable IdP feature.](https://learnorteachllc.lightning.force.com/lightning/setup/IdpPage/home) 
2.  Enable Salesforce as IdP: [Click on New Connected App](https://learnorteachllc.lightning.force.com/lightning/setup/NavigationMenus/home)
3. <PERSON>ose Create a Connected App
4. Fill out the Basic information about the App: Name, Contact, etc.
5. In the section **Web App Settings** 
  - Provide your Destination URL: **Must be the LMS admin url**
  - Enable SAML
  - Then Fill out with the following data (*e.g.* for lms-verkada : < project > = zdoqrbknzmqmbxnhcetw) :
    - EntityID:	*https://< project >.supabase.co/auth/v1/sso/saml/metadata*
    - Metadata URL:	*https://< project >.supabase.co/auth/v1/sso/saml/metadata*
    - Assertion Consumer Service (ACS) URL:	*https://< project >.supabase.co/auth/v1/sso/saml/acs*
    - Single Logout (SLO) URL:	*https://< project >.supabase.co/auth/v1/sso/slo*
    - NameID:	**emailAddress**
    - Signing algorithm: SHA1
    - Subject Type: User ID
  - Save and head back to Connected App with the **Manage** view.
6. In the manage view of the Connected App, head down in the **Profiles** section
  - Select the profile of your Salesforce users that will have access to this Connected App
7. In the manage view of the Connected App, head down in the **Custom Attributes** section
  - Add the following attributes
    - Key=FirstName, Value=$User.FirstName
    - Key=LastName, Value=$User.LastName
    - Key=UserName, Value=$User.UserName
    - Key=OrganizationID, Value=$Organization.Id
    - Key=Role, Value="learner"
  -  The *"Role"* will be given to any user that uses this Connected App
    - Roles can be "learner", "manager" or "admin"

###  Add IdP to Supabase
```sh
supabase sso add --type saml --project-ref ebongdaiwwvolarwnhvk \
  --metadata-url '<url>' \
  --domains <domain>
# e.g. of metadata-url: https://learnorteachllc.my.salesforce.com/.well-known/samlidp/Supadupabase.xml
# e.g. of domain: salesforce.com
```

### Custom claims
After creating a providerr in Supabase, we need to tell Supabase how to parse the Custom Attributes set in Salesforce.

```json
{
  "keys": {
    "email": {
      "name": "mail"
    },
    "first_name": {
      "name": "FirstName"
    },
    "last_name": {
      "name": "LastName"
    },
    "username": {
      "name": "UserName"
    },
    "role": {
      "name": "Role"
    }
  }
}
```

```sh
# Update the attribute mapping
npx supabase sso update 0165df1f-422b-4780-a9d2-042e645b1868 --project-ref zdoqrbknzmqmbxnhcetw --attribute-mapping-file /Users/<USER>/Documents/LMS/lms-app/saml/salesforce_attribute_mapping.json
```

###  Initiating the SSO from LMS

```js
// Usage in JavaScript
supabase.auth.signInWithSSO({
  domain: 'salesforce.com', // the domain provided when creating the SSO in the Supabase project.
})
// OR
supabase.auth.signInWithSSO({
  providerId: '<ID>', // the Supabase-generated ID of the Provider.
})
```
