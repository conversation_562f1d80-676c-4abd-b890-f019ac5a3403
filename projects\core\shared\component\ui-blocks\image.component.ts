import { Component, EventEmitter, Input, Output } from '@angular/core';
import { HtmlWrapperComponent } from '@lms/shared';

@Component({
  selector: 'app-block-image',
  imports: [HtmlWrapperComponent],
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg {{
        metaClass
      }} transform duration-500 hover:translate-x-2 hover:-translate-y-2 pointer-events-none"
      [style]="styleForm"
      (click)="selectBlock()"
    >
      @if (content.position === 'center') {
      <div class="flex flex-col items-center gap-4">
        <img
          [src]="content.url"
          [alt]="content.caption"
          class="max-w-2xl rounded-lg shadow-md w-full object-cover transform duration-500 hover:-translate-x-2 hover:translate-y-2 pointer-events-auto"
        />
        @if (content.textPosition === 'center') {
        <div class="text-center max-w-2xl">
          @if (content.caption) {
          <h3 class="text-xl font-semibold mb-2">{{ content.caption }}</h3>
          } @if (content.description) {

          <app-ui-html-wrapper
            class="text-lg break-words"
            [content]="content.description"
          />
          }
        </div>
        }
      </div>
      } @if (content.position === 'full') {
      <div class="relative flex flex-col gap-4 w-full">
        <img
          [src]="content.url"
          [alt]="content.caption"
          class="rounded-lg shadow-md w-full object-cover transform duration-500 hover:-translate-x-2 hover:translate-y-2 pointer-events-auto"
        />

        @if (content.textPosition === 'left') {
        <div class="w-full">
          @if (content.caption) {
          <h3 class="text-xl font-semibold mb-2">{{ content.caption }}</h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lg break-words"
            [content]="content.description"
          />
          }
        </div>
        } @if (content.textPosition === 'center') {
        <div
          class="w-full flex flex-col justify-center items-center text-center"
        >
          @if (content.caption) {
          <h3 class="text-xl font-semibold mb-2">{{ content.caption }}</h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lg break-words px-5"
            [content]="content.description"
          />
          }
        </div>
        } @if (content.textPosition === 'overlay') {

        <div
          class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 rounded-b-lg"
        >
          @if (content.caption) {
          <h3 class="text-xl font-semibold mb-2 break-words">
            {{ content.caption }}
          </h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lot-light-gray break-words"
            [content]="content.description"
          />
          }
        </div>

        }
      </div>

      <!-- @if (content.textPosition === 'bottom') {
      <div class="flex flex-col gap-4 w-full">
        <img
          [src]="content.url"
          [alt]="content.caption"
          class="rounded-lg shadow-md w-full object-cover transform duration-500 hover:-translate-x-2 hover:translate-y-2 pointer-events-auto"
        />
        <div class="w-full">
          @if (content.caption) {
          <h3 class="text-lg font-semibold mb-2">{{ content.caption }}</h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lg break-words"
            [content]="content.description"
          />
          }
        </div>
      </div>
      } @if (content.textPosition === 'overlay') {
      <div class="relative">
        <img
          [src]="content.url"
          [alt]="content.caption"
          class="w-full rounded-lg shadow-md object-cover transform duration-500 hover:-translate-x-2 hover:translate-y-2 pointer-events-auto"
        />
        <div
          class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 rounded-b-lg"
        >
          @if (content.caption) {
          <h3 class="text-lg font-semibold mb-2 break-words">
            {{ content.caption }}
          </h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lot-light-gray break-words"
            [content]="content.description"
          />
          }
        </div>
      </div>
      } @if (content.textPosition === 'center') {
      <div class="flex flex-col gap-8 ">
        <img
          [src]="content.url"
          [alt]="content.caption"
          class="w-1/2 rounded-lg shadow-md object-cover transform duration-500 hover:-translate-x-2 hover:translate-y-2 pointer-events-auto"
        />
        <div class="flex-1 justify-center items-center">
          @if (content.caption) {
          <h3 class="text-lg text-center font-semibold mb-2">
            {{ content.caption }}
          </h3>
          } @if (content.description) {
          <app-ui-html-wrapper
            class="text-lot-dark-gray break-words"
            [content]="content.description"
          />
          }
        </div>
      </div>
      }  -->

      }
    </div>
  `,
})
export class UIBlockImageComponent {
  @Input() content: {
    readyForLecture: boolean;
    url: string;
    caption?: string;
    description?: string;
    position: 'center' | 'full';
    textPosition: 'left' | 'center' | 'overlay';
    requirePass: boolean;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  @Output() view = new EventEmitter();

  get styleForm() {
    const extraStyle = this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
    return extraStyle;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    const position =
      (this.content?.meta?.width || 0) < 100
        ? ' mx-auto justify-center items-center '
        : '';
    return Object.values(meta).join(' ') + position;
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit(this.content);
  }
}

export const getImageTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Image with Bottom Text',
        url: 'https://picsum.photos/800/450',
        caption: ' Image with Bottom Text',
        description:
          'Your Paid Time Off (PTO) is designed to help you maintain a healthy work-life balance. Use this time to rest, recharge, and attend to personal matters, so you can return to work refreshed and ready to contribute your best.',
        position: 'full',
        textPosition: 'center',
        meta: {
          width: 100,
          padding: 26,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      };

    default:
      return {
        uiLabel: 'Full Width Image with Minimal Styling',
        url: 'https://picsum.photos/800/450',
        position: 'full',
        textPosition: 'overlay',
        caption: 'Overlaid  Caption',
        description: `Taking time off isn’t just a benefit — it’s essential. At Learn Or Teach, we encourage you to step away when needed, so you can bring your best self to every day`,
        meta: {
          width: 100,
          padding: 0,
          background: 'lot-dark',
          color: 'white',
        },
      };
  }
};
