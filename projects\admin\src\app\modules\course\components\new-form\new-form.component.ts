import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Router } from '@angular/router';
import { CourseCoreService, stripHtml, ToastMessageType } from '@lms/core';
import { markControlsDirty } from '@lms/core';
import { RichTextComponent } from '@lms/shared';

@Component({
  selector: 'app-new-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    RichTextComponent,
  ],
  templateUrl: 'new-form.component.html',
})
export class CourseNewFormComponent implements OnInit {
  readonly service = inject(CourseCoreService);
  readonly router = inject(Router);
  public dialogRef: MatDialogRef<CourseNewFormComponent> = inject(MatDialogRef);

  isLoading = false;
  error?: string;

  editorStyle = {
    height: '250px',
    backgroundColor: 'transparent',
    border: 'none',
  };

  form = new FormGroup({
    title: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl('', Validators.required),
  });

  get f() {
    return this.form.controls;
  }

  ngOnInit(): void {}

  getContent(text: string) {
    this.form.patchValue({
      description: text,
    });
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const payload = {
      name: this.form.value.title,
      description: this.form.value.description,
      cover: 'assets/images/new/card-3.jpeg',
      short: stripHtml(this.form.value.description!)?.substring(0, 250),
    } as any;

    const res = await this.service.add(payload);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Course created successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.router.navigate(['/lms/courses/view', res.data]);
    this.dialogRef.close();
  }
}
