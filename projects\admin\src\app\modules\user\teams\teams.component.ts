import { Component, inject, OnInit } from '@angular/core';
import { TeamListComponent } from './list/list.component';
import {
  GroupItem,
  Team,
  UserItem,
  TeamCoreService,
  UsersCoreService,
  ToastMessageType,
} from '@lms/core';
import { UserListComponent } from '../users/list/list.component';
import { DialogComponent, ResourceHeaderComponent } from '@lms/shared';
import { MatDialog } from '@angular/material/dialog';
import { TeamFormComponent } from './form/team-form.component';
import { firstValueFrom, map } from 'rxjs';
import { UserFormComponent } from '../users/form/user-form.component';

export type TeamAction = 'edit' | 'delete' | 'view' | 'users';

@Component({
  selector: 'app-teams',
  imports: [TeamListComponent, UserListComponent, ResourceHeaderComponent],
  templateUrl: './teams.component.html',
})
export class TeamsComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  service = inject(TeamCoreService);
  userService = inject(UsersCoreService);

  tab = 1;
  team?: Team;
  group?: GroupItem;

  type: TeamAction;

  source = this.service.teamSource;
  isLoading = false;
  error?: string;

  groups: (Team & any)[] = [];
  users: UserItem[] = [];

  ngOnInit(): void {
    this.service.filter.set({
      teamQuery: '1',
    });
  }

  async action({ type, data }: { type: TeamAction & any; data: Team & any }) {
    this.groups = (data.groups || []) as GroupItem[];
    this.users = [];

    if (type === 'users') {
      this.type = type;
      this.team = data;
      this.service.viewTeam.set(data);
      this.users = (data.users || []) as UserItem[];
      return;
    }

    if (type === 'delete') {
      await this.executeDelete(data, 'TEAM');
    }

    if (type === 'view') {
      this.service.viewType.set('GROUP');
      this.type = type;
      this.team = data;
      this.service.viewTeam.set(data);
    }

    if (type === 'edit') {
      this.dialog
        .open(TeamFormComponent, {
          width: '800px',
          minWidth: '800px',
          data: {
            type: 'TEAM',
            item: data,
            isTeam: true,
          },
        })
        .afterClosed()
        .subscribe(() => {
          this.service.teamSource.reload();
          this.userService.userSource.reload();
        });
    }
  }

  async actionGroup(
    { type, data }: { type: TeamAction & any; data: any },
    view: 'GROUP' | 'USER'
  ) {
    this.type = type;
    this.group = { ...data, team: this.team };
    this.users = (data.users || []) as UserItem[];

    if (type === 'delete' && view === 'GROUP') {
      await this.executeDelete(data, 'GROUP');
    }

    if (type === 'edit' && view === 'GROUP') {
      this.dialog
        .open(TeamFormComponent, {
          width: '400px',
          minWidth: '400px',
          data: {
            type: 'GROUP',
            item: this.team,
            group: data,
            isTeam: false,
          },
        })
        .afterClosed()
        .subscribe(() => {
          this.service.teamSource.reload();
          this.userService.userSource.reload();
        });
    }

    if (type === 'delete' && view === 'USER') {
      // await this.executeDelete(data, 'USER');
    }
    if (type === 'edit' && view === 'USER') {
      const res = await this.userService.getUsers({
        payload: { query: data.email },
        paging: {
          page: 1,
          size: 10,
        },
      });
      this.dialog
        .open(UserFormComponent, {
          minWidth: '800px',
          data: {
            type: 'EDIT',
            item: res.data[0] ?? data,
          },
        })
        .afterClosed()
        .subscribe(() => {
          this.service.teamSource.reload();
          this.userService.userSource.reload();
        });
    }
  }

  private async executeDelete(
    data: { id: string; name: string },
    type: 'TEAM' | 'GROUP'
  ) {
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${data.name} ${
              type === 'TEAM' ? 'Team' : 'Group'
            }. All users associated to it will be unassigned`,
            title: `Are you sure to remove this ${
              type === 'TEAM' ? 'Team' : 'Group'
            }?`,
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.deleteByType(data.id, type);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
        this.service.state.openToast({
          title: 'Delete Request Failed',
          message: 'Failed: ' + result.error,
          type: ToastMessageType.ERROR,
        });
      }
      if (result.data) {
        this.service.state.openToast({
          title: 'Delete Request Successful',
          message: `${type === 'TEAM' ? 'Team' : 'Group'} deleted successfully`,
          type: ToastMessageType.SUCCESS,
        });
        this.service.teamSource.reload();
        this.userService.userSource.reload();
      }
    }
  }

  goBack() {
    if (this.service.viewType() === 'GROUP') {
      this.group = undefined;
    }
    this.team = undefined;
    this.service.viewType.set('TEAM');
    this.service.viewTeam.set(null);
  }

  openUserAssigner() {
    this.dialog
      .open(TeamFormComponent, {
        width: '800px',
        minWidth: '800px',
        data: {
          type: 'ASSIGN_TEAM_GROUP',
          item: this.team,
          group: this.group,
          isTeam: !this.group,
        },
      })
      .afterClosed()
      .subscribe(() => {
        this.service.teamSource.reload();
        this.userService.userSource.reload();
      });
  }
}
