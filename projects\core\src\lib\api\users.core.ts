import { filter } from 'rxjs/operators';
import { inject, Injectable, resource, signal } from '@angular/core';
import {
  AccountStatus,
  EdgeError,
  getId,
  getPagination,
  GlobalStateService,
  ITeamGroupItem,
  Organization,
  TeamCoreService,
  tryPromise,
  UserItem,
  UserTeamGroupItem,
} from '@lms/core';

type UserFilter = {
  payload: {
    query?: string;
    role?: AccountStatus;
    sorting?: string[];
    showPending?: boolean;
  };
  paging: {
    page: number;
    size: number;
  };
};

type UserSubFilter = {
  type: 'active30' | 'activePass' | 'activeNone';
  paging: {
    page: number;
    size: number;
  };
};

@Injectable({
  providedIn: 'root',
})
export class UsersCoreService {
  state = inject(GlobalStateService);
  teamsService = inject(TeamCoreService);
  filter = signal<UserFilter | undefined>(undefined);
  filterSub = signal<UserSubFilter | undefined>(undefined);
  totalCount = signal<number>(0);

  get user(): UserItem {
    return this.state.user();
  }
  get organization(): Organization {
    return this.state.user().organization as Organization;
  }

  userSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request) {
        return [];
      }
      const { data } = request.payload.showPending
        ? await this.getPendings(request.paging.page, request.paging.size)
        : await this.getUsers(request);
      return data;
    },
  });

  subscribeUsersSource = resource({
    request: () => this.filterSub(),
    loader: async ({ request }) => {
      if (!request) {
        return {
          users: [],
          total: 0,
        };
      }
      const { data } = await this.getUserSubscribers(request);
      return data;
    },
  });

  async add(users: UserItem[]) {
    const { data, error } = await this.state.supabase.functions.invoke<{
      data: UserItem[];
      error: EdgeError[];
    }>('add-user', {
      body: JSON.stringify({ users }),
    });

    return {
      data: (data?.data || []).filter(Boolean) as UserItem[],
      error: (data?.error
        ?.filter(Boolean)
        ?.map((e) => e.message)
        ?.join(', ') ?? error?.message) as string | undefined,
    };
  }

  async update(users: UserItem[]) {
    const { data, error } = await this.state.supabase.functions.invoke<{
      data: UserItem[];
      error: EdgeError[];
    }>('update-user', {
      body: JSON.stringify({ users }),
    });

    return {
      data: (data?.data || []).filter(Boolean) as UserItem[],
      error: (data?.error
        ?.filter(Boolean)
        ?.map((e) => e.message)
        ?.join(', ') ?? error?.message) as string | undefined,
    };
  }

  async delete(userId: string) {
    const { data, error } = await this.state.supabase.functions.invoke<{
      data: UserItem[];
      error: any;
    }>('delete-user', {
      body: JSON.stringify({ user_id: userId }),
    });

    return {
      data: data?.data
        ? Object.keys(data?.data).length
          ? [data?.data]
          : []
        : ((data?.data || []).filter(Boolean) as UserItem[]),
      error: data?.error?.message,
    };
  }

  async deleteTeamGroup(items: ITeamGroupItem[]) {
    return await this.teamsService.assignUsers({
      news: [],
      olds: items.map((item) => item.userGroupId),
    });
  }

  async reassignUser(payload: UserTeamGroupItem[]) {
    const userId = payload[0].user;
    const organizationId = payload[0].organization;

    const { data: existingItems } =
      await this.teamsService.getUserAssignedGroups(userId, organizationId);

    const payloadSet = new Set(payload.map((item) => item.id));
    const existingSet = new Set(existingItems.map((item) => item.id));

    // Find new items (items in payload but not in existingItems)
    const newItems = payload.filter((item) => !existingSet.has(item.id));

    // Find delete items (items in existingItems but not in payload)
    const deleteItems = existingItems.filter(
      (item) => !payloadSet.has(item.id)
    );

    const errors = await this.teamsService.assignUsers({
      news: newItems,
      olds: deleteItems.map((item) => item.id),
    });

    return errors;
  }

  async getUsers(filter: UserFilter) {
    const res = await this.state.supabase.rpc('get_users', {
      filter: {
        organization: getId(this.organization),
        role: filter.payload.role,
        query: filter.payload.query,
        page: filter.paging.page,
        size: filter.paging.size ?? 10,
      },
    });

    const _data = ((res.data['users'] || [])['array_agg'] || []).map(
      (u: any) => u.user
    );

    const users = _data.map((u: any) => {
      const { team_groups, ...user } = u;
      return user;
    }) as UserItem[];
    const teamGroups = _data
      .map((u: any) => {
        const { team_groups, ...user } = u;
        return team_groups;
      })
      .flat() as ITeamGroupItem[];

    this.totalCount.set((res.data['total'] || 0) as number);

    const items = users.map((user) => ({
      ...user,
      username:
        !user.lastname && !user.firstname
          ? undefined
          : `${user.firstname}, ${user.lastname}`,
      teams: teamGroups
        .filter((t) => t.userId === user.id && !!t.teamId)
        .filter((v, i, _) => _.findIndex((x) => x.teamId === v.teamId) === i),
      groups: teamGroups
        .filter((t) => t.userId === user.id && !!t.groupId)
        .filter((v, i, _) => _.findIndex((x) => x.groupId === v.groupId) === i),
      self: user.id === this.state.user().id,
    })) as UserItem[];

    return {
      data: items,
      error: res?.error?.message,
    };
  }

  async getUserSubscribers(filter: UserSubFilter) {
    const res = await this.state.supabase.rpc('get_subscription_users', {
      filter: {
        organization: getId(this.organization),
        activity: filter.type,
        page: filter.paging.page - 1,
        size: filter.paging.size ?? 10,
      },
    });

    const users = ((res.data['users'] || [])['json_agg'] || []) as UserItem[];
    const teamGroups = (res.data['team_groups']['array_agg'] ||
      []) as ITeamGroupItem[];

    return {
      data: {
        users: users.map((user) => ({
          ...user,
          name:
            !user.lastname && !user.firstname
              ? 'uncompleted'
              : `${user.firstname}, ${user.lastname}`,
          teams: teamGroups.filter((t) => t.userId === user.id && !!t.teamId),
          groups: teamGroups.filter((t) => t.userId === user.id && !!t.groupId),
          self: user.id === this.state.user().id,
        })) as UserItem[],
        total: (res.data['total'] || 0) as number,
      },
      error: res?.error?.message,
    };
  }

  async getUserOnlyByQuery(query: string, roles: AccountStatus[] = []) {
    const { from, to } = getPagination(0, 15);
    const req = this.state.supabase
      .from('users')
      .select('id, firstname, lastname, email, username', { count: 'exact' })
      .eq('organization', getId(this.organization));

    if (roles.length) {
      req.in('role', roles);
    }
    if (query?.trim()?.length) {
      req.or(
        `firstname.ilike.%${query}%,lastname.ilike.%${query}%,email.ilike.%${query}%`
      );
    }
    req
      .order('firstname', { ascending: true })
      .order('lastname', { ascending: true })
      .order('email', { ascending: true })
      .range(from, to);

    const res = await req;

    return {
      data: ((res.data || []) as UserItem[]).map((x) => ({
        ...x,
        name: x.username ?? `${x.firstname}, ${x.lastname}`,
      })),
      count: res.count ?? 0,
      error: res?.error?.message,
    };
  }

  async getOne(id: string): Promise<{ userInfo?: UserItem; error?: string }> {
    const res = await this.state.supabase
      .from('users')
      .select('*, organization(*)')
      .eq('id', id)
      .limit(1)
      .single();
    if (res.data) {
      // this.state.user.set(<UserItem>res.data);
      return { userInfo: <UserItem>res.data };
    }
    return {
      error: res.error?.message,
    };
  }

  async getPendings(
    page = 0,
    size = 10
  ): Promise<{ data: UserItem[]; error?: string }> {
    const { from, to } = getPagination(page - 1, size);
    const res = await this.state.supabase
      .from('users')
      .select('*')
      .eq('organization', getId(this.organization))
      .is('lastname', null)
      .range(from, to);
    this.totalCount.set((res.count || 0) as number);

    return {
      data: (res.data || []).filter(Boolean) as UserItem[],
      error: res.error?.message,
    };
  }
}
