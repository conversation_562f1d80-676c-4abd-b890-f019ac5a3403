<div class="flex flex-col gap-5 w-full">
  <h3>AI {{ data.id === 'TEXT' ? 'Content' : 'Image' }} Generator</h3>
  <div class="flex flex-col gap-2 w-full">
    @if (data.id === 'TEXT') {
      <span class="text-lot-blue">Select widget type:</span>
    <ng-container *ngTemplateOutlet="textTemplate" />
    } @if (selectedStyle) {
    <div class="w-full flex flex-col gap-2">
      @if (data.id === 'TEXT') {
      <span class="text-lot-blue">Will generate {{ selectedStyle }}</span>
      }
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      } @if (error) {
      <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
      }
      <div class="rounded-lg border border-lot-gray flex gap-2 px-5 py-2.5">
        <input
          type="search"
          name="prompt"
          id="prompt"
          (input)="prompt.set($any($event.target).value)"
          class="bg-transparent border-0 w-full text-lot-dark placeholder:text-lot-dark outline-none pl-1"
          placeholder="Enter your prompt here..."
        />
        <a
          href="javascript:void(0)"
          (click)="generateAI()"
          class="bg-lot-ai-dark text-white p-2 rounded-md"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="size-6 -rotate-45"
          >
            <path
              d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"
            />
          </svg>
        </a>
      </div>
    </div>
    }
  </div>
  <div class="flex flex-col">
    <div #widgetHost class="w-full cursor-pointer"></div>
  </div>
</div>

<ng-template #textTemplate>
  <div class="flex justify-evenly gap-2 w-full">
    @for (textStyle of textStyles; track $index) { @switch (textStyle) { @case
    ('paragraph') {
    <a
      href="javascript:void(0)"
      (click)="selectedStyle = textStyle"
      class="flex flex-col w-full"
    >
      <!-- <div class="flex-grow">
        <div class="w-full max-w-2xl flex flex-col gap-2">
          <app-skeleton width="93%" height="1rem" />
          <app-skeleton width="63%" height="1rem" />
          <app-skeleton width="83%" height="1rem" />
        </div>
      </div> -->
      <span class="material-symbols-outlined text-4xl"> subject </span>
      <span class="text-lot-dark">Paragraph</span>
    </a>
    } @case ('list') {
    <a
      href="javascript:void(0)"
      (click)="selectedStyle = textStyle"
      class="flex flex-col w-full"
    >
      <!-- <div class="flex-grow">
        <div class="w-full max-w-2xl flex flex-col gap-2">
          <app-skeleton width="85%" height="1rem" />
          <div class="space-y-3">
            <div class="flex items-center gap-3">
              <app-skeleton width="1rem" height="1rem" shape="circle" />
              <app-skeleton width="66%" height="1rem" />
            </div>
            <div class="flex items-center gap-3">
              <app-skeleton width="1rem" height="1rem" shape="circle" />
              <app-skeleton width="50%" height="1rem" />
            </div>
            <div class="flex items-center gap-3">
              <app-skeleton width="1rem" height="1rem" shape="circle" />
              <app-skeleton width="75%" height="1rem" />
            </div>
          </div>
        </div>
      </div> -->
      <span class="material-symbols-outlined text-4xl">
        format_list_bulleted
      </span>
      <span class="text-lot-dark">List</span>
    </a>
    } @case ('card') {
    <a
      href="javascript:void(0)"
      (click)="selectedStyle = textStyle"
      class="flex flex-col w-full"
    >
      <!-- <div class="flex-grow">
        <div class="w-full max-w-2xl flex flex-col gap-2">
          <app-skeleton width="100%" height="1rem" />
          <div class="flex flex-col gap-2 pl-3">
            <app-skeleton width="75%" height="1rem" />
            <app-skeleton width="75%" height="1rem" />
          </div>
        </div>
      </div> -->
      <span class="material-symbols-outlined text-4xl"> text_snippet </span>
      <span class="text-lot-dark">Card</span>
    </a>
    } } }
  </div>
</ng-template>

<ng-template #imageTemplate>
  <div class="flex gap-2">
    @for (imageStyle of imageStyles; track $index) { @switch (imageStyle) {
    @case ('image') {
    <a
      href="javascript:void(0)"
      (click)="selectedStyle = imageStyle"
      class="flex flex-col w-full"
    >
      <span class="material-symbols-outlined text-4xl"> imagesmode </span>
      <span class="text-lot-dark">1 image</span>
    </a>
    } @case ('slides') {
    <a
      href="javascript:void(0)"
      (click)="selectedStyle = imageStyle"
      class="flex flex-col w-full"
    >
      <span class="material-symbols-outlined text-4xl"> photo_library </span>
      <span class="text-lot-dark">Slides</span>
    </a>
    } } }
  </div>
</ng-template>
