import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { Router } from '@angular/router';
import { CourseCoreService, LearningInstructorService, stripHtml, ToastMessageType } from '@lms/core';
import { markControlsDirty } from '@lms/core';
import { RichTextComponent } from '@lms/shared';

@Component({
  selector: 'app-instructor-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    RichTextComponent,
  ],
  templateUrl: 'form.component.html',
})
export class InstructorFormComponent {
  readonly service = inject(LearningInstructorService);
  readonly router = inject(Router);
  public dialogRef: MatDialogRef<InstructorFormComponent> =
    inject(MatDialogRef);

  isLoading = false;
  error?: string;

  form = new FormGroup({
    title: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl('', Validators.required),
    type: new FormControl('1', Validators.required),
  });

  get type() {
    return +(this.form.value.type || '1') === 1
      ? 'Learning Path'
      : 'Instructor Led';
  }

  get f() {
    return this.form.controls;
  }

  getContent(text: string) {
    this.form.patchValue({
      description: text,
    });
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const payload = {
      name: this.form.value.title,
      description: this.form.value.description,
      cover: 'assets/images/new/card-3.jpeg',
      short: stripHtml(this.form.value.description!)?.substring(0, 250),
      items: [],
      prerequisites: [],
      sessions: [],
      resources: [],
      type:
        +(this.form.value.type || '1') === 1 ? 'LEARNINGPATH' : 'INSTRUCTORLED',
    } as any;

    const res = await this.service.add(payload);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: this.type + ' created successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.router.navigate(['/lms/advanced/view', res.data]);
    this.dialogRef.close();
  }
}
