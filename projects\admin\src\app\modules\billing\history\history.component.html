<table class="w-full divide-y divide-gray-300">
  <thead>
    <tr>
      <th
        scope="col"
        class="py-3.5 pr-3 pl-4 text-left text-sm font-normal italic text-lot-dark-gray sm:pl-3 w-1/5"
      >
        Date
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray w-1/5"
      >
        Plan
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray w-1/5"
      >
        Amount
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray w-1/5"
      >
        Status
      </th>
      <th scope="col" class="relative py-3.5 pr-4 pl-3 sm:pr-3 w-2/5"></th>
    </tr>
  </thead>
  <tbody class="bg-white">
    @for (item of data; track $index) {
    <tr class="even:bg-gray-50 group border-t">
      <td
        class="py-4 pr-3 pl-4 text-base font-medium whitespace-nowrap text-lot-dark sm:pl-3"
      >
        {{ item.created_at | date : "EEE, MMM d, y, h:mm a" }}
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.plan.name }}
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.purchaseAmount | currency }}
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap text-lot-dark">
        {{ item.status }}
      </td>
      <td
        class="py-4 pr-4 pl-3 text-sm sm:pr-3 flex items-center justify-end gap-8"
      >
        <a
          href="javascript:void(0)"
          class="text-lot-dark-gray hover:text-lot-dark text-xl"
        >
          <i class="fa-solid fa-receipt"></i>
        </a>
      </td>
    </tr>
    }
  </tbody>
</table>
<mat-paginator [length]="data.length" class="border-t" />
