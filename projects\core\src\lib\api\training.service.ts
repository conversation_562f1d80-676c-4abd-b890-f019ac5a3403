import { Injectable, inject, resource, signal } from '@angular/core';
import { GlobalStateService, ScormCloundService } from '../services';
import {
  Course,
  Module,
  UserTrackingItem,
  LearningType,
  TraceStatus,
  Lesson,
  LearningInstructor,
  TrackItem,
} from '../models';
import { getId, mapCollection } from '../utils';
import { CourseLearnerPlayer } from '../queries';
import { InstructorLearnerPlayer } from '../queries/instructor.query';

@Injectable({
  providedIn: 'root',
})
export class TrainingService {
  state = inject(GlobalStateService);
  scormService = inject(ScormCloundService);

  get user() {
    return this.state.user();
  }

  get organization() {
    return this.state.envConfig.env.organization;
  }

  courseId = signal('');
  ledId = signal('');

  course = resource<{ course: Course; tracking?: UserTrackingItem }, string>({
    request: () => this.courseId(),
    loader: async ({ request }) => {
      const res = await this.queryCourseForLearner(request);
      if (!res) {
        throw new Error('Instructor Led not found');
      }
      return {
        course: res.course,
        tracking: res,
      };
    },
  });

  instructor = resource<
    { instructor: LearningInstructor; tracking?: UserTrackingItem },
    string
  >({
    request: () => this.ledId(),
    loader: async ({ request }) => {
      const res = await this.queryInstructorOne(request);
      if (!res) {
        throw new Error('Instructor Led not found');
      }
      return {
        instructor: res.instructor!,
        tracking: res,
      };
    },
  });

  async queryCourseForLearner(id: string) {
    if (!id) {
      return;
    }
    const { data } = await this.state.graphql.rawRequest(CourseLearnerPlayer, {
      id,
      userId: getId(this.user),
      teams: this.state.teamGroupIds.teams,
      groups: this.state.teamGroupIds.groups,
    });
    if (!data) {
      return;
    }

    const info = mapCollection(data, 'course_enrollmentsCollection')[0];

    const tracking = mapCollection(info, 'user_trackingsCollection').map(
      (x) => ({
        ...x,
        trackings:
          typeof x['trackings'] === 'string' ? JSON.parse(x['trackings']) : [],
        lastTrace:
          typeof x['lastTrace'] === 'string'
            ? JSON.parse(x['lastTrace'])
            : undefined,
        feedback:
          typeof x['feedback'] === 'string'
            ? JSON.parse(x['feedback'])
            : undefined,
      })
    )[0] as UserTrackingItem;

    const item = {
      ...info['courses'],
      creator:
        typeof info['courses']['creator'] === 'string'
          ? JSON.parse(info['courses']['creator'])
          : info['courses']['creator'],
      modules: (
        mapCollection(info['courses'], 'modulesCollection') as Module[]
      ).map(
        (w) =>
          ({
            ...w,
            lessons: (mapCollection(w, 'lessonsCollection') as Lesson[]).map(
              (z) => ({
                ...z,
                contents:
                  typeof z.contents === 'string'
                    ? JSON.parse(z.contents)
                    : z.contents,
              })
            ),
            module: w.id,
          } as Module)
      ),
    } as Course;
    
    return {
      ...tracking,
      course: item,
    } as UserTrackingItem;
  }

  async queryInstructorOne(id: string) {
    if (!id) {
      return;
    }
    const { data } = await this.state.graphql.rawRequest(
      InstructorLearnerPlayer,
      {
        id,
        userId: getId(this.user),
        teams: this.state.teamGroupIds.teams,
        groups: this.state.teamGroupIds.groups,
      }
    );
    if (!data) {
      return;
    }

    const info = mapCollection(data, 'instructor_enrollmentsCollection')[0];
    const tracking = mapCollection(info, 'user_trackings_comboCollection').map(
      (x) => ({
        ...x,
        trackings:
          typeof x['trackings'] === 'string' ? JSON.parse(x['trackings']) : [],
        lastTrace:
          typeof x['lastTrace'] === 'string'
            ? JSON.parse(x['lastTrace'])
            : undefined,
        feedback:
          typeof x['feedback'] === 'string'
            ? JSON.parse(x['feedback'])
            : undefined,
        sessions:
          typeof x['sessions'] === 'string'
            ? JSON.parse(x['sessions'])
            : undefined,
      })
    )[0] as UserTrackingItem;

    const item = {
      ...info['learning_instructors'],
      creator:
        typeof info['learning_instructors']['creator'] === 'string'
          ? JSON.parse(info['learning_instructors']['creator'])
          : info['learning_instructors']['creator'],

      items:
        typeof info['learning_instructors']['items'] === 'string'
          ? JSON.parse(info['learning_instructors']['items'])
          : undefined,
      sessions:
        typeof info['learning_instructors']['sessions'] === 'string'
          ? JSON.parse(info['learning_instructors']['sessions'])
          : undefined,
      resources:
        typeof info['learning_instructors']['resources'] === 'string'
          ? JSON.parse(info['learning_instructors']['resources'])
          : undefined,
      prerequisites:
        typeof info['learning_instructors']['prerequisites'] === 'string'
          ? JSON.parse(info['learning_instructors']['prerequisites'])
          : undefined,
    } as LearningInstructor;

    return {
      ...tracking,
      instructor: item,
    } as UserTrackingItem;
  }

  checkIfEnrolledTracking(id: string, type: LearningType) {
    return this.state.supabase
      .from('user_trackings')
      .select('*')
      .eq('user', getId(this.user))
      .eq('enrollment', id);
  }

  async addTracking(course: Course, enrollment: UserTrackingItem) {
    const payload = {
      enrollment: enrollment.enrollment,
      user: getId(this.user),
      progress: 1,
      status: TraceStatus.IN_PROGRESS,
      scormLaunchLink: enrollment.scormLaunchLink,
      scormRegistrationId: enrollment.scormRegistrationId,
      type: LearningType.COURSE,
      dueDate: enrollment.dueDate,
      rating: 0,
      trackings: [],
      lastTrace: null,
      created_by: getId(this.user),
    };

    if (course.scormCourseId && !enrollment.scormRegistrationId) {
      const res = await this.scormService.subscribeLearner(course);
      if (res?.url === 'READY') {
        payload.scormRegistrationId = res.registrationId;
      }
    }

    const { data, error } = await this.state.supabase
      .from('user_trackings')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0) as UserTrackingItem,
      error: error?.message,
    };
  }

  async updateTracking(id: string, enrollment: UserTrackingItem) {
    const payload = {
      progress: enrollment.progress,
      status: enrollment.status,
      rating: enrollment.rating,
      trackings: enrollment.trackings,
      lastTrace: enrollment.lastTrace,
      updated_by: getId(this.user),
    };

    const { data, error } = await this.state.supabase
      .from('user_trackings')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0) as UserTrackingItem,
      error: error?.message,
    };
  }

  async addTrackingCombo(
    learningPath: LearningInstructor,
    enrollment: UserTrackingItem
  ) {
    const payload = {
      enrollment: enrollment.enrollment,
      user: getId(this.user),
      progress: 1,
      status: TraceStatus.IN_PROGRESS,
      type:
        learningPath.type === 'LEARNINGPATH'
          ? LearningType.LEARNINGPATH
          : LearningType.INSTRUCTORLED,
      dueDate: enrollment.dueDate,
      rating: 0,
      trackings: [],
      lastTrace: null,
      created_by: getId(this.user),
    };

    const { data, error } = await this.state.supabase
      .from('user_trackings_combo')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []).at(0) as UserTrackingItem,
      error: error?.message,
    };
  }

  async updateTrackingCombo(id: string, enrollment: UserTrackingItem) {
    const payload = {
      progress: enrollment.progress,
      status: enrollment.status,
      rating: enrollment.rating,
      trackings: enrollment.trackings,
      lastTrace: enrollment.lastTrace,
      updated_by: getId(this.user),
    };

    const { data, error } = await this.state.supabase
      .from('user_trackings_combo')
      .update(payload)
      .eq('id', id)
      .select('*');

    return {
      data: (data ?? []).at(0) as UserTrackingItem,
      error: error?.message,
    };
  }
}
