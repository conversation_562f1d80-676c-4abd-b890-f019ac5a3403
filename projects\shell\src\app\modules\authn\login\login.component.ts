import { CommonModule, NgTemplateOutlet } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService, markControlsDirty, UserItem } from '@lms/core';
import { ValidationTextComponent } from '@lms/shared';
import { ChangeProfileComponent } from '../profile/profile.component';

@Component({
  selector: 'app-login',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ValidationTextComponent,
    NgTemplateOutlet,
    ChangeProfileComponent,
  ],
  templateUrl: './login.component.html',
})
export class LoginComponent {
  private authService = inject(AuthService);
  private router = inject(Router);

  form = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', Validators.required),
  });

  view: 'SIGIN' | 'VERIFY' | 'PROFILE' = 'SIGIN';

  message: string;
  currentEmail = '';
  _user?: any;

  get user() {
    return this.authService.userService.user ?? this._user;
  }

  get organization() {
    return this.authService.organization['name'] ?? '';
  }

  get f() {
    return this.form.controls;
  }

  async onSubmit() {
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    try {
      this.authService.isLoading = true;
      const {data, error} = await this.authService.signIn(
        this.form.value.email!,
        this.form.value.password!
      );
      this.authService.isLoading = false;
      this.currentEmail = this.form.value.email!;
      if (error) {
        this.view = error.includes('Email not confirmed') ? 'VERIFY' : 'SIGIN';
        this.message = error.includes('Email not confirmed')
          ? 'Please confirm your email address'
          : error;
        return;
      }
      this._user = data;
      const user = this.user;
      if (!user.phone || !user.lastname || !user.lastname) {
        this.view = 'PROFILE';
        return;
      }

      this.router.navigate(['/']);
    } catch (error: any) {
      this.message = error?.message;
    } finally {
      this.authService.isLoading = false;
    }
  }

  async onSubmitProfile(value: number) {
    if (!value) return;
    this.router.navigate(['/']);
  }

  gotForgotPassword() {
    this.router.navigate(['/reset-password']);
  }
  register() {
    this.router.navigate(['/sign-up']);
  }

  async reverify() {
    if (!this.currentEmail) return;
    this.authService.isLoading = true;
    await this.authService.resendCode(this.currentEmail);
    this.authService.isLoading = false;
  }
}
