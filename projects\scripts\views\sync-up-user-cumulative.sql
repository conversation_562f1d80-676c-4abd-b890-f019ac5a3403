UPDATE public.user_audits SET "signedUpDate" = temp.min
FROM signedIn_users_view temp
WHERE temp.id = public.user_audits.id;

UPDATE public.user_audits 
SET 
  "invitedDate" = CASE
           WHEN temp.invited_at is not null THEN temp.invited_at
           WHEN temp.invited_at is null THEN temp.created_at
       END
FROM auth.users temp
WHERE temp.id = public.user_audits.id
    AND "invitedDate" is null

    
UPDATE public.user_audits 
SET 
  "confirmDate" = temp.confirmed_at
FROM auth.users temp
WHERE temp.id = public.user_audits.id
    AND "confirmDate" is null