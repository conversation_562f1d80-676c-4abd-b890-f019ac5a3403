import { Common } from './common.model';
import { Organization, UserItem } from './user-info.model';

export type Team = {
  id: string;
  name: string;
  size: number;
  link: string;
  organizations?: Organization;
  groups?: GroupItem[];
  users?: UserItem[];
} & Common;

export type GroupItem = {
  id: string;
  name: string;
  size: number;
  link?: string;
  isdefault: boolean;
  team?: GroupItem;
  users?: UserItem[];
} & Common;

export type TeamGroupItemView = {
  user: UserItem;
  team: GroupItem;
  group: GroupItem;
};

export type UserTeamGroupItem = {
  organization: string;
  user: string;
  team: string;
  group: string;
  expires_at: string;
} & Common;
export type ITeamGroupItem = {
  userGroupId: string;
  userId: string;
  teamId: string;
  teamName: string;
  groupId: string;
  groupName: string;
  userName: string;
};
