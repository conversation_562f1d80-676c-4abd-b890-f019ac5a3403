<div class="flex flex-col justify-center items-center gap-5 px-10 py-20 w-full">
  @if (view === 'PLAN') {
  <h1 class="text-lot-blue text-3xl text-center font-bold mx-auto">
    Pick your plan based on the number of users! <br />
    All plans have the same features.
  </h1>
  <p class="text-center">* you will not be charged until the trial expires</p>

  @if (isLoading) {
  <div class="flex flex-col gap-5 justify-center items-center my-20">
    <mat-progress-bar mode="indeterminate" />
    <p class="text-center">Hold on, loading plans</p>
  </div>
  } @else {
  <ng-container *ngTemplateOutlet="planTemplate" />} } @if (view === 'PAYMENT')
  {
  <app-payment [data]="selectedPlans" />
  }
</div>

<ng-template #planTemplate>
  <div class="inline-flex rounded-full p-1 bg-lot-light-blue/30">
    @for (item of frequencies; track $index) {
    <button
      (click)="selectFrequency(item)"
      type="button"
      [class]="
        frequency() === item.value ? 'bg-lot-blue text-white' : 'text-lot-dark'
      "
      class="px-4 py-0.5 rounded-full focus:outline-none transition-colors duration-200 ease-linear"
    >
      {{ item.label }}
    </button>
    }
  </div>

  <section class="flex flex-wrap items-center justify-center gap-4">
    @for (item of plans(); track $index) {
    <div
      [class]="
        'cursor-pointer flex justify-center flex-col items-start flex-1 rounded-xl p-6 gap-0 bg-lot-gray/30 w-[184px] h-60 hover:bg-lot-light-blue/50 hover:border border-lot-blue transition-colors duration-200 ease-linear ' +
        (selectedPlan?.id === item.id
          ? 'bg-lot-light-blue/50 border border-lot-blue'
          : '')
      "
      (click)="selectPlan(item)"
    >
      <span class="text-lot-blue text-2xl">
        <i class="{{ item.icon }}"></i>
      </span>
      <h2 class="text-2xl font-bold text-lot-dark">{{ item.name }}</h2>
      <p class="text-sm">
        {{
          item.countMin === item.countMax
            ? item.countMax + "+ "
            : (item.countMin === 1 ? "upto " : item.countMin + " to ") +
              item.countMax
        }}
        users
      </p>
      <div class="flex flex-col gap-2 mt-10">
        <p class="text-base text-lot-dark font-semibold">
          {{ item.price | currency
          }}{{ item.frequency === "MONTHLY" ? "/month" : "/year" }}
        </p>
        @if (item.description) {
        <span class="text-lot-dark-gray text-sm">({{ item.description }})</span>
        }
      </div>
    </div>
    }
  </section>

  <div class="flex flex-col justify-center items-center gap-4">
    @for (item of addOns(); track $index) {
    <div
      class="flex justify-between gap-20 items-center bg-lot-ai/20 from-white to-lot-ai/70 py-6 px-8 rounded-xl"
    >
      <div class="flex-grow flex-row">
        <div class="flex gap-3">
          <span>
            <input
              type="checkbox"
              class="size-4 text-lot-dark bg-gray-100 border-gray-300 outline-none rounded-xl"
              (change)="addAddon($any($event.target).checked, item)"
              [checked]="selectedPlans.includes(item)"
            />
          </span>
          <div class="flex flex-col">
            <h5 class="text-lot-dark font-bold text-xs">
              {{ item.name }}
            </h5>
            <p class="text-xs">{{ item.description }}</p>
          </div>
        </div>
      </div>
      <span>${{ item.price + " " + item.pricingNote }}</span>
    </div>
    }
  </div>

  <button
    type="button"
    (click)="proceedPayment()"
    class="button-primary w-44 py-4 px-6"
  >
    Extend your Trial
  </button>
</ng-template>
