import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'numToAlphabet',
  standalone: true
})
export class NumToAlphabetPipe implements PipeTransform {
  alphabets = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
  ];
  transform(value: number, ...args: unknown[]): string {
    return this.alphabets[value] as string;
  }
}
