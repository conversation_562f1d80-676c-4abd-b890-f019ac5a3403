<div class="py-3">
  <div class="flex h-80">
    <div class="w-2/3">
      <img
        src="assets/images/new/hero-1.jpeg"
        alt=""
        class="w-full h-80 aspect-auto rounded-s-3xl"
      />
    </div>
    <div
      class="w-1/3 bg-lot-dark text-white px-8 py-16 flex flex-col justify-center rounded-e-3xl"
    >
      <h1 class="font-bold text-4xl text-white flex flex-col">
        <span>All new Course on</span>
        <span>Team Building</span>
      </h1>
      <p class="max-w-[500px]">
        O'nine timbers mizzen jack jib. <br />
        Nipperkin nipper jones' sheet.
      </p>
    </div>
  </div>

  <div class="flex flex-col gap-5 mt-10">
    <div class="flex items-center gap-4">
      <h2 class="font-semibold">Due soon ({{dueItems().length || 0}})</h2>
      <span class="bg-lot-dark h-[24px] w-[1px]"></span>
      @if(dueProgress()){
      <span class="text-sm font-semibold text-lot-blue">{{dueProgress()}}% complete</span>
      }
      <div class="w-[464px]">
        <app-progress [status]="defaultStatus" [progress]="dueProgress()" />
      </div>
    </div>

    <app-resource-loader [source]="myCourses" />
    <div class="grid grid-cols-4 gap-10">
      @for (item of dueItems(); track $index) {
      <ng-container
        *ngTemplateOutlet="
          tileCard;
          context: { item: item, showIcon: 1, showDate: 2 }
        "
      />
      }
    </div>

    <div class="flex justify-center items-center mt-5">
      @if (dueItems().length > 4) {
      <button (click)="goCatalog()" type="button" class="button-primary">View all Assigned Content</button>
      } @if (dueItems().length === 0) {
      <p class="text-center">No Content</p>
      }
    </div>
  </div>

  <div class="flex flex-col gap-5 mt-16">
    <div class="flex items-center gap-4">
      <h2 class="font-semibold">Assigned to you ({{myCourses.value()?.noDueItems?.length || 0}})</h2>
      <span class="bg-lot-dark h-[24px] w-[1px]"></span>
      @if(noDueProgress()){
      <span class="text-sm font-semibold text-lot-blue">{{noDueProgress()}}% complete</span>
      }
      <div class="w-[464px]">
        <app-progress [status]="defaultStatus" [progress]="noDueProgress()" />
      </div>
    </div>

    <app-resource-loader [source]="myCourses" />
    <div class="grid grid-cols-4 gap-10">
      @for (item of myCourses.value()?.noDueItems || []; track $index) {

      <ng-container
        *ngTemplateOutlet="
          tileCard;
          context: { item: item, showIcon: 1, showDate: 2 }
        "
      />
      }
    </div>

    <div class="flex justify-center items-center mt-5">
      @if ((myCourses.value()?.noDueItems?.length ?? 0) > 4) {
      <button (click)="goCatalog()" type="button" class="button-primary">View all Assigned Content</button>
      } @if (myCourses.value()?.noDueItems?.length === 0) {
      <p class="text-center">No Content</p>
      }
    </div>
  </div>

  <div class="flex flex-col gap-5 mt-16 mb-20">
    <div class="flex items-center gap-4">
      <h2 class="font-semibold">Suggeted for you</h2>
      <span class="bg-lot-dark h-[24px] w-[1px]"></span>
      @if(suggestedCourses.length){
        <span class="text-sm font-semibold text-lot-blue">40% complete</span>
      }
      <div class="w-[464px]">
        <app-progress [status]="defaultStatus" [progress]="suggestedCourses.length ? 33 : 0" />
      </div>
    </div>

    <div class="grid grid-cols-4 gap-10">
      @for (item of suggestedCourses; track $index) {
      <ng-container
        *ngTemplateOutlet="
          tileCard;
          context: { item: item, showIcon: 0, showDate: 0 }
        "
      />
      }
    </div>

    <div class="flex justify-center items-center mt-5">
      @if ((myCourses.value()?.noDueItems?.length ?? 0) > 4) {
      <button (click)="goCatalog()" type="button" class="button-primary">View all Suggested</button>
      } @else {
      <p class="text-center">No Content</p>
      }
    </div>
  </div>
</div>

<ng-template
  #tileCard
  let-item="item"
  let-showIcon="showIcon"
  let-showDate="showDate"
>
  <div class="flex flex-col gap-5 group">
    <a
      href="javascript:void(0);"
      (click)="viewCourse(item)"
      class="relative w-full h-40"
    >
      <img
        [src]="item.item?.cover || cover"
        alt=""
        class="w-full h-full object-cover rounded-3xl"
      />
      <div
        class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
      >
        <div class="hidden group-hover:flex text-white">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            class="size-12"
          >
            <path
              fill-rule="evenodd"
              d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
      </div>
    </a>

    <div
      class="flex-1"
      [class.text-lot-danger]="item.status === 'PAST'"
      [class.text-lot-warning]="item.status === 'DUE'"
    >
      <div class="flex flex-col gap-3 h-full">
        <h1
          class="font-semibold text-xl"
          [class.text-lot-danger]="item.status === 'PAST'"
          [class.text-lot-warning]="item.status === 'DUE'"
        >
          {{ item.item.name }}
        </h1>
        <div class="flex-grow text-base mb-2">
          <p>{{ item.short }}</p>
        </div>
        <div class="flex flex-col gap-3">
          <app-progress [status]="item.status" [progress]="item.progress" />
          <div
            class="flex gap-2 text-base"
            [class.text-lot-blue]="item.status === 'IN_PROGRESS'"
          >
            @if (showIcon === 1) {
            <span class="material-symbols-outlined">{{
              item.type === "COURSE"
                ? "book_5"
                : item.type === "INSTRUCTORLED"
                ? "hourglass_top"
                : "conversion_path"
            }}</span>
            }
            <span>{{
              item.progress
                ? item.progress + "% completed - Keep Going"
                : item.type === "COURSE"
                ? "Start Course"
                : item.type === "INSTRUCTORLED"
                ? "Start Instructor Led"
                : "Start Learning Path"
            }}</span>
          </div>
          @if (showDate === 2) {
          <span
            class="text-sm"
            [class.text-lot-dark-gray]="!['PAST', 'DUE'].includes(item.status)"
          >
            {{
              (item.status === "PAST"
                ? "Deadline passed on "
                : item.status === "DUE"
                ? "Due soon on "
                : "Deadline: ") + (!item.dueDate ? "--" : item.dueDate | date : "longDate")
            }}
          </span>
          }
        </div>
      </div>
    </div>
  </div>
</ng-template>
