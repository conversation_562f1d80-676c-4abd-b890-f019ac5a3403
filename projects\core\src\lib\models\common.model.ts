export class UploadDto {
  containerName?: string;
  folderName?: string;
  fileData?: any;
}

export class RequestDemoDto {
  id?: string;
  created_by?: string;
  created_at?: string;
  updated_by?: string;
  updated_at?: string;
  type?: string;
  courseDesciption?: string;
  budgetAmount?: number;
  firstname?: string;
  lastname?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  industry?: string;
  size?: number;
  country?: string;
  hasMaterial?: boolean;
  learnerCount?: number;
  dateNeeded?: string;
}

export class ApiResponse<T> {
  data?: T;
  count?: number;
  error?: string;
}

export interface DashboardQuickStat {
  activeCourse: number;
  module: number;
  course: number;
  quiz: number;
  learningPath: number;
  guest: number;
  student: number;
  teacher: number;
  admin: number;
  [key: string]: number;
}

export function mapResponse<T>(response: any): ApiResponse<T> {
  return <ApiResponse<T>>{
    data: !response?.error ? (response as T) : null,
    count: response?.count ?? 0,
    error: response?.error,
  };
}

export interface ApiRespone<T> {
  error?: string;
  count?: number;
  data: T[];
}

export interface BaseModel extends Common {}

export type Common = {
  id: string;
  created_at?: string;
  created_by?: string;
  updated_at?: string;
  updated_by?: string;
  createdName?: string;
  updatedName?: string;
};

export type EdgeError = {
  code: string;
  expected: string;
  message: string;
  path: string[];
  received: string;
};
