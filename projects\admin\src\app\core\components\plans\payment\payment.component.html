<div class="flex flex-col gap-5">
  <h1 class="text-lot-blue text-3xl text-center font-bold mx-auto">
    Proceed to payment
  </h1>

  @if (isLoading) {
  <mat-progress-bar mode="indeterminate" />
  } @if (status === 'NONE') { @if (error) {
  <p class="text-lot-danger text-center my-2">{{ error }}</p>
  }

  <div class="flex flex-col">
    <label class="block text-gray-700">Subscription Plan</label>
    <div class="pt-1 rounded-lg">
      {{ sub.name }}
    </div>
  </div>

  <div class="flex flex-col">
    <label class="block text-gray-700 mb-2">Amount</label>
    <div class="p-3 bg-gray-100 rounded-lg">
      {{ amount / 100 | currency }}
    </div>
  </div>

  <div class="flex flex-col">
    <label class="block text-gray-700 mb-2">Card Details</label>
    <div id="card-element" class="p-3 border border-gray-300 rounded-lg"></div>
  </div>

  <div class="flex flex-col gap-3 justify-center items-center">
    <button
      (click)="handleOneTimePayment()"
      type="button"
      class="button-primary-outline w-[235px] py-2 px-6"
    >
      <i class="fa-solid fa-money-check-dollar mr-2"></i>
      One Time Payment
    </button>

    <div class="terms-section border-t pt-4 mt-3">
      <label class="flex items-start">
        <input
          type="checkbox"
          [(ngModel)]="acceptedTerms"
          class="mt-1 mr-2"
          required
        />
        <div>
          I agree to the
          <a
            href="javascript:void(0)"
            (click)="openTerms()"
            class="text-lot-blue underline"
          >
            Subscription Terms & Conditions
          </a>
        </div>
      </label>
    </div>

    <button
      (click)="subscriptionPayment()"
      [disabled]="!acceptedTerms"
      type="button"
      class="button-primary w-fit py-2 px-6"
    >
      <i class="fa-solid fa-money-check-dollar mr-2"></i>
      Subscription Payment
    </button>
  </div>
  } @if (status === 'SUCCESS' && paymentIntent) {
  <div class="max-w-md mx-auto p-8 text-center">
    <div class="mb-6 text-green-500">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-20 w-20 mx-auto"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 13l4 4L19 7"
        />
      </svg>
    </div>

    <h1 class="text-3xl font-bold mb-4">Payment Successful!</h1>

    <div class="bg-gray-100 p-6 rounded-lg mb-6">
      <div class="flex justify-between mb-2">
        <span class="text-gray-600">Amount Paid:</span>
        <span class="font-semibold">{{
          paymentIntent.amount / 100 | currency
        }}</span>
      </div>
      <div class="flex justify-between mb-2">
        <span class="text-gray-600">Date:</span>
        <span class="font-semibold">{{
          paymentIntent.created * 1000 | date : "medium"
        }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Transaction ID:</span>
        <span class="font-mono text-sm">{{ paymentIntent.id }}</span>
      </div>
    </div>

    <div class="flex flex-col space-y-3">
      <button
        type="button"
        (click)="gotBack()"
        class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Go to Back
      </button>
    </div>
  </div>
  }
</div>
