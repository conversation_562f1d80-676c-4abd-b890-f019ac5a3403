<header class="py-3 md:py-5 bg-lot-dark">
  <div class="mx-auto px-10">
    <nav class="flex items-center justify-between h-14">
      <a href="javascript:void(0)" class="w-64 h-8">
        <img
          alt="Company Logo"
          class="w-auto h-full"
          src="assets/images/new/logo-dark.png"
        />
      </a>
      <div class="flex items-center">
        <div class="flex justify-between w-full">
          <div class="flex items-center gap-8">
            <span class="h-6 w-[1px] bg-white"></span>
            @if (source.isLoading()) {
            <span class="text-white">Loading...</span>
            }@if (source.error()) {
            <span class="text-rose-100">
              {{ source.error() }}
            </span>
            } @if (source.value()?.course; as course) {
            <div class="flex flex-col">
              <h2 class="text-white">
                {{ course.name || " -- Not Found Course --" }}
              </h2>
            </div>
            <a
              href="javascript:void(0)"
              (click)="back()"
              class="bg-transparent hover:bg-lot-light-gray/20 font-semibold py-1 px-2 rounded-md border-2 border-white text-center"
            >
              <img src="assets/images/new/mdi_share.svg" alt="" srcset="" />
            </a>
            <span class="h-6 w-[1px] bg-white"></span>
            <div class="flex gap-4 items-center">
              @if ((source.value()?.progress || 0)  < 2) {
                <button (click)="launch()" class="button-primary py-3 px-5">
                  Start Course!
                </button>
              }
              @if (mode === 'LEARNER') {
              <app-progress
                [status]="source.value()?.status!"
                [progress]="source.value()?.progress || 0"
              />
              <span class="text-white">{{
                source.value()?.progress
                  ? source.value()?.progress + "% complete - Keep Going"
                  : "0% complete - Time to Start!"
              }}</span>
              }
            </div>
            }
          </div>
        </div>
      </div>
      <div class="flex items-center gap-10">
        <ng-container *ngTemplateOutlet="menu" />
      </div>
    </nav>
  </div>
</header>

<ng-template #menu>
  <div class="relative">
    <button
      [matMenuTriggerFor]="menu"
      type="button"
      class="p-2 text-white focus:outline-none"
    >
      <span class="material-symbols-outlined text-4xl"> menu </span>
    </button>
    <mat-menu #menu="matMenu" class="max-h-[70vh] overflow-y-auto scrollbar">
      <div
        class="border-gray-200 w-[400px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark flex flex-col"
      >
        <a
          class="text-lot-danger font-semibold hover:text-lot-blue flex justify-between items-center gap-2"
          href="javascript:void(0)"
          (click)="back()"
        >
          Exit
          <i class="fa-solid fa-xmark text-3xl"></i>
        </a>
      </div>
      @for (item of navigations(); track $index; let last=$last) {
      <div
        class="border-gray-200 w-[400px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark flex flex-col"
        [class.border-b]="!last"
      >
        @if (item.type === 'TOP') {
        <a
          class="flex justify-between items-center gap-2 text-lot-ai-dark hover:text-lot-ai-dark"
          href="javascript:void(0)"
          (click)="gotTo(item)"
        >
          <div class="flex items-center gap-2">
            <span class="material-symbols-outlined">{{ item.icon }}</span>
            {{ item.name }}
          </div>
        </a>
        } @else {
        <a
          class="flex justify-between items-center gap-2"
          href="javascript:void(0)"
          [class.text-lot-blue]="item.current"
          [class.font-semibold]="item.current"
          [class.pointer-events-none]="!item.visited && !item.current"
          (click)="gotTo(item)"
        >
          <div class="flex items-center gap-2">
            <span class="material-symbols-outlined">{{ item.icon }}</span>
            {{ item.name }}
          </div>
          @if (item.visited) {
          <i class="fa-solid fa-circle-check text-lot-ai"></i>
          }@if (!item.visited && !item.current) {
          <i class="fa-solid fa-lock"></i>
          }
        </a>
        } @if (item.items?.length) {
        <div
          class="flex flex-col gap-2 ml-2 py-2 px-5 font-[500] text-base leading-10 text-lot-dark"
        >
          @for (subItem of item.items; track $index) {
          <a
            class="hover:text-lot-blue flex justify-between items-center gap-2"
            [class.text-lot-blue]="subItem.current"
            [class.font-semibold]="subItem.current"
            [class.pointer-events-none]="!subItem.visited && !subItem.current"
            href="javascript:void(0)"
            (click)="gotTo(subItem)"
          >
            <div class="flex items-center gap-2">
              <span class="material-symbols-outlined">{{ subItem.icon }}</span>
              {{ subItem.name }}
            </div>
            @if (subItem.visited) {
            <i class="fa-solid fa-circle-check text-lot-ai"></i>
            }@if (!subItem.visited && !subItem.current) {
            <i class="fa-solid fa-lock"></i>
            }
          </a>
          }
        </div>
        }
      </div>
      }
    </mat-menu>
  </div>
</ng-template>
