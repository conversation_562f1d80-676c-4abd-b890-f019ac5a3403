import { Component, EventEmitter, Input, Output } from '@angular/core';
import { HtmlWrapperComponent } from '@lms/shared';

@Component({
  selector: 'app-block-text',
  imports: [HtmlWrapperComponent],
  template: `
    @if(content){
    <div
      class="flex flex-col gap-4 rounded-lg cursor-pointer max-w-full overflow-hidden {{
        metaClass
      }}"
      [style]="styleForm"
      (click)="selectBlock()"
    >
      @if (content.heading) {
      <h1 class="text-2xl font-bold break-words">{{ content.heading }}</h1>
      } @if (content.subHeading) {
      <h2 class="text-xl font-bold break-words">{{ content.subHeading }}</h2>
      } @if (content.description) {
      <app-ui-html-wrapper
        class="text-lg break-words"
        [content]="content.description"
      />
      } @if (content.columns?.length) {
      <div class="grid grid-cols-{{ content.columnSize || 2 }} gap-5">
        @for (item of content.columns; track $index) {
        <p class="text-lg">{{ item }}</p>
        }
      </div>
      } @if (content.table) {
      <table class="min-w-full divide-y divide-gray-300 w-full">
        <thead>
          <tr>
            @for (item of content.table.headers; track $index) {
            <th
              scope="col"
              class="px-2 py-3.5 text-left text-sm font-semibold whitespace-nowrap text-gray-900"
            >
              {{ item }}
            </th>
            }
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-white">
          @for (row of content.table.rows; track $index) {
          <tr>
            @for (item of row; track $index) {
            <td class="px-2 py-2 text-sm whitespace-nowrap text-gray-900">
              {{ item }}
            </td>
            }
          </tr>
          }
        </tbody>
      </table>
      }
    </div>
    } @else {
    <div
      class="flex flex-col gap-4 rounded-lg cursor-pointer max-w-full overflow-hidden"
    >
      <p class="text-lot-danger">something malformed</p>
    </div>
    }
  `,
})
export class UIBlockTextComponent {
  @Input() content: {
    readyForLecture: boolean;
    heading?: string;
    subHeading?: string;
    description?: string;
    columns?: string[];
    columnSize?: number;
    table?: {
      headers: string[];
      rows: string[][];
    };
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  @Output() view = new EventEmitter();

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      // background: !this.content?.meta?.background
      //   ? 'bg-lot-gray/20'
      //   : this.content?.meta?.background.includes('#')
      //   ? `bg-[${this.content?.meta?.background}]`
      //   : 'bg-' + this.content?.meta?.background,
      // color: !this.content?.meta?.color
      //   ? ''
      //   : this.content?.meta?.color.includes('#')
      //   ? `text-[${this.content?.meta?.color}]`
      //   : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit(this.content);
  }
}

export const getTextTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Paragraph Block',
        description:
          'At Learn Or Teach, we value work-life balance and make it easy to take time off when you need it. Whether it’s for vacation, illness, or a personal matter, all time-off requests are submitted through our HR Portal. Vacation and personal days should be requested at least three business days in advance to help with scheduling, while sick days can be logged as needed. In this lesson, we’ll walk you through the different types of leave and how to submit a request step by step.',
        meta: {
          // width: 33,
          background: '#2980b9',
          color: 'white',
        },
      };
    case 1:
      return {
        uiLabel: 'Title & Paragraph Block',
        heading: 'How to Request Time Off at Learn Or Teach',
        description:
          'Requesting time off at Learn Or Teach is a straightforward process designed to support both individual needs and team planning. Whether you’re scheduling a vacation, managing a personal day, or taking unexpected sick leave, all requests go through our HR Portal. To keep things running smoothly, vacation and personal time should be submitted at least three business days in advance. Sick days can be entered on the same day. In this module, we’ll walk through the types of leave available and how to make your request step by step.',
        // meta: {
        //   width: 33,
        // },
      };
    case 2:
      return {
        uiLabel: 'Title, Subtitle & Paragraph Block',
        heading: 'Understanding Your Leave Options',
        subHeading:
          'The types of time off available to Learn Or Teach employees',
        description: `At Learn Or Teach, team members have access to several types of leave to fit different situations. These include <b>Vacation Days</b> for planned time away, <b>Sick Days</b> for health-related absences, and <b>Personal Days</b> for non-medical personal matters, and <b>Unpaid Leave</b> for extended time off when PTO has been used. Each type of leave has its own rules, but all are requested the same way through our HR Portal. Understanding which type of leave to use ensures accurate tracking and smooth approvals.
`,
      };
    case 3:
      return {
        uiLabel: 'Title, Paragraph & Columns Block',
        heading: 'Types of Leave at Learn Or Teach',
        description:
          'Learn Or Teach offers several leave types to support your personal and professional needs. Below is a quick overview of each type, so you know when to use them and how they work.',
        columns: [
          'Leave Type - Vacation Days',
          'Description - Planned time off to relax or travel.',
          'Leave Type - Sick Leave',
          'Description - Time off when you’re ill or caring for a family member.',
        ],
        columnSize: 2,
        // meta: {
        //   width: 33,
        // },
      };
    case 4:
      return {
        uiLabel: ' 2 Columns Block Only',
        columns: [
          'Python - Data Science, AI, Web Development Lorem ipsum dolor sit amet, consectetur adipiscing elit. Integer nec odio. Praesent libero. Sed cursus ante dapibus diam.',
          'JavaScript - Frontend, Backend Development Sed nisi. Nulla quis sem at nibh elementum imperdiet. Duis sagittis ipsum. Praesent mauris.',
        ],
        columnSize: 2,
      };
    case 5:
      return {
        uiLabel: 'Table Block',
        heading: 'Programming Language Comparison',
        table: {
          headers: ['Leave Type', 'Typical Approval Time', 'Notes'],
          rows: [
            [
              'Vacation Days',
              '1–3 business days',
              'Requests submitted late may be delayed.',
            ],
            [
              'Sick Leave',
              'Same day to 1 business day',
              'Immediate supervisor notification required.',
            ],
            [
              'Personal Days',
              '1–3 business days',
              'Immediate supervisor notification required.',
            ],
            [
              'Unpaid Leave',
              'Up to 5 business days',
              'Manager approval and HR review required.',
            ],
          ],
        },
        meta: {
          width: 100,
        },
      };
    default:
      return {
        uiLabel: 'Paragraph Block',
        heading: `At Learn Or Teach, we value work-life balance and make it easy to take time off when you need it. Whether it’s for vacation, illness, or a personal matter, all time-off requests are submitted through our HR Portal. Vacation and personal days should be requested at least three business days in advance to help with scheduling, while sick days can be logged as needed. In this lesson, we’ll walk you through the different types of leave and how to submit a request step by step. <br/><br/>
        At Learn Or Teach, team members have access to several types of leave to fit different situations. These include Vacation Days for planned time away, Sick Leave for health-related absences, Personal Days for non-medical personal matters, and Unpaid Leave for extended time off when PTO has been used. Each type of leave has its own rules, but all are requested the same way through our HR Portal. Understanding which type of leave to use ensures accurate tracking and smooth approvals.
        `,
      };
  }
};
