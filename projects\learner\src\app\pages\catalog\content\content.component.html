<div class="flex flex-col gap-5 my-10 w-full">
  <app-resource-loader [source]="dataSource" />
  <div
    class="gap-10"
    [class.flex]="view !== 'grid'"
    [class.flex-col]="view !== 'grid'"
    [class.grid]="view === 'grid'"
    [class.grid-cols-3]="view === 'grid'"
  >
    @for (item of dataSource.value() || []; track $index) { @if (view ===
    'grid') {
    <div class="flex flex-col gap-5 group">
      <a
        href="javascript:void(0);"
        (click)="goTo(item)"
        class="relative w-full h-40"
      >
        <img
          [src]="item.item.cover || cover"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <div
          class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
        >
          <div class="hidden group-hover:flex text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="size-12"
            >
              <path
                fill-rule="evenodd"
                d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </a>
      <div class="flex-1">
        <div class="flex flex-col gap-3 h-full">
          <a
            href="javascript:void(0);"
            (click)="goTo(item)"
            class="font-bold hover:underline text-xl"
            >{{ item.item.name }}</a
          >
          <div class="flex-grow text-base mb-2">
            <p class="break-words">{{ item.item.short.slice(0, 70) }}...</p>
          </div>
          <div class="flex flex-col gap-3">
            <app-progress [status]="item.status" [progress]="item.progress" />
            <div class="flex gap-2 text-base text-lot-blue">
              <span class="material-symbols-outlined">{{
                item.type === "COURSE"
                  ? "book_5"
                  : item.type === "LEARNINGPATH"
                  ? "conversion_path"
                  : item.type === "QUIZ"
                  ? "hourglass_top"
                  : "compass_calibration"
              }}</span>
              <span>{{
                item.progress
                  ? item.progress + "% completed - Keep Going"
                  : item.type === "COURSE"
                  ? "Start Course"
                  : item.type === "LEARNINGPATH"
                  ? "Start Learning Path"
                  : item.type === "QUIZ"
                  ? "Start Quiz"
                  : "Start Instructor Led"
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    } @if (view === 'list') {
    <div class="flex justify-between items-center border-b pb-8">
      <a
        href="javascript:void(0);"
        (click)="goTo(item)"
        class="font-bold hover:underline text-base"
        >{{ item.item.name }}</a
      >
      <div class="flex gap-2 text-base text-lot-blue">
        <span class="material-symbols-outlined">{{
          item.type === "COURSE"
            ? "book_5"
            : item.type === "LEARNINGPATH"
            ? "conversion_path"
            : item.type === "QUIZ"
            ? "hourglass_top"
            : "compass_calibration"
        }}</span>
        <span>{{
          item.progress
            ? item.progress + "% completed - Keep Going"
            : item.type === "COURSE"
            ? "Start Course"
            : item.type === "LEARNINGPATH"
            ? "Start Learning Path"
            : item.type === "QUIZ"
            ? "Start Quiz"
            : "Start Instructor Led"
        }}</span>
      </div>
    </div>
    } @if (view === 'detail') {
    <div class="flex gap-5 border-b pb-8 group">
      <a
        href="javascript:void(0);"
        (click)="goTo(item)"
        class="relative w-44 h-auto"
      >
        <img
          src="{{ item.item.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />

        <div
          class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
        >
          <div class="hidden group-hover:flex text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="size-12"
            >
              <path
                fill-rule="evenodd"
                d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </a>
      <div class="flex-1">
        <div class="flex flex-col gap-3 h-full">
          <a
            href="javascript:void(0);"
            (click)="goTo(item)"
            class="font-bold hover:underline text-xl"
            >{{ item.item.name }}</a
          >
          <div class="flex-grow text-base mb-2">
            <p>{{ item.item.short }}...</p>
          </div>
          <div class="flex items-center gap-8">
            <div class="flex gap-2 text-base text-lot-blue">
              <span class="material-symbols-outlined">{{
                item.type === "COURSE"
                  ? "book_5"
                  : item.type === "LEARNINGPATH"
                  ? "conversion_path"
                  : item.type === "QUIZ"
                  ? "hourglass_top"
                  : "compass_calibration"
              }}</span>
              <span>{{
                item.progress
                  ? item.progress + "% completed - Keep Going"
                  : item.type === "COURSE"
                  ? "Start Course"
                  : item.type === "LEARNINGPATH"
                  ? "Start Learning Path"
                  : item.type === "QUIZ"
                  ? "Start Quiz"
                  : "Start Instructor Led"
              }}</span>
            </div>
            <div class="w-72">
              <app-progress [status]="item.status" [progress]="item.progress" />
            </div>
          </div>
        </div>
      </div>
    </div>
    } @if (view === 'calendar') {
    <div class="flex gap-5">
      <span class="text-center my-1">Calendar</span>
    </div>
    } }@empty {
    <p class="text-center my-1 mx-auto">No Content</p>
    }
  </div>

  @if ((dataSource.value()?.length ?? 0) > 12) {
  <div class="flex justify-center items-center mt-5">
    <button class="button-primary">View more</button>
  </div>
  }
</div>
