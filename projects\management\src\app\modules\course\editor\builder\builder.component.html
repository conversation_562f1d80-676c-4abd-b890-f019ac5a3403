<div class="flex flex-col gap-5">
  <div class="flex flex-col gap-4">
    @for (module of modules; track $index; let first=$first) {
    <div class="flex flex-col border-t py-3">
      <div class="flex justify-between items-center gap-8 w-full">
        <h4 class="text-lot-dark font-semibold uppercase">{{ module.name }}</h4>

        <a
          href="javascript:void(0)"
          (click)="editModule(module)"
          class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
      </div>
      <div class="flex flex-col gap-2 mt-4 ml-10">
        @for (lesson of module.lessons; track $index) {
        <div
          class="flex justify-between items-center gap-3 py-4 max-w-md border-b"
        >
          <div class="flex items-center gap-2">
            <i class="fa-solid fa-circle text-[9px]"></i>
            <span>{{ lesson.name }}</span>
          </div>

          <div class="flex items-center gap-8">
            <button
              type="button"
              (click)="goToLesson(module, lesson)"
              class="button-primary w-fit"
            >
              Add Content
            </button>

            <a
              href="javascript:void(0)"
              (click)="editLesson(lesson)"
              class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
            >
              <i class="fa-solid fa-pen-to-square"></i>
            </a>
          </div>
        </div>
        }
        <a
          href="javascript:void(0)"
          class="flex items-center gap-3 text-lot-blue underline pt-4"
          [class.border-t]="!modules.length"
          (click)="add(module, 'LESSON')"
        >
          <i class="fa-solid fa-plus"></i>
          Add a Lesson
        </a>
      </div>
    </div>
    }
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-dark-gray border-y py-4"
      (click)="add()"
    >
      <i class="fa-solid fa-plus"></i>
      Add a Module
    </a>
  </div>
</div>

<ng-template #formTemplate>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 p-8 min-h-96"
  >
    <mat-dialog-content>
      <h1 class="text-2xl font-semibold mb-5">
        {{f['id'].value ? "Edit " : "Create a New " }} {{ type === "MODULE" ? "Module" : "Lesson" }}
      </h1>
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      } @if (error) {
      <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
      }
      <div class="form-lot-input mt-10 ">
        <div class="field">
          <input
            type="text"
            id="name"
            formControlName="name"
            placeholder="How to Make a Good Sale"
          />
          <label
            for="name"
            [class.error]="f['name'].invalid && f['name'].dirty"
          >
            Name your {{ type === "MODULE" ? "Module" : "Lesson" }}
          </label>
        </div>
        @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
        <div class="error">
          @if (f['name'].errors['required']) {
          <span
            >{{ type === "MODULE" ? "Module" : "Lesson" }} name is
            required</span
          >
          } @if (f['name'].errors['minlength']) {
          <span
            >{{ type === "MODULE" ? "Module" : "Lesson" }} name must be at least
            3 characters</span
          >
          }
        </div>
        }
      </div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <div class="flex justify-end items-center gap-4">
        <button
          class="button-primary-outline w-fit"
          click="dialogRef.close()"
          type="button"
        >
          Cancel
        </button>
        <button class="button-primary w-fit py-1.5" type="submit">
          Save and Close
        </button>
      </div>
    </mat-dialog-actions>
    <!-- <div class="form-lot-input mt-2">
      <div class="field">
        <quill-editor
          formControlName="description"
          [styles]="editorStyle"
          formControlName="description"
          placeholder="Enter your description"
        />
        <label
          for="description"
          [class.error]="f['description'].invalid && f['description'].dirty"
        >
          Give your {{ type === "MODULE" ? "Module" : "Lesson" }} a description
        </label>
      </div>
    </div> -->
  </form>
</ng-template>
