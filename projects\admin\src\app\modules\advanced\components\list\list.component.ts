import { Component, inject, Input } from '@angular/core';
import { MatPaginatorModule } from '@angular/material/paginator';
import { Course, LearningInstructor } from '@lms/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-instructor-list',
  imports: [MatPaginatorModule],
  templateUrl: 'list.component.html',
})
export class ListComponent {
  router = inject(Router);
  @Input() data: LearningInstructor[] = [];
  @Input() total = 0;

  goTo(item: LearningInstructor) {
    this.router.navigate(['/lms/advanced/view', item.id]);
  }
}
