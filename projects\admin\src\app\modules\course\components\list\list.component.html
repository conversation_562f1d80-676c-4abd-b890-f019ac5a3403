<div class="flex flex-col gap-5 my-10 w-full">
  <div class="grid grid-cols-4 gap-10">
    @for (item of data; track $index; let i=$index) {
    <div class="flex flex-col gap-5 group max-w-[340px] animate-fade-left animate-duration-[{{100*i}}ms]  animate-delay-[{{200*i}}ms]">
      <a
        href="javascript:void(0);"
        (click)="goTo(item)"
        class="relative w-full h-40"
      >
        <img
          src="{{ item.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
        <div
          class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
        >
          <div class="hidden group-hover:flex text-white">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              class="size-12"
            >
              <path
                fill-rule="evenodd"
                d="M4.5 5.653c0-1.427 1.529-2.33 2.779-1.643l11.54 6.347c1.295.712 1.295 2.573 0 3.286L7.28 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653Z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
        </div>
      </a>
      <div class="flex-1">
        <div class="flex flex-col gap-3 h-full">
          <a
            href="javascript:void(0);"
            (click)="goTo(item)"
            class="font-bold hover:underline text-base leading-tight"
            >{{ item.name }}</a
          >
          <div class="flex-grow text-sm mb-2">
            <p class="break-words">{{ item.short.slice(0, 90) }}...</p>
          </div>
          <!-- <div class="flex flex-col gap-3">
            <div class="flex gap-2 text-base text-lot-blue">
              <span class="material-symbols-outlined">book_5</span>
              <span>{{ '-'}}</span>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    }@empty {
    <p class="text-center my-1 mx-auto">No Content</p>
    }
  </div>

  @if ((data.length) > 12) {
  <div class="flex justify-center items-center mt-5">
    <button class="button-primary">View more</button>
  </div>
  }
</div>
