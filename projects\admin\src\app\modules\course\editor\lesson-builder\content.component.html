<app-resource-loader [source]="lessonSource" />
@if (lessonSource.value(); as item) {
<div class="flex flex-col h-full gap-3 pb-20">
  <div class="flex justify-between items-end">
    <div class="flex flex-col text-lot-dark">
      <a
        href="javascript:void(0)"
        (click)="back()"
        class="text-lot-blue text-sm flex items-center gap-2"
      >
        <span class="material-symbols-outlined"> arrow_back_ios_new </span>
        Back to <span class="text-lot-blue text-2xl">{{ item.course.name }}</span>
      </a>
  
      <h1 class="text-lot-dark text-2xl font-bold flex items-center gap-2">
        {{ item.module.name }}
        <i class="fa-solid fa-chevron-right text-lot-dark text-sm"></i>
        <span class="text-lot-blue text-2xl">{{ item.lesson.name }}</span>
      </h1>
    </div>
  
    <div class="flex gap-5">
      <button
        type="button"
        (click)="deleteLesson()"
        class="button-danger w-fit py-2 px-6"
      >
        Delete
      </button>
      <button
        type="button"
        (click)="preview()"
        class="button-primary w-fit py-2 px-6"
      >
        Preview
      </button>
    </div>
  </div>
  <div class="flex flex-col justify-center items-center gap-10 border-t pt-6">
    @if (!contents().length) {
    <p class="text-lot-dark-gray font-medium text-center">
      Start by adding a block
    </p>
    } @if (isLoading) {
    <mat-progress-bar mode="indeterminate" />
    } @if (error) {
    <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
    }
    <div class="flex-grow py-8 px-10 overflow-auto w-full">
      <div class="flex flex-col gap-14 w-full">
        @for (item of contents(); track item['id']; let i = $index) {
        <ng-container
          *ngTemplateOutlet="contentItem; context: { i: i, item: item }"
        />
        }
      </div>
    </div>
    <ng-container *ngTemplateOutlet="widgetPicker" />
  </div>
</div>
} @else {
<p class="text-center py-20">Lesson in progress ...</p>
<div class="flex justify-center items-center gap-5">
  @for (item of [1,2,3,4]; track $index) {
  <span class="size-6 bg-lot-blue rounded-full animate-pulse"></span>
  }
</div>
}

<ng-template #widgetPicker>
  <div class="flex flex-wrap items-center gap-8">
    @for (item of widgets.slice(0, 10); track $index) {
    <a
      href="javascript:void(0)"
      (click)="openMore(item)"
      class="flex flex-col gap-2"
    >
      <i
        class="{{ item.icon }} text-2xl {{
          item.shortName.includes('AI') ? 'text-lot-ai' : 'text-lot-blue'
        }}"
      ></i>
      <span class="text-sm">{{ item.shortName }}</span>
    </a>
    }

    <a
      href="javascript:void(0)"
      (click)="openMore(widgets[0])"
      class="w-20 flex justify-center items-center p-5 bg-lot-dark text-white rounded-2xl"
    >
      More Blocks
    </a>
  </div>
</ng-template>

<ng-template #contentItem let-i="i" let-item="item">
  <div class="flex group relative w-full h-full">
    <div
      class="absolute -top-10 left-0 right-0 hidden group-hover:flex justify-between items-center w-full h-12"
    >
      <div class="flex items-center gap-8 relative" id="overMenu-{{ i }}">
        <a
          href="javascript:void(0)"
          (click)="getAction('edit', i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="openFormatter(i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-ai/70 text-xl"
        >
          <i class="fa-solid fa-palette"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="openFormatter(i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
        >
          <i class="fa-solid fa-ruler"></i>
        </a>
      </div>
      <div class="flex items-center gap-8">
        <a
          href="javascript:void(0)"
          href="javascript:void(0)"
          (click)="getAction('moveUp', i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-dark/70 text-xl"
        >
          <i class="fa-regular fa-circle-up"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="getAction('moveDown', i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-dark/70 text-xl"
        >
          <i class="fa-regular fa-circle-down"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="getAction('clone', i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
        >
          <i class="fa-solid fa-clone"></i>
        </a>
        <a
          href="javascript:void(0)"
          href="javascript:void(0)"
          (click)="getAction('delete', i)"
          class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </div>
    </div>
    <app-dynamic-content
      [data]="item"
      (track)="getTrack($event)"
      class="w-full h-full"
    />
  </div>
</ng-template>
