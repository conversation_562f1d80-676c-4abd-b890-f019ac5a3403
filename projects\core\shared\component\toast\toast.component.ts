import { Component, inject, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToastMessageType } from '@lms/core';
import { MAT_SNACK_BAR_DATA } from '@angular/material/snack-bar';

@Component({
  selector: 'app-toast',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- max-w-xs p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-sm -->
    <div
      [id]="'toast-notification'"
      class="flex items-center w-full border-l-4 z-50"
      [ngClass]="{
        'border-green-500': data.type === 'success',
        'border-red-500': data.type === 'error',
        'border-orange-500 ': data.type === 'warning',
        'border-blue-500': data.type === 'info'
      }"
      role="alert"
    >
      <div
        class="inline-flex items-center justify-center shrink-0 w-8 h-8 rounded-lg"
        [ngClass]="{
          'text-green-500': data.type === 'success',
          'text-red-500 ': data.type === 'error',
          'text-orange-500': data.type === 'warning',
          'text-blue-500 ': data.type === 'info'
        }"
      >
        @if (data.type === 'success') {
        <span class="material-symbols-outlined"> task_alt </span>
        } @if (data.type === 'error') {
        <span class="material-symbols-outlined"> error </span>
        } @if (data.type === 'warning') {
        <span class="material-symbols-outlined"> warning </span>
        } @if (data.type === 'info') {
        <span class="material-symbols-outlined"> info </span>
        }
      </div>
      <div class="ms-3 text-sm">
        <div *ngIf="data.title" class="font-semibold">{{ data.title }}</div>
        <div class="font-normal" [innerHTML]="data.message"></div>
      </div>
      <!-- <button
        type="button"
        class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8"
        (click)="onClose()"
        aria-label="Close"
      >
        <span class="sr-only">Close</span>
        <svg
          class="w-3 h-3"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 14 14"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
          />
        </svg>
      </button> -->
    </div>
  `,
  styles: [
    `
      ::ng-deep .mat-mdc-snack-bar-label .mdc-snackbar__label {
        padding: 0 !important;
      }

      :host ::ng-deep {
        .mat-mdc-snackbar-surface {
          padding: 0 !important;
        }
      }
    `,
  ],
  encapsulation: ViewEncapsulation.None,
})
export class ToastComponent {
  data: {
    title?: string;
    message: string;
    type: ToastMessageType;
  } = inject(MAT_SNACK_BAR_DATA);

  @Input() onClose: () => void = () => {
    void 2;
  };
}
