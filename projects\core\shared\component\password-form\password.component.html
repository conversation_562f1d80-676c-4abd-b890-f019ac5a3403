<form [formGroup]="form" (ngSubmit)="submit()" class="flex flex-col gap-8 w-full">

  <div class="form-lot-input">
    <div class="field relative">
      <input
        [type]="showNewPassword ? 'text' : 'password'"
        id="newPassword"
        formControlName="newPassword"
        placeholder="Enter new password"
      />
      <label
        for="newPassword"
        [class.error]="f['newPassword'].invalid && f['newPassword'].dirty"
      >
        New Password
      </label>
      <button
        type="button"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        (click)="togglePasswordVisibility('new')"
      >
        <span class="material-icons text-gray-500">
          {{ showNewPassword ? "visibility_off" : "visibility" }}
        </span>
      </button>
    </div>
    @if(f['newPassword'].dirty && f['newPassword'].errors){
    <div class="mt-1 text-sm text-lot-danger">
      @if(f['newPassword'].errors['required']){
      <span>New password is required</span>
      } @if(f['newPassword'].errors['minlength']){
      <span> Password must be at least 8 characters </span>
      }
    </div>
    }
  </div>

  <div class="form-lot-input">
    <div class="field relative">
      <input
        [type]="showConfirmPassword ? 'text' : 'password'"
        id="confirmPassword"
        formControlName="confirmPassword"
        placeholder="Confirm new password"
      />
      <label
        for="confirmPassword"
        [class.error]="
          f['confirmPassword'].invalid && f['confirmPassword'].dirty
        "
      >
        Confirm New Password
      </label>
      <button
        type="button"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        (click)="togglePasswordVisibility('confirm')"
      >
        <span class="material-icons text-gray-500">
          {{ showConfirmPassword ? "visibility_off" : "visibility" }}
        </span>
      </button>
    </div>
    @if(f['confirmPassword'].dirty && f['confirmPassword'].errors){
    <div class="mt-1 text-sm text-lot-danger">
      @if(f['confirmPassword'].errors['required']){
      <span>Please confirm your password</span>
      } @if(f['confirmPassword'].errors['mustMatch']){
      <span> Passwords must match </span>
      }
    </div>
    }
  </div>

  <div class="bg-gray-50 p-4 rounded-xl">
    <h5 class="text-sm font-medium text-gray-700 mb-2">
      Password Requirements:
    </h5>
    <ul class="space-y-1 text-sm">
      <li [class]="getPasswordRequirementClass('minlength')">
        • At least 8 characters
      </li>
      <li [class]="getPasswordRequirementClass('upperCase')">
        • One uppercase letter (A-Z)
      </li>
      <li [class]="getPasswordRequirementClass('lowerCase')">
        • One lowercase letter (a-z)
      </li>
      <li [class]="getPasswordRequirementClass('number')">
        • One number (0-9)
      </li>
      <li [class]="getPasswordRequirementClass('specialChar')">
        • One special character (&#64;$!%*?&+.)
      </li>
    </ul>
  </div>

  <div class="flex justify-end items-center gap-5">
    <button
      type="button"
      (click)="cancel()"
      class="button-primary-outline w-fit"
    >
      Cancel
    </button>
    <button type="submit" class="button-primary w-fit">Change Password</button>
  </div>
</form>
