import {
  Component,
  computed,
  inject,
  input,
  OnD<PERSON>roy,
  OnInit,
  resource,
} from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import {
  LearningInstructorService,
  Lesson,
  ToastMessageType,
} from '@lms/core';
import { DialogComponent, ResourceHeaderComponent } from '@lms/shared';
import { BuilderComponent } from './builder/builder.component';
import { SettingsComponent } from './settings/settings.component';
import { Router } from '@angular/router';
import { AssignmentComponent } from './assignment/assign-team.component';
import { firstValueFrom } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { LearningInstructor } from '@lms/core';

@Component({
  selector: 'app-instructor-view',
  imports: [
    ResourceHeaderComponent,
    BuilderComponent,
    SettingsComponent,
    NgTemplateOutlet,
    AssignmentComponent,
    MatProgressBarModule,
  ],
  templateUrl: 'view.component.html',
})
export class ViewComponent implements OnInit, OnDestroy {
  readonly router = inject(Router);
  readonly service = inject(LearningInstructorService);
  readonly dialog = inject(MatDialog);
  id = input<string>('');

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'Structure',
    },
    {
      id: 2,
      name: 'Settings',
    },
    {
      id: 3,
      name: 'Assign Audience',
    },
  ];

  viewType: 'BUILDER' | 'SETTINGS' = 'BUILDER';

  get isSettings() {
    return this.service.viewType() === 'SETTINGS';
  }

  source = resource({
    request: () => this.id(),
    loader: async ({ request }) => {
      if (!request) {
        return;
      }
      const { data, error } = await this.service.getById(request);
      if (error || !data) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return data;
    },
  });

  type = computed(() => this.source.value()?.type || 'LEARNINGPATH');
  typeLabel = computed(() =>
    this.type() === 'LEARNINGPATH' ? 'Learning Path' : 'Instructor Led'
  );

  isLoading = false;
  isPreview = false;
  windowRef: any;
  error?: string;

  data = computed(() => {
    const item = this.source.value();
    return {
      id: item?.id!,
      label: this.typeLabel(),
      courseIds: (item?.type === 'LEARNINGPATH'
        ? item?.items?.map((x) => x.courses.map((c) => c.id)).flat()
        : item?.prerequisites?.map((x) => x.id)) as string[],
    };
  });

  ngOnInit(): void {
    // this.service.courseId.set(this.id());
  }

  back() {
    this.router.navigate(['/lms/advanced']);
  }

  preview(id: string) {
    if (this.tab === 2) {
      this.service.actionTrigger.next({
        type: 'SETTINGS',
        id// this.source.value()!,
      });
      return;
    }
    if (this.tab === 3) {
      this.service.actionTrigger.next({
        type: 'LEARNINGPATH',
        id, // this.source.value()!,
      });
      return;
    }

    this.isPreview = true;
    const query = new URLSearchParams({
      id,
      source: 'ADMIN',
    });

    this.windowRef = window.open(
      `go-training?${query.toString()}`,
      'LearnOrTeach',
      'popup'
    );
    this.windowRef?.focus();
  }

  setTab(tab: number) {
    this.tab = tab;
    this.service.viewType.set('LEARNINGPATH');
    if (tab === 2) {
      this.service.viewType.set('SETTINGS');
    }
  }
  exit() {
    this.windowRef?.close();
    this.isPreview = false;
  }
  ngOnDestroy(): void {
    this.exit();
  }

  async delete(item: LearningInstructor) {
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${
              item.name
            } ${this.typeLabel()}. All assigned users and contents associated to it will be deleted.`,
            title: `Are you sure to remove this ${this.typeLabel()}?`,
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.delete(item.id);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
        this.service.state.openToast({
          title: 'Delete Request Failed',
          message: 'Failed: ' + result.error,
          type: ToastMessageType.ERROR,
        });
      }
      if (result.data) {
        this.service.state.openToast({
          title: 'Delete Request Successful',
          message: `${this.typeLabel()} deleted successfully`,
          type: ToastMessageType.SUCCESS,
        });
        this.router.navigate(['/lms/advanced']);
        // this.service.coursesSource.reload();
      }
    }
  }
}
