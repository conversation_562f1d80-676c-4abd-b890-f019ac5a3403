import {
  AfterViewInit,
  Component,
  TemplateRef,
  inject,
  viewChild,
} from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { UploadFormComponent } from '../user/users/upload/upload-form.component';
import { Router } from '@angular/router';
import { PlansComponent } from '../../core';
import { GlobalStateService } from '@lms/core';
import { UploadScormFormComponent } from '../course/components/upload/upload-form.component';

@Component({
  selector: 'app-home',
  imports: [MatDialogModule],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements AfterViewInit {
  router = inject(Router);
  readonly dialog = inject(MatDialog);
  service = inject(GlobalStateService);
  promoRef = viewChild<TemplateRef<any>>('promo');
  coursesRef = viewChild<TemplateRef<any>>('courses');

  get subscription() {
    return this.service.mySubscription();
  }

  get profileCompleted() {
    return (
      !!this.service.user().lastname &&
      !!this.service.user().lastname &&
      !!this.service.user().phone
    );
  }

  get dayCount() {
    const date = new Date(this.subscription.expired_at ?? 0);
    const now = new Date();
    return Math.ceil(
      Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)
    );
  }

  get isExpiringSoon() {
    return this.dayCount <= 3;
  }

  ngAfterViewInit(): void {
    if (this.isExpiringSoon) {
      this.dialog.open(this.promoRef()!);
    }
  }

  subscribe(): void {
    this.dialog.open(PlansComponent, {
      minWidth: '600px',
      maxWidth: '1024px',
    });
  }

  action(id: number) {
    if (id === 1) {
      this.router.navigate(['/lms/settings']);
    }
    if (id === 2) {
      this.dialog.open(UploadFormComponent, {
        minWidth: '800px',
      });
    }
    if (id === 3) {
      this.dialog.open(UploadScormFormComponent, {
        minWidth: '800px',
      });
    }
  }
}
