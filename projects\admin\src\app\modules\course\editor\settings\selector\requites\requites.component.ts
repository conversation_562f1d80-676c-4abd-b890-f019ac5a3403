import { Component, inject, OnInit, resource, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { CourseCoreService } from '@lms/core';
import { ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-course-requisite',
  imports: [FormsModule, ResourceHeaderComponent],
  templateUrl: './requites.component.html',
})
export class CourseRequisiteComponent implements OnInit {
  dialogRef: MatDialogRef<CourseRequisiteComponent> = inject(MatDialogRef);
  service = inject(CourseCoreService);

  data: 'PRE' | 'POST' = inject(MAT_DIALOG_DATA);

  filter = signal<string | undefined>(undefined);

  courseSource = resource({
    request: () => this.filter(),
    loader: async ({ request }) => {
      if (!request) {
        const { data, error, count } = await this.service.getCourses({
          payload: {
            type: 'MY',
          },
          paging: {
            page: 1,
            size: 10,
          },
        });
        if (error) {
          throw new Error('Unable to fetch data. Please refresh this page');
        }
        return {
          data: data.map((x) => ({
            ...x,
            selected: false,
          })),
          count: count,
        };
      }
      const [myCourses, libraryCourses] = await Promise.all([
        await this.service.getCourses({
          payload: {
            query: request,
            type: 'MY',
          },
          paging: {
            page: 1,
            size: 10,
          },
        }),
        await this.service.getCourses({
          payload: {
            query: request,
            type: 'LIBRARY',
          },
          paging: {
            page: 1,
            size: 10,
          },
        }),
      ]);
      if (myCourses.error || libraryCourses.error) {
        throw new Error('Unable to fetch data. Please refresh this page');
      }
      return {
        data: [...myCourses.data, ...libraryCourses.data].map((x) => ({
          ...x,
          selected: false,
        })),
        count: myCourses.count + libraryCourses.count,
      };
    },
  });

  courses: {
    id: string;
    name: string;
    cover: string;
    type: 'PRE' | 'POST';
    selected: boolean;
  }[] = [];

  ngOnInit(): void {
    this.filter.set('');
    this.courseSource.reload();
  }

  onSelectionChange(checked: boolean, item: any) {
    if (checked) {
      this.courses.push({
        id: item.id,
        name: item.name,
        cover: item.cover,
        type: this.data,
        selected: true,
      });
    } else {
      this.courses = this.courses.filter((course) => course.id !== item.id);
    }
  }

  saveSelection() {
    this.dialogRef.close({
      courses: this.courses,
    });
  }
}