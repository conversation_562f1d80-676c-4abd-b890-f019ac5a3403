<div class="flex flex-col h-full">
  <div class="flex justify-between items-end">
    <div class="text-sm font-medium text-center text-gray-500">
      <ul class="flex flex-wrap -mb-px">
        <li class="me-2">
          <a
            href="javascript:void(0)"
            (click)="setTab(1)"
            class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
            [class.text-lot-dark]="tab === 1"
            [class.border-transparent]="tab !== 1"
            [class.border-lot-blue]="tab === 1"
          >
            Courses
          </a>
        </li>
        <li class="me-2">
          <a
            href="javascript:void(0)"
            (click)="setTab(2)"
            class="inline-block font-bold text-lg p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-lot-blue"
            [class.text-lot-dark]="tab === 2"
            [class.border-transparent]="tab !== 2"
            [class.border-lot-blue]="tab === 2"
          >
            Learning Paths
          </a>
        </li>
      </ul>
    </div>

    <!-- <button type="button" (click)="saveCourse()" class="button-primary w-fit py-2 px-6">
      Save from SCORM Cloud
    </button> -->
    <button type="button" (click)="add()" class="button-primary w-fit py-2 px-6">
      {{ type() === 'COURSE'  ? "Create Course +" : type() === 'LEARNINGPATH' ? 'Add Learning Path' : "Add Lesson" }}
    </button>
  </div>

  <div class="flex-grow my-10 bg-white p-8 rounded-xl shadow-md w-full">
    @if (tab === 1) {
    <app-course class="w-full h-full" />
    } @if (tab === 2) {
    <!-- <app-teams class="w-full h-full" /> -->
    }
  </div>
</div>
