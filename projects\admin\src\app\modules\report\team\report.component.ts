import { Component, OnInit, resource } from '@angular/core';
import { ResourceHeaderComponent } from '@lms/shared';

@Component({
  selector: 'app-report-team',
  imports: [ResourceHeaderComponent],
  templateUrl: './report.component.html',
})
export class TeamReportComponent implements OnInit {
  source = resource({
    loader: async ({ request }) => {
      return Promise.resolve([]);
    },
  });

  ngOnInit() {}
}
