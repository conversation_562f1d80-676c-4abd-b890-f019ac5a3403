<div class="flex flex-col gap-5 px-10 py-10">
  @if (view === 'NONE') {
  <h1 class="text-2xl font-semibold">Create a New Course</h1>
  <div class="grid grid-cols-2 gap-8 h-52">
    <a
      href="javascript:void(0)"
      (click)="addNewUser()"
      class="flex flex-col justify-center items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
    >
      <i class="fa-solid fa-user text-lot-blue"></i>
      <span class="text-lot-dark font-semibold">Build Your own Course</span>
    </a>
    <a
      href="javascript:void(0)"
      (click)="view = 'UPLOAD'"
      class="flex flex-col justify-center items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
    >
      <i class="fa-solid fa-file-excel text-lot-blue"></i>
      <span class="text-lot-dark font-semibold"
        >Upload your SCORM, xAPI, AICC or cmi5 course.</span
      >
    </a>
  </div>

  } @if (view !== 'NONE') {
  <div class="flex items-center justify-between">
    <h1 class="text-2xl font-semibold">SCORM Upload</h1>
  </div>
  @if (error) {
  <p class="text-lot-danger mb-4 text-center">{{ error }}</p>
  } @if (view === 'UPLOAD') {
  <ng-container *ngTemplateOutlet="uploader" />
  } }
</div>

<ng-template #uploader>
  <div class="flex flex-col gap-5 p-8">
    @if (!scormFile) {
    <a
      href="javascript:void(0)"
      class="relative flex flex-col justify-center items-center gap-2 border-2 border-dashed border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20 h-72"
    >
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      } @if (!isLoading) {
      <i class="fa-solid fa-arrow-up-from-bracket text-lot-blue text-4xl"></i>
      <span class="text-lot-dark font-medium text-center">
        Drop your SCORM, xAPI, AICC or cmi5 course here. <br />
        Or click here to upload the course.
      </span>
      <input
        type="file"
        accept=".zip"
        (change)="onFileChange($event)"
        class="absolute inset-0 opacity-0 cursor-pointer"
      />
      }
    </a>
    } @if (scormFile) {

    <form
      [formGroup]="form"
      (ngSubmit)="onSubmit()"
      class="flex flex-col gap-5 p-5"
    >
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      <p class="text-lot-dark font-medium text-center">
        <b>Please be patient while we're processing your data</b>
      </p>
      } @if (error) {
      <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
      }

      <div class="flex flex-col gap-2">
        <label for="scormFile">Uploaded Course: </label>
        <span class="text-lot-dark font-medium text-center text-lg">
          {{ scormFile.name }}
        </span>
      </div>
      <div class="form-lot-input">
        <div class="field">
          <input
            type="text"
            id="title"
            formControlName="title"
            placeholder="How to Make a Good Sale"
          />
          <label
            for="title"
            [class.error]="f['title'].invalid && f['title'].dirty"
          >
            Name your Course
          </label>
        </div>
        @if(f['title'].errors && f['title'].invalid && f['title'].dirty){
        <div class="error">
          @if (f['title'].errors['required']) {
          <span>Course name is required</span>
          } @if (f['title'].errors['minlength']) {
          <span>Course name must be at least 3 characters</span>
          }
        </div>
        }
      </div>

      <div class="mt-2">
        <app-rich-text [form]="form" label="Give your course a description" />
      </div>

      <div class="flex justify-end items-center gap-4">
        <button class="button-primary-outline w-fit" type="submit">
          Cancel
        </button>
        <button class="button-primary w-fit py-1.5" type="submit">
          Start Course Creation
        </button>
      </div>
    </form>

    }
  </div>
</ng-template>
