import { inject, Injectable } from '@angular/core';
import { EdgeError, GlobalStateService, tryPromise, UserItem } from '@lms/core';

@Injectable({
  providedIn: 'root',
})
export class AIService {
  state = inject(GlobalStateService);

  get user(): UserItem {
    return this.state.user();
  }

  async generateText(prompt: string, format: 'json' | 'html' | 'text') {
    const aiProvider = 'xai'; // 'oai';
    const { data, error } = await this.state.supabase.functions.invoke<
      | string
      | {
          data: string;
          error: EdgeError[];
        }
    >('ai-texgen', {
      body: JSON.stringify({ query: prompt, backend: aiProvider }),
    });

    const res =
      typeof data === 'string'
        ? this.mapResponse(format, JSON.parse(data).data)
        : this.mapResponse(format, data?.data);

    return {
      data: res,
      error: (typeof data === 'string'
        ? undefined
        : data?.error
            ?.filter(Boolean)
            ?.map((e) => e.message)
            ?.join(', ') ?? error?.message) as string | undefined,
    };
  }

  async generateImage(prompt: string) {
    const aiProvider = 'oai'; // 'oai';
    const { data, error } = await this.state.supabase.functions.invoke<{
      data: string;
      error: EdgeError[];
    }>('ai-imgen', {
      body: JSON.stringify({
        query: prompt,
        backend: aiProvider,
        size: '1024x1024',
        model: 'dall-e-3',
      }),
    });

    const res =
      typeof data === 'string'
        ? (JSON.parse(data).data as string[])
        : undefined;
    // {"error":{"code":"internal_error","message":"Internal error"}}
    return {
      // data: res?.map((x: any) => x.url as string).filter(Boolean),
      data: res
        ?.map((x: any) => ('data:image/jpeg;base64, ' + x.b64_json) as string)
        .filter(Boolean),
      error: (!Array.isArray(data?.error)
        ? (data?.error as any)?.message
        : data?.error
            ?.filter(Boolean)
            ?.map((e) => e.message)
            ?.join(', ') ?? error?.message) as string | undefined,
    };
  }

  // async genAIImage() {
  //   const token = '';
  //   const options = {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       Authorization: 'Bearer ' + token,
  //     },
  //     body: JSON.stringify({
  //       prompt: 'A cat',
  //       n: 4,
  //       size: '1024x1024',
  //     }),
  //   };

  //   const [error, res] = await tryPromise(
  //     fetch('https://api.openai.com/v1/images/generations', options).then(
  //       (response) => response.json()
  //     )
  //   );

  //   return {
  //     data: res,
  //     error: error?.message,
  //   };
  // }

  mapResponse(format: 'json' | 'html' | 'text', res?: string) {
    if (!res) return;
    if (['html', 'text'].includes(format)) {
      return res.replace(/```html/g, '').replace(/```/g, '');
    }
    const objString = res.replace(/```json/g, '').replace(/```/g, '');
    return JSON.parse(objString);
  }
}
