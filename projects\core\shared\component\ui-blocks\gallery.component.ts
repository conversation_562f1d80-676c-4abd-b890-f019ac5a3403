import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { HtmlWrapperComponent, UIBlockData, WidgetType } from '@lms/shared';

@Component({
  selector: 'app-block-gallery',
  imports: [HtmlWrapperComponent],
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg h-full {{ metaClass }}"
      [style]="styleForm"
      (click)="selectBlock()"
    >
      @if (content.title) {
      <h3 class="text-lg font-semibold my-2">{{ content.title }}</h3>
      }
      <!-- Grid Layout -->
      @if (content.type === 'grid') {
      <div class="grid gap-4 grid-cols-{{ content.columnSize }}">
        @for (image of content.images; track $index) {
        <div class="relative group aspect-square">
          <img
            [src]="image.url"
            [alt]="image.caption || ''"
            class="w-full h-full object-cover rounded-lg"
          />
          @if (image.caption) {
          <div
            class="absolute inset-0 bg-lot-dark/70 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex items-end"
          >
            <app-ui-html-wrapper
              class="text-lg break-words"
              [content]="image.caption"
            />
          </div>
          }
        </div>
        }
      </div>
      }

      <!-- Carousel Layout -->
      @if (content.type === 'carousel') {
      <div class="relative">
        <div class="overflow-hidden">
          <div
            class="flex transition-transform duration-300 ease-in-out"
            [style.transform]="'translateX(-' + currentIndex * 100 + '%)'"
          >
            @for (image of content.images; track $index) {
            <div class="w-full flex-shrink-0">
              <div class="relative aspect-video">
                <img
                  [src]="image.url"
                  [alt]="image.caption || ''"
                  class="w-full h-full object-cover rounded-lg"
                />
                @if (image.caption) {
                <div
                  class="absolute bottom-0 left-0 right-0 bg-lot-dark/70 p-4 rounded-b-lg"
                >
                  <app-ui-html-wrapper
                    class="text-lg break-words"
                    [content]="image.caption"
                  />
                </div>
                }
              </div>
            </div>
            }
          </div>
        </div>

        <!-- Navigation Buttons -->
        @if (content.images.length > 1) {
        <a
          href="javascript:void(0)"
          (click)="prevSlide()"
          class="absolute left-2 top-1/2 -translate-y-1/2 bg-lot-dark/50 hover:bg-lot-dark text-white rounded-full size-10 flex items-center justify-center p-2"
          [class.invisible]="currentIndex === 0"
        >
          <i class="fas fa-chevron-left"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="nextSlide()"
          class="absolute right-2 top-1/2 -translate-y-1/2 bg-lot-dark/50 hover:bg-lot-dark text-white rounded-full size-10 flex items-center justify-center p-2"
          [class.invisible]="currentIndex === content.images.length - 1"
        >
          <i class="fas fa-chevron-right"></i>
        </a>

        <!-- Dots Indicator -->
        <div class="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
          @for (image of content.images; track $index; let i=$index) {
          <button
            (click)="goToSlide(i)"
            class="w-2 h-2 rounded-full transition-colors duration-200 {{
              currentIndex !== i ? 'bg-white/50' : 'bg-white'
            }}"
            [class.bg-white]="currentIndex === i"
          ></button>
          }
        </div>
        }
      </div>
      }
    </div>
  `,
})
export class UIBlockGalleryComponent {
  @Input() content: {
    readyForLecture: boolean;
    title?: string;
    images: Array<{
      url: string;
      caption?: string;
    }>;
    type: 'grid' | 'carousel';
    columnSize: number;
    requirePass: boolean;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    const position =
      (this.content?.meta?.width || 0) < 100
        ? ' mx-auto justify-center items-center '
        : '';
    return Object.values(meta).join(' ') + position;
  }

  @Output() view = new EventEmitter<UIBlockData>();

  currentIndex = 0;

  selectBlock() {
    if (this.content.readyForLecture) return;
    this.view.emit({ widgetType: WidgetType.GALLERY, content: this.content });
  }

  prevSlide(): void {
    if (this.currentIndex > 0) {
      this.currentIndex--;
    }
  }

  nextSlide(): void {
    if (this.currentIndex < this.content.images.length - 1) {
      this.currentIndex++;
    }

    if (this.currentIndex === this.content.images.length - 1) {
      this.view.emit({
        widgetType: WidgetType.GALLERY,
        content: this.content,
        userAnswers: { completed: true },
      });
    }
  }

  goToSlide(index: number): void {
    this.currentIndex = index;
  }
}

export const getGalleryTemplate = (type: number) => {
  switch (type) {
    case 0: // Basic Grid Layout - 2 columns
      return {
        uiLabel: 'Basic Grid Layout - 2 columns',
        type: 'grid',
        columnSize: 2,
        images: [
          { url: 'https://picsum.photos/800/450', caption: 'John Doe - CEO' },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Jane Smith - CTO',
          },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Mike Johnson - Lead Developer',
          },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Mike Johnson - Lead Developer',
          },
        ],
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      };

    case 1: // Basic Carousel
      return {
        uiLabel: 'Basic Carousel',
        type: 'carousel',
        columnSize: 1, // Not used in carousel but required by type
        images: [
          { url: 'https://picsum.photos/800/450', caption: 'John Doe - CEO' },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Jane Smith - CTO',
          },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Mike Johnson - Lead Developer',
          },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Mike Johnson - Lead Developer',
          },
        ],
        meta: {
          width: 100,
          height: 50,
          padding: 0,
          background: 'lot-dark',
          color: 'white',
        },
      };

    default: // Default Gallery Configuration
      return {
        uiLabel: 'Basic Grid Layout - 2 columns',
        type: 'grid',
        columnSize: 2,
        images: [
          { url: 'https://picsum.photos/800/450', caption: 'John Doe - CEO' },
          {
            url: 'https://picsum.photos/800/450',
            caption: 'Jane Smith - CTO',
          },
        ],
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };
  }
};
