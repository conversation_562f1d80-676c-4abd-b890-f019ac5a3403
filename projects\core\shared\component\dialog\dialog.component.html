<div class="p-10" *ngIf="config">
  @if (config.title) {
  <div class="flex items-center gap-3">
    <div
      class="flex items-center justify-center h-12 w-12 rounded-full bg-green-100"
      *ngIf="!config.hideModalIcon"
      [ngClass]="{
        'bg-green-100': config.type === 'SUCCESS',
        'bg-red-100 ': config.type === 'ERROR' || config.type === 'DELETE',
        'bg-blue-100': config.type === 'INFO' || config.type === 'WARNING'
      }"
    >
      <svg
        *ngIf="config.type === 'SUCCESS'"
        class="h-6 w-6 text-green-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 13l4 4L19 7"
        ></path>
      </svg>
      <svg
        *ngIf="config.type === 'INFO' || config.type === 'WARNING'"
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 text-yellow-600"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
      <svg
        *ngIf="config.type === 'ERROR' || config.type === 'DELETE'"
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6 text-red-600"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M6 18L18 6M6 6l12 12"
        />
      </svg>
    </div>
    <h3 class="text-lg leading-6 font-medium text-gray-900">
      {{ config.title }}
    </h3>
  </div>
  } @if (config.message) {
  <p class="text-base text-gray-500 mb-4 mt-2">
    {{ config.message }}
  </p>
  }

  <div class="flex flex-wrap items-center gap-4 px-4 py-3 justify-end">
    <button
      id="ok-btn"
      *ngIf="config.cancelButton"
      [class]="
        config.cancelClass
          ? config.cancelClass
          : 'px-4 py-1 bg-white border border-lot-blue text-lot-blue text-base font-medium rounded-md shadow-sm hover:bg-blue-700 outline-none focus:ring-2 focus:ring-blue-300'
      "
      (click)="dialogRef.close(0)"
    >
      {{ config.cancelButton }}
    </button>
    <button
      id="ok-btn"
      *ngIf="config.confirmButton"
      (click)="dialogRef.close(1)"
      class="px-4 py-1.5 text-white text-base font-medium rounded-md shadow-sm outline-none focus:ring-2"
      [ngClass]="{
        'bg-green-500 hover:bg-green-600 focus:ring-green-300':
          config.type === 'SUCCESS',
        'bg-red-500 hover:bg-red-600 focus:ring-red-300':
          config.type === 'ERROR' || config.type === 'DELETE',
        'bg-lot-blue hover:bg-blue-700 focus:ring-blue-300':
          config.type === 'INFO' || config.type === 'WARNING'
      }"
    >
      {{ config.confirmButton }}
      <i
        class="fa fa-arrow-right ml-2"
        aria-hidden="true"
        *ngIf="config.showConfirmArrowIcon"
      ></i>
    </button>
  </div>
</div>
