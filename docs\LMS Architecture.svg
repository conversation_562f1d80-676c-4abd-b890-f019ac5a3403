<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="689px" height="504px" viewBox="-0.5 -0.5 689 504" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-08-31T20:25:57.965Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.5112.102 Safari/537.36&quot; etag=&quot;0goEe1B_n_kJHVYxRJkI&quot; version=&quot;20.2.7&quot;&gt;&lt;diagram id=&quot;tBn2ew-JMekaJpDPOEMT&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-945df2-1-5a30b5-1-s-0"><stop offset="0%" style="stop-color: rgb(90, 48, 181); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(148, 93, 242); stop-opacity: 1;"/></linearGradient><linearGradient x1="0%" y1="100%" x2="0%" y2="0%" id="mx-gradient-ff4f8b-1-bc1356-1-s-0"><stop offset="0%" style="stop-color: rgb(188, 19, 86); stop-opacity: 1;"/><stop offset="100%" style="stop-color: rgb(255, 79, 139); stop-opacity: 1;"/></linearGradient></defs><g><path d="M 0 10 L 420 10 L 420 490 L 0 490 Z" fill="none" stroke="#cd2264" stroke-miterlimit="10" pointer-events="none"/><path d="M 0 10 L 0 35 L 25 35 L 25 10 L 0 10 Z M 4.09 13.69 L 20.91 13.69 C 21.01 13.69 21.12 13.73 21.19 13.81 C 21.27 13.88 21.31 13.99 21.31 14.09 L 21.31 30.91 C 21.31 31.01 21.27 31.12 21.19 31.19 C 21.12 31.27 21.01 31.31 20.91 31.31 L 4.09 31.31 C 3.99 31.31 3.88 31.27 3.81 31.19 C 3.73 31.12 3.69 31.01 3.69 30.91 L 3.69 14.09 C 3.69 13.99 3.73 13.88 3.81 13.81 C 3.88 13.73 3.99 13.69 4.09 13.69 Z M 4.49 14.49 L 4.49 30.51 L 20.51 30.51 L 20.51 14.49 L 4.49 14.49 Z M 16.5 15.31 C 16.65 15.31 16.79 15.38 16.86 15.52 L 19.46 20.72 C 19.53 20.84 19.52 20.99 19.45 21.11 C 19.37 21.23 19.25 21.3 19.11 21.3 L 13.9 21.3 L 13.9 21.3 C 13.76 21.3 13.63 21.23 13.56 21.11 C 13.49 20.99 13.48 20.84 13.54 20.72 L 16.14 15.52 C 16.21 15.38 16.36 15.31 16.5 15.31 Z M 16.5 16.59 L 14.55 20.5 L 18.46 20.5 L 16.5 16.59 Z M 6.09 19.3 L 11.3 19.3 C 11.41 19.3 11.51 19.34 11.58 19.41 C 11.66 19.49 11.7 19.59 11.7 19.7 L 11.7 24.9 C 11.7 25.01 11.66 25.11 11.58 25.19 C 11.51 25.26 11.41 25.3 11.3 25.3 L 6.09 25.3 C 5.99 25.3 5.89 25.26 5.81 25.19 C 5.74 25.11 5.69 25.01 5.69 24.9 L 5.69 19.7 C 5.69 19.59 5.74 19.49 5.81 19.41 C 5.89 19.34 5.99 19.3 6.09 19.3 Z M 6.49 20.1 L 6.49 24.5 L 10.9 24.5 L 10.9 20.1 L 6.49 20.1 Z M 15.7 23.3 C 17.47 23.3 18.9 24.74 18.91 26.5 C 18.9 28.27 17.47 29.7 15.7 29.71 C 13.94 29.7 12.5 28.27 12.5 26.5 C 12.5 24.74 13.94 23.3 15.7 23.3 Z M 15.7 24.1 C 14.38 24.1 13.3 25.18 13.3 26.5 C 13.3 27.83 14.38 28.9 15.7 28.91 C 17.03 28.9 18.1 27.83 18.1 26.5 C 18.1 25.18 17.03 24.1 15.7 24.1 Z" fill="#cd2264" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe flex-start; width: 388px; height: 1px; padding-top: 17px; margin-left: 32px;"><div data-drawio-colors="color: #CD2264; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(205, 34, 100); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><b><font style="font-size: 15px;">LMS APP</font></b></div></div></div></foreignObject><text x="32" y="29" fill="#CD2264" font-family="Helvetica" font-size="12px">LMS APP</text></switch></g><path d="M 375.5 277.08 L 369.32 280.24 L 351.18 275.8 L 351.18 289.4 L 337.26 296.5 L 323.23 289.4 L 323.23 275.8 L 305.16 280.24 L 299 277.1 L 299 222.8 L 305.16 219.65 L 323.23 224.01 L 323.23 210.6 L 337.26 203.5 L 351.18 210.6 L 351.18 224.01 L 369.32 219.63 L 375.5 222.8 Z" fill="#e05243" stroke="none" pointer-events="all"/><path d="M 305.16 219.65 L 305.16 280.24 L 299 277.1 L 299 222.8 Z M 337.26 226.98 L 323.23 230.47 L 323.23 210.6 L 337.26 203.5 Z M 337.26 261.73 L 323.23 259.93 L 323.23 240.06 L 337.26 238.25 Z M 337.26 296.5 L 323.23 289.4 L 323.23 269.53 L 337.26 273.02 Z M 369.32 280.24 L 351.18 275.8 L 351.18 269.45 L 337.26 266.92 L 337.26 261.73 L 351.18 259.93 L 351.18 240.06 L 337.26 238.25 L 337.26 233.06 L 351.18 230.47 L 351.18 224.01 L 369.32 219.63 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><path d="M 337.26 233.06 L 323.23 230.47 L 337.26 226.98 L 351.18 230.47 Z" fill-opacity="0.5" fill="#000000" stroke="none" pointer-events="all"/><path d="M 351.18 269.6 L 337.26 273.02 L 323.23 269.53 L 337.26 266.92 Z" fill-opacity="0.5" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 304px; margin-left: 337px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b><font style="font-size: 13px;">Admin APP</font></b></div></div></div></foreignObject><text x="337" y="316" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Admin APP</text></switch></g><path d="M 375.5 433.58 L 369.32 436.74 L 351.18 432.3 L 351.18 445.9 L 337.26 453 L 323.23 445.9 L 323.23 432.3 L 305.16 436.74 L 299 433.6 L 299 379.3 L 305.16 376.15 L 323.23 380.51 L 323.23 367.1 L 337.26 360 L 351.18 367.1 L 351.18 380.51 L 369.32 376.13 L 375.5 379.3 Z" fill="#e05243" stroke="none" pointer-events="all"/><path d="M 305.16 376.15 L 305.16 436.74 L 299 433.6 L 299 379.3 Z M 337.26 383.48 L 323.23 386.97 L 323.23 367.1 L 337.26 360 Z M 337.26 418.23 L 323.23 416.43 L 323.23 396.56 L 337.26 394.75 Z M 337.26 453 L 323.23 445.9 L 323.23 426.03 L 337.26 429.52 Z M 369.32 436.74 L 351.18 432.3 L 351.18 425.95 L 337.26 423.42 L 337.26 418.23 L 351.18 416.43 L 351.18 396.56 L 337.26 394.75 L 337.26 389.56 L 351.18 386.97 L 351.18 380.51 L 369.32 376.13 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><path d="M 337.26 389.56 L 323.23 386.97 L 337.26 383.48 L 351.18 386.97 Z" fill-opacity="0.5" fill="#000000" stroke="none" pointer-events="all"/><path d="M 351.18 426.1 L 337.26 429.52 L 323.23 426.03 L 337.26 423.42 Z" fill-opacity="0.5" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 460px; margin-left: 337px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b><font style="font-size: 13px;">Learner APP</font></b></div></div></div></foreignObject><text x="337" y="472" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Learner APP</text></switch></g><path d="M 126.5 443.58 L 120.32 446.74 L 102.18 442.3 L 102.18 455.9 L 88.26 463 L 74.23 455.9 L 74.23 442.3 L 56.16 446.74 L 50 443.6 L 50 389.3 L 56.16 386.15 L 74.23 390.51 L 74.23 377.1 L 88.26 370 L 102.18 377.1 L 102.18 390.51 L 120.32 386.13 L 126.5 389.3 Z" fill="#e05243" stroke="none" pointer-events="all"/><path d="M 56.16 386.15 L 56.16 446.74 L 50 443.6 L 50 389.3 Z M 88.26 393.48 L 74.23 396.97 L 74.23 377.1 L 88.26 370 Z M 88.26 428.23 L 74.23 426.43 L 74.23 406.56 L 88.26 404.75 Z M 88.26 463 L 74.23 455.9 L 74.23 436.03 L 88.26 439.52 Z M 120.32 446.74 L 102.18 442.3 L 102.18 435.95 L 88.26 433.42 L 88.26 428.23 L 102.18 426.43 L 102.18 406.56 L 88.26 404.75 L 88.26 399.56 L 102.18 396.97 L 102.18 390.51 L 120.32 386.13 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><path d="M 88.26 399.56 L 74.23 396.97 L 88.26 393.48 L 102.18 396.97 Z" fill-opacity="0.5" fill="#000000" stroke="none" pointer-events="all"/><path d="M 102.18 436.1 L 88.26 439.52 L 74.23 436.03 L 88.26 433.42 Z" fill-opacity="0.5" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 88px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b><font style="font-size: 13px;">APP Assets<br /><br /></font></b></div></div></div></foreignObject><text x="88" y="482" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">APP Assets...</text></switch></g><path d="M 226.5 250 L 250 250 L 292.63 250" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 297.88 250 L 290.88 253.5 L 292.63 250 L 290.88 246.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 188.3 303 L 188.3 420 L 292.63 420" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 297.88 420 L 290.88 423.5 L 292.63 420 L 290.88 416.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 188.3 303 L 188.3 420 L 132.87 420" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 127.62 420 L 134.62 416.5 L 132.87 420 L 134.62 423.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 226.5 248.55 L 209.83 249.59 L 199.65 248.52 L 199.65 246.31 L 188.3 247.44 L 176.76 246.32 L 176.76 248.53 L 166.93 249.57 L 150 248.56 L 150 229.33 L 159.2 224.68 L 169.83 227.48 L 169.83 219.32 L 188.3 210 L 206.56 219.22 L 206.56 227.5 L 217.39 224.67 L 226.5 229.31 Z M 188.3 303 L 169.83 293.68 L 169.83 285.49 L 159.2 288.33 L 150 283.67 L 150 264.45 L 166.76 263.43 L 176.76 264.41 L 176.76 266.68 L 188.3 265.56 L 199.65 266.68 L 199.65 264.41 L 209.83 263.43 L 226.5 264.48 L 226.5 283.76 L 217.39 288.33 L 206.56 285.46 L 206.56 293.75 Z" fill="#f58536" stroke="none" pointer-events="all"/><path d="M 199.65 248.52 L 199.65 246.31 L 206.56 245.63 L 206.56 227.5 L 217.39 224.67 L 217.39 247.2 Z M 169.83 245.64 L 169.83 219.32 L 188.3 210 L 188.3 242.95 Z M 159.2 247.2 L 150 248.56 L 150 229.33 L 159.2 224.68 Z M 159.2 288.33 L 150 283.67 L 150 264.45 L 159.2 265.8 Z M 188.3 303 L 169.83 293.68 L 169.83 267.36 L 188.3 270.05 Z M 217.39 288.33 L 206.56 285.46 L 206.56 267.37 L 199.65 266.68 L 199.65 264.41 L 217.39 265.82 Z" fill-opacity="0.3" fill="#000000" stroke="none" pointer-events="all"/><path d="M 199.65 264.41 L 209.83 263.43 L 226.5 264.48 L 217.39 265.82 Z M 169.83 267.36 L 188.3 265.56 L 206.56 267.37 L 188.3 270.05 Z M 159.2 265.8 L 150 264.45 L 166.76 263.43 L 176.76 264.41 Z" fill-opacity="0.3" fill="#ffffff" stroke="none" pointer-events="all"/><path d="M 176.76 248.53 L 166.93 249.57 L 150 248.56 L 159.2 247.2 Z M 206.56 245.63 L 188.3 247.44 L 169.83 245.64 L 188.3 242.95 Z M 226.5 248.55 L 209.83 249.59 L 199.65 248.52 L 217.39 247.2 Z" fill-opacity="0.5" fill="#000000" stroke="none" pointer-events="all"/><path d="M 88 250 L 119 250 L 119 256.5 L 143.63 256.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 148.88 256.5 L 141.88 260 L 143.63 256.5 L 141.88 253 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 10 211 L 88 211 L 88 289 L 10 289 Z" fill="url(#mx-gradient-945df2-1-5a30b5-1-s-0)" stroke="none" pointer-events="all"/><path d="M 58.66 250.82 C 59.49 251.58 59.9 252.6 59.9 253.88 C 59.9 255.27 59.4 256.38 58.4 257.21 C 57.4 258.04 56.05 258.46 54.37 258.46 C 53.03 258.46 51.71 258.17 50.41 257.6 L 50.41 255.9 C 51.95 256.41 53.27 256.67 54.37 256.67 C 55.44 256.67 56.27 256.43 56.86 255.94 C 57.44 255.46 57.73 254.77 57.73 253.88 C 57.73 252.16 56.64 251.3 54.47 251.3 C 53.79 251.3 53.12 251.34 52.45 251.41 L 52.45 250.01 L 56.92 245.13 L 50.62 245.13 L 50.62 243.38 L 59.28 243.38 L 59.28 245.07 L 54.9 249.71 C 54.97 249.69 55.04 249.69 55.11 249.69 L 55.32 249.69 C 56.73 249.69 57.84 250.07 58.66 250.82 M 46.3 250.45 C 47.17 251.26 47.6 252.37 47.6 253.79 C 47.6 255.18 47.1 256.31 46.09 257.17 C 45.08 258.03 43.76 258.46 42.11 258.46 C 40.66 258.46 39.31 258.17 38.05 257.6 L 38.05 255.9 C 39.62 256.41 40.97 256.67 42.09 256.67 C 43.17 256.67 43.99 256.42 44.57 255.93 C 45.14 255.44 45.43 254.74 45.43 253.81 C 45.43 252.81 45.16 252.08 44.62 251.62 C 44.08 251.17 43.2 250.94 41.98 250.94 C 41.1 250.94 40 251.01 38.68 251.15 L 38.68 249.75 L 39.09 243.38 L 46.86 243.38 L 46.86 245.13 L 40.88 245.13 L 40.6 249.45 C 41.38 249.31 42.08 249.24 42.7 249.24 C 44.24 249.24 45.44 249.64 46.3 250.45 M 62.29 266.05 C 56.9 267.02 52.17 269.22 49 271.01 C 45.83 269.22 41.1 267.02 35.71 266.05 C 34.19 265.78 26.62 264.2 26.62 259.83 C 26.62 257.8 27.34 256.45 28.74 254.05 C 30.4 251.18 32.48 247.61 32.48 242.46 C 32.48 238.78 31.51 235.25 29.61 231.96 C 29.83 231.69 30.06 231.41 30.29 231.13 C 33.11 232.54 36.04 233.25 39.03 233.25 C 42.67 233.25 46.03 232.29 49 230.4 C 51.97 232.29 55.32 233.25 58.97 233.25 C 61.95 233.25 64.89 232.54 67.71 231.13 C 67.94 231.41 68.17 231.69 68.39 231.96 C 66.49 235.25 65.52 238.78 65.52 242.46 C 65.52 247.61 67.6 251.18 69.26 254.06 C 70.65 256.45 71.38 257.8 71.38 259.83 C 71.38 264.2 63.81 265.78 62.29 266.05 M 67.75 242.46 C 67.75 238.95 68.74 235.58 70.69 232.46 C 70.94 232.05 70.91 231.53 70.61 231.16 C 70.04 230.47 69.45 229.74 68.86 229.02 C 68.53 228.6 67.94 228.48 67.46 228.74 C 64.72 230.26 61.87 231.02 58.97 231.02 C 55.48 231.02 52.42 230.09 49.63 228.15 C 49.25 227.89 48.75 227.89 48.37 228.15 C 45.58 230.09 42.52 231.02 39.03 231.02 C 36.13 231.02 33.27 230.26 30.54 228.74 C 30.06 228.48 29.47 228.6 29.13 229.02 C 28.55 229.74 27.96 230.47 27.39 231.16 C 27.09 231.53 27.06 232.05 27.31 232.46 C 29.26 235.58 30.25 238.95 30.25 242.46 C 30.25 247.01 28.35 250.29 26.81 252.94 C 25.31 255.52 24.39 257.24 24.39 259.83 C 24.39 265.82 32.75 267.78 35.31 268.24 C 40.72 269.21 45.43 271.5 48.44 273.25 C 48.61 273.36 48.81 273.41 49 273.41 C 49.19 273.41 49.39 273.36 49.56 273.25 C 52.57 271.5 57.28 269.21 62.69 268.24 C 65.25 267.78 73.6 265.82 73.6 259.83 C 73.6 257.24 72.69 255.52 71.19 252.94 C 69.65 250.29 67.75 247.01 67.75 242.46 M 63.45 272.48 C 56.63 273.71 50.92 277.37 49 278.71 C 47.08 277.37 41.37 273.71 34.55 272.48 C 21.13 270.07 20.09 262.18 20.09 259.83 C 20.09 255.94 21.62 253.32 23.09 250.78 C 24.5 248.36 25.95 245.85 25.95 242.46 C 25.95 237.1 22.86 233.31 21.33 231.77 C 22.93 229.82 26.99 224.88 28.99 222.3 C 32.02 225.16 35.54 226.72 39.03 226.72 C 42.89 226.72 46.09 225.15 49 221.79 C 51.91 225.15 55.1 226.72 58.97 226.72 C 62.45 226.72 65.98 225.16 69.01 222.3 C 71.01 224.88 75.06 229.82 76.67 231.77 C 75.14 233.31 72.05 237.1 72.05 242.46 C 72.05 245.85 73.5 248.36 74.91 250.78 C 76.38 253.32 77.91 255.94 77.91 259.83 C 77.91 262.18 76.86 270.07 63.45 272.48 M 76.83 249.66 C 75.46 247.29 74.27 245.25 74.27 242.46 C 74.27 236.58 78.85 232.78 78.89 232.74 C 79.12 232.55 79.27 232.28 79.3 231.99 C 79.33 231.69 79.24 231.4 79.05 231.17 C 78.98 231.08 71.69 222.24 70.02 219.95 C 69.82 219.69 69.52 219.52 69.19 219.5 C 68.86 219.47 68.54 219.61 68.31 219.85 C 65.51 222.84 62.19 224.5 58.97 224.5 C 55.41 224.5 52.61 222.91 49.88 219.35 C 49.46 218.8 48.54 218.8 48.12 219.35 C 45.39 222.91 42.59 224.5 39.03 224.5 C 35.81 224.5 32.49 222.84 29.69 219.85 C 29.46 219.61 29.14 219.47 28.81 219.5 C 28.48 219.52 28.17 219.69 27.98 219.95 C 26.31 222.24 19.02 231.08 18.95 231.17 C 18.76 231.4 18.67 231.69 18.7 231.99 C 18.73 232.28 18.87 232.55 19.1 232.74 C 19.15 232.78 23.73 236.58 23.73 242.46 C 23.73 245.25 22.54 247.29 21.17 249.66 C 19.62 252.33 17.87 255.35 17.87 259.83 C 17.87 267.29 23.96 272.84 34.16 274.67 C 41.84 276.05 48.26 280.92 48.32 280.97 C 48.52 281.12 48.76 281.2 49 281.2 C 49.24 281.2 49.48 281.12 49.68 280.97 C 49.74 280.92 56.14 276.05 63.84 274.67 C 74.04 272.84 80.13 267.29 80.13 259.83 C 80.13 255.35 78.38 252.33 76.83 249.66" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 296px; margin-left: 49px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b>Route/DNS</b></div></div></div></foreignObject><text x="49" y="308" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">Route/DNS</text></switch></g><path d="M 280 50 L 358 50 L 358 128 L 280 128 Z" fill="url(#mx-gradient-ff4f8b-1-bc1356-1-s-0)" stroke="none" pointer-events="all"/><path d="M 334.22 117.97 C 331.83 117.97 329.89 116.02 329.89 113.62 C 329.89 111.21 331.83 109.26 334.22 109.26 C 336.61 109.26 338.55 111.21 338.55 113.62 C 338.55 116.02 336.61 117.97 334.22 117.97 Z M 324.72 99.03 L 313.2 99.03 L 307.44 89 L 313.2 78.97 L 324.72 78.97 L 330.48 89 Z M 305.21 68.74 C 302.82 68.74 300.87 66.79 300.87 64.38 C 300.87 61.98 302.82 60.03 305.21 60.03 C 307.6 60.03 309.54 61.98 309.54 64.38 C 309.54 66.79 307.6 68.74 305.21 68.74 Z M 334.22 107.03 C 333.39 107.03 332.61 107.19 331.89 107.47 L 327.18 99.49 L 326.93 99.63 L 332.72 89.56 C 332.91 89.21 332.91 88.79 332.72 88.44 L 326.32 77.3 C 326.12 76.95 325.75 76.74 325.36 76.74 L 313.72 76.74 L 313.76 76.72 L 309.45 69.39 C 310.86 68.19 311.76 66.39 311.76 64.38 C 311.76 60.75 308.82 57.8 305.21 57.8 C 301.6 57.8 298.66 60.75 298.66 64.38 C 298.66 68.01 301.6 70.97 305.21 70.97 C 306.03 70.97 306.81 70.81 307.54 70.53 L 311.57 77.36 L 305.21 88.44 C 305.01 88.79 305.01 89.21 305.21 89.56 L 311.6 100.7 C 311.8 101.05 312.17 101.26 312.56 101.26 L 325.36 101.26 C 325.45 101.26 325.54 101.24 325.63 101.22 L 329.98 108.61 C 328.57 109.81 327.67 111.61 327.67 113.62 C 327.67 117.25 330.61 120.2 334.22 120.2 C 337.83 120.2 340.76 117.25 340.76 113.62 C 340.76 109.99 337.83 107.03 334.22 107.03 Z M 340.82 80.2 C 338.43 80.2 336.49 78.25 336.49 75.85 C 336.49 73.44 338.43 71.49 340.82 71.49 C 343.21 71.49 345.15 73.44 345.15 75.85 C 345.15 78.25 343.21 80.2 340.82 80.2 Z M 348.94 88.44 L 344.73 81.12 C 346.33 79.91 347.37 78 347.37 75.85 C 347.37 72.22 344.43 69.26 340.82 69.26 C 339.9 69.26 339.03 69.45 338.23 69.8 L 334.8 63.83 C 334.61 63.48 334.24 63.27 333.85 63.27 L 320.11 63.27 L 320.11 65.5 L 333.21 65.5 L 336.38 71.03 C 335.09 72.23 334.28 73.94 334.28 75.85 C 334.28 79.48 337.21 82.43 340.82 82.43 C 341.5 82.43 342.15 82.33 342.76 82.14 L 346.7 89 L 341.16 98.64 L 343.08 99.75 L 348.94 89.56 C 349.14 89.21 349.14 88.79 348.94 88.44 Z M 298.08 105.99 C 295.7 105.99 293.75 104.04 293.75 101.63 C 293.75 99.23 295.7 97.28 298.08 97.28 C 300.47 97.28 302.42 99.23 302.42 101.63 C 302.42 104.04 300.47 105.99 298.08 105.99 Z M 301.7 107.12 C 303.47 105.93 304.63 103.92 304.63 101.63 C 304.63 98 301.69 95.05 298.08 95.05 C 297.04 95.05 296.05 95.3 295.17 95.74 L 291.3 89 L 297.72 77.82 L 295.8 76.71 L 289.06 88.44 C 288.86 88.79 288.86 89.21 289.06 89.56 L 293.38 97.07 C 292.24 98.25 291.54 99.86 291.54 101.63 C 291.54 105.26 294.47 108.22 298.08 108.22 C 298.63 108.22 299.15 108.14 299.66 108.02 L 303.2 114.17 C 303.39 114.52 303.76 114.73 304.15 114.73 L 317.89 114.73 L 317.89 112.5 L 304.79 112.5 Z" fill="#ffffff" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 135px; margin-left: 319px;"><div data-drawio-colors="color: #232F3E; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(35, 47, 62); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b>EventBridge</b></div></div></div></foreignObject><text x="319" y="147" fill="#232F3E" font-family="Helvetica" font-size="12px" text-anchor="middle">EventBridge</text></switch></g><rect x="110" y="185" width="140" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 200px; margin-left: 180px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 14px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b>CloudFrond/CDN</b></div></div></div></foreignObject><text x="180" y="204" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="14px" text-anchor="middle">CloudFrond/CDN</text></switch></g><path d="M 624 120 L 624 85 L 625 85 L 625 56.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 625 51.12 L 628.5 58.12 L 625 56.37 L 621.5 58.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 624 248 L 624 303.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 624 308.88 L 620.5 301.88 L 624 303.63 L 627.5 301.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><image x="559.5" y="119.5" width="128" height="128" xlink:href="https://cdn2.iconfinder.com/data/icons/boxicons-logos/24/bxl-heroku-128.png" preserveAspectRatio="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 255px; margin-left: 624px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><b>LMS API</b></div></div></div></foreignObject><text x="624" y="268" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle">LMS API</text></switch></g><rect x="577.25" y="310" width="93.5" height="170" fill="none" stroke="none" pointer-events="all"/><path d="M 624.05 368.05 C 618.55 368.05 614.05 372.66 614.05 378.31 C 614.05 383.95 618.55 388.56 624.05 388.56 C 629.56 388.56 634.06 383.95 634.06 378.31 C 634.06 372.66 629.56 368.05 624.05 368.05 Z M 622.76 371.28 L 622.76 378.52 C 622.76 379 623 379.44 623.41 379.68 C 623.81 379.91 624.3 379.91 624.7 379.68 C 625.1 379.44 625.35 379 625.35 378.52 L 625.35 371.28 C 628.6 371.9 631.03 374.79 631.03 378.31 C 631.03 382.28 627.93 385.46 624.05 385.46 C 620.18 385.46 617.07 382.28 617.07 378.31 C 617.07 374.79 619.51 371.9 622.76 371.28 Z M 577.25 427.9 L 577.25 430.12 L 577.25 450.97 L 670.75 450.97 L 670.75 427.9 Z M 581.57 432.33 L 666.43 432.33 L 666.43 446.54 L 581.57 446.54 Z M 577.25 355.99 L 577.25 358.21 L 577.25 423.18 L 670.75 423.18 L 670.75 355.99 Z M 581.57 360.43 L 666.43 360.43 L 666.43 418.75 L 581.57 418.75 Z M 670.75 352.67 L 670.75 320.39 C 670.75 314.67 666.08 310 660.5 310 L 587.39 310 C 581.81 310 577.25 314.67 577.25 320.39 L 577.25 352.67 Z M 577.25 455.22 L 577.25 469.61 C 577.25 475.33 581.81 480 587.39 480 L 660.5 480 C 666.08 480 670.75 475.33 670.75 469.61 L 670.75 455.22 Z M 666.43 459.84 L 666.43 469.61 C 666.43 472.95 663.76 475.57 660.5 475.57 L 587.39 475.57 C 584.13 475.57 581.57 472.95 581.57 469.61 L 581.57 459.84 Z M 581.57 348.11 L 581.57 320.39 C 581.57 317.05 584.13 314.43 587.39 314.43 L 660.5 314.43 C 663.76 314.43 666.43 317.05 666.43 320.39 L 666.43 348.11 Z" fill="#005073" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 487px; margin-left: 624px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;"><b>Supabase</b></div></div></div></foreignObject><text x="624" y="500" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="13px" text-anchor="middle">Supabase</text></switch></g><path d="M 358 89 L 459 89 L 459 184 L 553.63 184" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 184 L 551.88 187.5 L 553.63 184 L 551.88 180.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 375.5 406.5 L 476.4 406.5 L 476.4 377.2 L 565.65 377.15" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 570.9 377.15 L 563.9 380.65 L 565.65 377.15 L 563.89 373.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><image x="599.5" y="-0.5" width="50" height="50" xlink:href="https://app.diagrams.net/img/lib/mscae/SendGrid_Accounts.svg"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 57px; margin-left: 625px;"><div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;"><b>Sendgrid Email</b></div></div></div></foreignObject><text x="625" y="69" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Sendgrid...</text></switch></g><path d="M 375.5 250 L 480 250 L 480 206.5 L 553.63 206.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 558.88 206.5 L 551.88 210 L 553.63 206.5 L 551.88 203 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 375.5 250 L 476.4 250 L 476.4 376.1 L 570.41 376.13" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 575.66 376.13 L 568.66 379.63 L 570.41 376.13 L 568.67 372.63 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>