import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-ui-start-rating',
  imports: [MatIconModule],
  templateUrl: './start-rating.component.html',
  styleUrls: ['./start-rating.component.scss'],
})
export class StartRatingComponent implements OnChanges {
  starList = [false, false, false, false, false];

  @Input() readOnly = false;
  @Input() size: 'sm' | 'md' | 'lg' | 'xl' = 'lg';
  @Input() rate = 0;
  @Output() rating = new EventEmitter<number>();

  get stars() {
    return this.starList.sort((a, b) => (a > b ? -1 : 1));
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.rate) {
      this.starList = this.starList.map((v, i) => i < this.rate);
    }
  }

  setStar(rate: number) {
    if (!this.readOnly) {
      for (let i = 0; i <= 4; i++) {
        if (i <= rate) {
          this.starList[i] = true;
        } else {
          this.starList[i] = false;
        }
      }
      this.rating.emit(rate + 1);
    }
  }
}
