import { Component, Input, input, OnInit } from '@angular/core';
import { TraceStatus } from '@lms/core';

@Component({
  selector: 'app-progress',
  template: `
    <!-- 
    [class.bg-lot-gray]="status === 'PAST'"
    [class.bg-blue-200]="status === 'DUE'"
    [class.bg-orange-200]="status === 'IN_PROGRESS'"
    [class.bg-green-200]="status === 'COMPLETED'"
     -->
    <div class="w-full rounded-full h-2 bg-lot-gray">
      <div
        class="h-2 rounded-full"
        [style.width.%]="progress()"
        [class.bg-lot-danger]="status === 'PAST'"
        [class.bg-lot-warning]="status === 'DUE'"
        [class.bg-lot-blue]="status === 'IN_PROGRESS'"
        [class.bg-lot-ai]="status === 'COMPLETED'"
      ></div>
    </div>
  `,
})
export class ProgressComponent {
  @Input() status: TraceStatus = TraceStatus.IN_PROGRESS;
  progress = input<number>(0);
}
