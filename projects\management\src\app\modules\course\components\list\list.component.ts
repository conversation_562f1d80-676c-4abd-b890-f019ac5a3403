import { Component, inject, Input } from '@angular/core';
import { MatPaginatorModule } from '@angular/material/paginator';
import { Course } from '@lms/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-course-list',
  imports: [MatPaginatorModule],
  templateUrl: 'list.component.html',
})
export class ListComponent {
  router = inject(Router);
  @Input() data: Course[] = [];
  @Input() total = 0;

  goTo(item: Course) {
    this.router.navigate(['/lms/courses/view', item.id]);
    // if (item.type === 'COURSE') {
    //   this.router.navigate(['/training', item.id]);
    // }
    // if (item.type === 'LEARNINGPATH') {
    //   this.router.navigate(['/learning-path', item.id]);
    // }
    // if (item.type === 'LED') {
    //   this.router.navigate(['/instructor-led', item.id]);
    // }
  }
}
