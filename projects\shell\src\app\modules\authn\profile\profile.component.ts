import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AuthService,
  markControlsDirty,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { ValidationTextComponent } from '@lms/shared';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-profile',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    ValidationTextComponent,
    NgxMaskDirective,
  ],
  templateUrl: './profile.component.html',
})
export class ChangeProfileComponent implements OnInit {
  private authService = inject(AuthService);
  service = inject(UsersCoreService);

  @Input() userId: string;
  @Output() redirect = new EventEmitter<number>();

  form = new FormGroup({
    phone: new FormControl('', Validators.required),
    firstname: new FormControl('', Validators.required),
    lastname: new FormControl('', Validators.required),
    title: new FormControl('', Validators.required),
  });

  message?: string;

  ngOnInit(): void {
    this.authService.authChanges((event, session) => {
      if (session?.user) {
        this.userId = session?.user?.id!;
      }
    });
  }
  async onSubmit() {
    this.message = undefined;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    this.authService.isLoading = true;
    const formData = this.form.getRawValue();

    const session = await this.authService.state.supabase.auth.getSession();

    const id =
      this.userId ?? session.data.session?.user?.id ?? this.service.user.id;

    if (!id) {
      this.message = 'Invalid User Id.';
      return;
    }

    const res = await this.service.update([
      {
        id,
        phone: formData.phone?.replace(/\D/g, ''),
        title: formData.title,
        firstname: formData.firstname,
        lastname: formData.lastname,
      } as UserItem & any,
    ]);

    this.authService.isLoading = false;
    if (res.error) {
      this.message = res.error;
      return;
    }
    this.redirect.emit(1);
  }
}
