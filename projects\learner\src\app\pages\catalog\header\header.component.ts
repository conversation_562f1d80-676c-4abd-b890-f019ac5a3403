import { Component, output, signal } from '@angular/core';
import { FloatingComponent } from '@lms/shared';

@Component({
  selector: 'lms-catalog-header',
  templateUrl: './header.component.html',
  imports: [FloatingComponent],
})
export class CatalogHeaderComponent {
  sortBy = [
    { id: 'titleASC', name: 'A to Z' },
    { id: 'titleDESC', name: 'Z to A' },
    { id: 'createdDateASC', name: 'Oldest' },
    { id: 'createdDateDESC', name: 'Latest' },
  ];
  viewAs = [
    { id: 'grid', name: 'Grid' },
    { id: 'list', name: 'List' },
    { id: 'detail', name: 'Detailed List' },
    { id: 'calendar', name: 'Calendar' },
  ];
  search = output<string>();
  sort = output<string>();
  view = output<string>();
}
