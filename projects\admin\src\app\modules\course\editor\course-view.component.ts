import { Component, inject, input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NgTemplateOutlet } from '@angular/common';
import { Course, CourseCoreService, Lesson, Module, ToastMessageType } from '@lms/core';
import { DialogComponent, ResourceHeaderComponent } from '@lms/shared';
import { CourseBuilderComponent } from './builder/builder.component';
import { CourseSettingsComponent } from './settings/settings.component';
import { LessonBuilderComponent } from './lesson-builder/content.component';
import { Router } from '@angular/router';
import { CourseAssignmentComponent } from './assignment/assign-team.component';
import { firstValueFrom } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';

@Component({
  selector: 'app-course-view',
  imports: [
    ResourceHeaderComponent,
    CourseBuilderComponent,
    CourseSettingsComponent,
    // LessonBuilderComponent,
    NgTemplateOutlet,
    CourseAssignmentComponent,
    MatProgressBarModule,
  ],
  templateUrl: 'course-view.component.html',
})
export class CourseViewComponent implements OnInit, OnDestroy {
  readonly router = inject(Router);
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  id = input<string>('');

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'Course Structure',
    },
    {
      id: 2,
      name: 'Course Settings',
    },
    {
      id: 3,
      name: 'Assign Audience',
    },
  ];

  get view() {
    return this.service.state.viewType();
  }
  get isSettings() {
    return this.service.viewType() === 'SETTINGS';
  }

  content?: { module: Module; lesson: Lesson };

  source = this.service.courseSource;
  isLoading = false;
  isPreview = false;
  windowRef: any;
  error?: string;

  ngOnInit(): void {
    this.service.courseId.set(this.id());
  }

  back() {
    this.router.navigate(['/lms/courses']);
  }

  preview(id: string) {
    if (this.tab === 2) {
      this.service.enrollTrigger.next({
        type: 'SETTINGS',
        course: this.source.value()!,
      });
      return;
    }
    if (this.tab === 3) {
      this.service.enrollTrigger.next({
        type: this.view,
        course: this.source.value()!,
      });
      return;
    }

    this.isPreview = true;
    const query = new URLSearchParams({
      id,
      source: 'ADMIN',
    });

    this.windowRef = window.open(
      `go-training?${query.toString()}`,
      'LearnOrTeach',
      'popup'
    );
    this.windowRef?.focus();
  }

  onView(item: { module: Module; lesson: Lesson }) {
    this.content = item;
    // this.service.state.viewType.set('LESSON');
    this.router.navigate(['/lms/courses/lesson-builder', item.lesson.id]);
  }

  setTab(tab: number) {
    this.tab = tab;
    this.service.viewType.set('COURSE');
    if (tab === 2) {
      this.service.viewType.set('SETTINGS');
    }
  }
  exit() {
    this.service.state.viewType.set('COURSE');
    this.windowRef?.close();
    this.isPreview = false;
  }
  ngOnDestroy(): void {
    this.service.state.viewType.set('COURSE');
    this.exit();
  }

  async delete(course: Course) {
    const res = await firstValueFrom(
      this.dialog
        .open(DialogComponent, {
          width: '450px',
          data: {
            type: 'DELETE',
            message: `You're about to delete ${course.name} course. All modules and lessons associated to it will be deleted.`,
            title: 'Are you sure to remove this Course?',
          },
        })
        .afterClosed()
    );

    if (res) {
      this.isLoading = true;
      const result = await this.service.delete(course.id);
      this.isLoading = false;
      if (result.error) {
        this.error = result.error;
        this.service.state.openToast({
          title: 'Delete Request Failed',
          message: 'Failed: ' + result.error,
          type: ToastMessageType.ERROR,
        });
      }
      if (result.data) {
        this.service.state.openToast({
          title: 'Delete Request Successful',
          message: 'Course deleted successfully',
          type: ToastMessageType.SUCCESS,
        });
        this.router.navigate(['/lms/courses']);
        this.service.coursesSource.reload();
      }
    }
  }
}
