export class Environment {
  homePageUrl: string;
  playerUrl: string;
  learnerAppUrl: string;
  lmsCoreApi: string;
  stripePublishableKey: string;
  graphCMSApi: string;
  graphCMSToken: string;
  apikey: string;
  server: string;
  audienceId: string;
  supabaseUrl: string;
  supabaseKey: string;
  emailTemplate: { [key: string]: string };
  organization: { [key: string]: string };
  cloudApi: { [key: string]: string };
  apiEnpointConfig: { [key: string]: string };
}
