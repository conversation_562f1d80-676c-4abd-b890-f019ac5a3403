<div class="flex flex-col gap-5 px-10 py-10">
  @if (view === 'NONE') {
  <h1 class="text-2xl font-semibold">Add New Users</h1>
  <div class="grid grid-cols-2 gap-8 h-52">
    <a
      href="javascript:void(0)"
      (click)="addNewUser()"
      class="flex flex-col justify-center items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
    >
      <i class="fa-solid fa-user text-lot-blue"></i>
      <span class="text-lot-dark font-semibold">Add Users manually</span>
    </a>
    <a
      href="javascript:void(0)"
      (click)="view = 'UPLOAD'"
      class="flex flex-col justify-center items-center gap-2 border border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20"
    >
      <i class="fa-solid fa-file-excel text-lot-blue"></i>
      <span class="text-lot-dark font-semibold">Bulk upload via EXCEL</span>
    </a>
  </div>

  } @if (view !== 'NONE') {
  <div class="flex items-center justify-between">
    <h1 class="text-2xl font-semibold">Excel Upload</h1>
    @if (view !== 'SUBMIT') {
    <a
      href="template/Users_Template.xlsx"
      target="_blank"
      rel="noopener noreferrer"
      class="flex items-center gap-2 text-lot-blue underline italic"
    >
      <i class="fa-solid fa-download"></i>
      <span>Download EXCEL Template</span>
    </a>
    }
  </div>
  @if (error) {
  <p class="text-lot-danger mb-4 text-center">{{ error }}</p>
  } 
  @if (view === 'UPLOAD') {
  <ng-container *ngTemplateOutlet="uploader" />
  } @if (view === 'VALIDATION') {
  <ng-container *ngTemplateOutlet="validation" />
  } @if (view === 'SUBMIT') {
  <ng-container *ngTemplateOutlet="submit" />
  } }
</div>

<ng-template #uploader>
  <div class="flex flex-col gap-5">
    <a
      href="javascript:void(0)"
      class="relative flex flex-col justify-center items-center gap-2 border-2 border-dashed border-lot-blue p-10 rounded-xl hover:bg-lot-blue/20 h-72"
    >
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      } @if (!isLoading) {
      <i class="fa-solid fa-arrow-up-from-bracket text-lot-blue text-4xl"></i>
      <span class="text-lot-dark font-medium text-center">
        Fill the EXCEL with your user information and drop it here. <br />
        Or click here to upload the EXCEL.
      </span>
      <input
        type="file"
        accept=".xlsx"
        (change)="onFileChange($event)"
        class="absolute inset-0 opacity-0 cursor-pointer"
      />
      }
    </a>
  </div>
</ng-template>
<ng-template #validation>
  <div class="flex flex-col gap-5 h-full max-h-96 min-h-72 overflow-y-auto">
    <table class="min-w-full divide-y divide-gray-300 h-full border">
      <thead>
        <tr class="divide-x divide-gray-200">
          <th
            scope="col"
            class="w-1/5 p-3 text-left text-sm font-normal italic text-lot-dark-gray whitespace-nowrap"
          >
            Row Number
          </th>
          <th
            scope="col"
            class="w-full p-3 text-left text-sm font-normal italic text-lot-dark-gray whitespace-nowrap"
          >
            Validation Error
          </th>
        </tr>
      </thead>
      <tbody class="bg-white">
        @for (item of dataErrors; track $index;) {
        <tr class="divide-x divide-gray-200 even:bg-gray-50">
          <td
            class="py-1 px-3 text-base font-medium whitespace-nowrap text-lot-dark"
          >
            {{ item.row }}
          </td>
          <td
            class="py-1 px-3 text-sm font-medium whitespace-nowrap text-lot-danger"
          >
            {{ item.error }}
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>
</ng-template>

<ng-template #submit>
  <div class="flex flex-col gap-5">
    <div
      class="relative flex flex-col justify-center items-center gap-2 border-2 border-dashed border-lot-blue p-10 rounded-xl h-72"
    >
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      <p class="text-lot-dark font-medium text-center">
        <b>Please be patient while we're processing your data</b>
      </p>
      } @if (!isLoading) {
      <i class="fa-solid fa-upload text-lot-ai text-4xl"></i>
      <p class="text-lot-dark font-medium text-center">
        Uploaded Data Ready for submission <br />
        <b>Please be patient as we process your user data</b>
      </p>
      <button
        type="button"
        (click)="onSubmit()"
        class="button-primary py-2 w-fit"
      >
        Submit Now
        <i class="fa-solid fa-circle-check text-lot-ai ml-2"></i>
      </button>
      }
    </div>
  </div>
</ng-template>
