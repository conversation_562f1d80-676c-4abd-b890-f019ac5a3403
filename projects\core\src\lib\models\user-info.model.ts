import { Common } from './common.model';
import { BillingPlan } from './billing.model';
import { ITeamGroupItem } from './team.model';

export enum AccountStatus {
  SUPER = 'super',
  ADMINISTRATOR = 'admin',
  OWNER = 'owner',
  MANAGER = 'manager',
  LEARNER = 'learner',
  NONE = 'none',
}

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  DEACTIVATED = 'DEACTIVATED',
}

export const userStatus = [
  {
    label: 'Active',
    value: true,
  },
  {
    label: 'Inactive',
    value: false,
  },
];

export type UserItem = {
  id: string;
  username: string;
  email: string;
  role: AccountStatus;
  phone?: string;
  title?: string;
  organization?: Organization;
  firstname?: string;
  lastname?: string;
  avatar?: string;
  teams?: ITeamGroupItem[];
  groups?: ITeamGroupItem[];
  self?: boolean;
  last_sign_in_at?: string;
} & Common;

export const defaultUser = {
  id: '',
  role: AccountStatus.LEARNER,
} as UserItem;

export interface Organization extends Common {
  name: string;
  description: string;
  logo: string;
  phone: string;
  email: string;
  contact?: {
    name: string;
    phone: string;
    email: string;
    title: string;
  };
  about?: Record<string, any>;
  configuration?: Record<string, any>;
  seats: number;
}

export type OrgSubscription = Common & {
  organization: Organization;
  plan: BillingPlan;
  expired_at: string;
  paymentId: string;
  seat_max: number;
  used_seat: number;
  current: number;
  status: SubscriptionStatus;
  purchaseAmount: number;
  addOns?: BillingPlan[];
};

export const defaultTrial = {
  id: 'bf9d30af-43d3-430e-a432-0430cf97850a',
  created_at: '2025-03-26T01:25:06.697002+00:00',
  organization: '06765b86-01e5-11f0-ac02-b793649000e0',
  expired_at: '02:03:04+00',
  seat_max: 3,
  status: 'ACTIVE',
  purchaseAmount: 0,
  addOns: null,
  plan: {
    id: '0b51f763-a98e-4177-9c07-f0f07aa3acb6',
    icon: 'fa-solid fa-building',
    name: 'TRIAL',
    type: 'PLAN',
    price: 5,
    status: 'ACTIVE',
    countMax: 3,
    countMin: 3,
    frequency: 'NONE',
    created_at: '2025-03-26T00:44:15.46557+00:00',
    description: 'Billed Annually',
    pricingNote: 'per active user/month',
  },
} as OrgSubscription & any;
