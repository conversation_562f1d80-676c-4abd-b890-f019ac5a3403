import {
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  Course,
  CourseCoreService,
  Lesson,
  markControlsDirty,
  Module,
  stripHtml,
} from '@lms/core';
import { QuillEditorComponent } from 'ngx-quill';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  MatDialog,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';

@Component({
  selector: 'app-course-builder',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatProgressBarModule,
  ],
  templateUrl: 'builder.component.html',
})
export class CourseBuilderComponent {
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);
  formRef = viewChild<TemplateRef<any>>('formTemplate');

  @Input({ required: true }) data: Course;
  @Output() view = new EventEmitter<{ module: Module; lesson: Lesson }>();

  isLoading = false;
  error?: string;
  type: 'MODULE' | 'LESSON' = 'MODULE';

  get modules() {
    return this.data.modules || [];
  }

  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    id: new FormControl(''),
  });
  module?: Module;
  dialogRef?: MatDialogRef<any>;

  get f() {
    return this.form.controls;
  }

  add(module?: Module, type: 'MODULE' | 'LESSON' = 'MODULE') {
    this.type = type;
    this.module = module;
    this.form.reset();
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  editModule(module: Module) {
    this.type = 'MODULE';
    this.form.patchValue({
      id: module.id,
      name: module.name,
    });
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  editLesson(lesson: Lesson) {
    this.type = 'LESSON';
    this.form.patchValue({
      id: lesson.id,
      name: lesson.name,
    });
    this.dialogRef = this.dialog.open(this.formRef()!, {
      width: '600px',
    });
  }

  goToLesson(module: Module, lesson: Lesson) {
    this.view.emit({ module, lesson });
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const payload = {
      name: this.form.value.name,
      // description: this.form.value.description,
    } as any;

    let res: {
      data: Lesson | Module | string;
      error: string | undefined;
    } = {
      data: '',
      error: undefined,
    };

    if (this.form.value.id) {
      payload['id'] = this.form.value.id;
      res =
        this.type === 'MODULE'
          ? await this.service.saveModule(payload)
          : await this.service.saveLesson(payload);
    } else {
      if (this.type === 'MODULE') {
        payload['course'] = this.data.id;
      } else {
        payload['module'] = this.module?.id;
      }

      res =
        this.type === 'MODULE'
          ? await this.service.addModule(payload)
          : await this.service.addLesson(payload);
    }
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      return;
    }
    this.dialogRef?.close();
    this.service.courseSource.reload();
  }
}
