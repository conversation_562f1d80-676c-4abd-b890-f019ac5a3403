import { Component, computed, inject, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { ProgressComponent, ResourceHeaderComponent } from '@lms/shared';
import {
  LearningInstructor,
  LearningService,
  LearningType,
  TraceStatus,
  TrainingService,
  UserTrackingItem,
} from '@lms/core';

import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

dayjs.extend(isSameOrBefore);

@Component({
  selector: 'lms-home',
  templateUrl: './home.component.html',
  imports: [
    ProgressComponent,
    DatePipe,
    NgTemplateOutlet,
    ResourceHeaderComponent,
  ],
})
export class HomeComponent implements OnInit, OnDestroy {
  router = inject(Router);
  service = inject(LearningService);
  trainingService = inject(TrainingService);

  myCourses = this.service.assignedSource;

  dueItems = computed(() => this.myCourses.value()?.dueItems || []);
  noDueItems = computed(() => this.myCourses.value()?.noDueItems || []);

  dueProgress = computed(() => {
    const items = this.dueItems();
    const completed = items.filter((x) => x.status === 'COMPLETED').length;
    return items.length ? Math.ceil((completed / items.length) * 100) : 0;
  });
  noDueProgress = computed(() => {
    const items = this.noDueItems();
    const completed = items.filter((x) => x.status === 'COMPLETED').length;
    return items.length ? Math.ceil((completed / items.length) * 100) : 0;
  });

  cover = 'assets/images/new/card-3.jpeg';
  defaultStatus = TraceStatus.IN_PROGRESS;
  suggestedCourses = [];

  async viewCourse(item: UserTrackingItem) {
    if (item.type === LearningType.COURSE) {
      if (!item.id) {
        await this.trainingService.addTracking(item.course, item);
      }
      this.router.navigate(['/myTraining/training', item.course.id]);
    }
    if (item.type === LearningType.LEARNINGPATH) {
      if (!item.id) {
        await this.trainingService.addTrackingCombo(item.item as any, item);
      }
      this.router.navigate(['/myTraining/learning-path', item.item.id]);
    }
    if (item.type === LearningType.INSTRUCTORLED) {
      if (!item.id) {
        await this.trainingService.addTrackingCombo(item.item as any, item);
      }
      this.router.navigate(['/myTraining/instructor-led', item.item.id]);
    }
  }

  goCatalog() {
    this.router.navigate(['/myTraining/course-library']);
  }

  ngOnInit(): void {
    this.service.homePageLoad.set(1);
  }
  ngOnDestroy(): void {
    this.service.homePageLoad.set(0);
  }
}
