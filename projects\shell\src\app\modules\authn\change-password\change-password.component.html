<a
  href="javascript:void(0)"
  routerLink="/sign-in"
  class="absolute top-5 left-5 text-lot-blue text-lg flex items-center gap-2"
>
  <span class="material-symbols-outlined"> arrow_back_ios_new </span>
  Back to Login
</a>
@if (message) {
<p class="text-sm font-semibold text-accent my-3 text-center">
  {{ message }}
</p>
} @if (view ==='PASSWORD') {
<div class="text-center my-2">
  <h1 class="font-bold text-center text-2xl text-lot-blue">
    First, let's set your password
  </h1>
</div>
<div class="flex flex-col gap-0 mt-8 w-[480px]">
  <app-password-form (data)="onSubmitPassword($event)" />
</div>
} @if (view ==='VERIFY') {
<app-profile [userId]="userId" (redirect)="onSubmitProfile($event)" />
}
