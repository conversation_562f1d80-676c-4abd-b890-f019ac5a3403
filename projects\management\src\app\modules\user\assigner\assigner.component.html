<!-- <mat-tab-group color="accent" mat-stretch-tabs="false" animationDuration="100ms" class="bg-white py-4">
  <mat-tab label="By Groups">
    <app-assign-group-v2
      [data]="audienceData.group ?? []"
      [groups]="(groups$ | async) ?? []"
      (save)="saveItem($event, 'GROUP')"
      (onAssignmentItemClicked)="updateItemAssignment($event)"
    ></app-assign-group-v2>
  </mat-tab>
  <mat-tab label="By Subgroups">
    <app-assign-subgroup-v2
      [data]="audienceData.subgroup ?? []"
      [subgroups]="items"
      [groups]="(groups$ | async) ?? []"
      (getItem)="getSubgroup($event)"
      (save)="saveItem($event, 'SUBGROUP')"
      (onAssignmentItemClicked)="updateItemAssignment($event)"
    ></app-assign-subgroup-v2>
  </mat-tab>
  <mat-tab label="By Users">
  </mat-tab>
</mat-tab-group> -->
