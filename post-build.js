const { cpSync, existsSync } = require("fs");

const distSourceDir = "dist/player/browser";
const distAdminDir = "dist/admin/browser";
const distLearnerDir = "dist/learner/browser";

async function main() {
  if (!existsSync(distSourceDir)) return;

  if (existsSync(distAdminDir)) {
    cpSync(distSourceDir, distAdminDir + "/course-player/browser", {
      recursive: true,
    });
  }
  if (existsSync(distLearnerDir)) {
    cpSync(distSourceDir, distLearnerDir + "/course-player/browser", {
      recursive: true,
    });
  }
}

main()
  .then(() => console.log("All right!"))
  .catch((err) => {
    console.error("Err", err);
    process.exit(1);
  });
