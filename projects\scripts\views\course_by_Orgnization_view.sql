DROP VIEW courses_view;

CREATE VIEW
  courses_view AS
SELECT
  S.organization,
  C.id,
  C.title,
  C.subtitle,
  C.description,
  C.length,
  C.objective,
  C.subject,
  C.status,
  C.active,
  C."isPublic",
  C.category,
  C."imageUrl",
  C."videoUrl",
  <PERSON><PERSON>price,
  C."readingType",
  <PERSON>."scheduleEndDate",
  C."difficultyLevel",
  C.author,
  C."created_by",
  C."created_at",
  C."createdName",
  C."updated_by",
  C."updated_at",
  C."updatedName",
  C."scheduleStartDate",
  C."minimunScorePercentage",
  C."preTestQuiz",
  C."postTestQuiz",
  C."scormCourseId",
  <PERSON>."scormVersion",
  C.language,
  C.runtime,
  CAT.name "categoryName"
FROM
  public.courses C
  INNER JOIN public.course_subscriptions S ON S.course = C.id
  LEFT JOIN public.categories CAT ON CAT.id = C.category
ORDER BY
  C.title;

CREATE VIEW
  catalog_view AS
SELECT
  S.organization,
  C.id,
  C.title,
  C.description,
  C.objective,
  C.subject,
  C.category,
  C."imageUrl",
  C."scormVersion",
  C.language,
  C.runtime,
  CAT.name "categoryName",
  LOWER(
    REGEXP_REPLACE (
      REGEXP_REPLACE (C.title, '[,.:-_]', ' ', 'g'),
      '\s+',
      '-',
      'g'
    )
  ) as slug
FROM
  public.courses C
  INNER JOIN public.course_subscriptions S ON S.course = C.id
  LEFT JOIN public.categories CAT ON CAT.id = C.category
ORDER BY
  C.title;