create table public.course_subscriptions (
  id            uuid default uuid_generate_v1() primary key not null,

  "organization" uuid references public.organizations(id) on delete cascade not null,
  "course" uuid references public.courses(id) on delete cascade not null,
  "isPaid" boolean,
  "isPrivate" boolean,
  "created_by" uuid references public.users not null,
  "created_at" timestamp with time zone default now(),
  "createdName" text,
  "updated_by" uuid references public.users,
  "updated_at" timestamp with time zone default now(),
  "updatedName" text
);



alter table public.users add column "permissions" jsonb default [];