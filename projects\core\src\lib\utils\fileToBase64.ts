export function fileToBase64(fileToLoad: any): Promise<any> {
  return new Promise((res, rej) => {
    const fileReader = new FileReader();

    fileReader.onload = function (fileLoadedEvent) {
      const srcData = fileLoadedEvent.target?.result; // <--- data: base64
      res(srcData);
    };
    fileReader.onerror = function (error) {
      rej(error);
    };
    fileReader.readAsDataURL(fileToLoad);
  });
}
