import { Component, Input, TemplateRef } from '@angular/core';
import {
  trigger,
  state,
  style,
  transition,
  animate,
} from '@angular/animations';
import { NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-expandable',
  imports: [NgTemplateOutlet],
  templateUrl: './expandable.component.html',
  animations: [
    trigger('expandCollapse', [
      state(
        'collapsed',
        style({
          height: '0',
          opacity: '0',
          overflow: 'hidden',
        })
      ),
      state(
        'expanded',
        style({
          height: '*',
          opacity: '1',
          overflow: 'visible',
        })
      ),
      transition('collapsed => expanded', animate('300ms ease-in')),
      transition('expanded => collapsed', animate('300ms ease-out')),
    ]),
  ],
})
export class ExpandableComponent {
  @Input() headerTemplate: TemplateRef<any>;
  @Input() isExpanded = false;

  onToggle(context: ExpandableComponent) {
    context.isExpanded = !context.isExpanded;
  }
}
