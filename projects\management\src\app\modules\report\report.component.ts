import { NgIf } from '@angular/common';
import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router, RouterOutlet } from '@angular/router';
import {
  AccountStatus,
  GlobalStateService,
  ReportService,
} from '@lms/core';
import { Subscription, debounceTime } from 'rxjs';

@Component({
  selector: 'app-report',
  imports: [RouterOutlet, NgIf],
  templateUrl: './report.component.html',
})
export class ReportComponent implements OnInit, OnDestroy {
  searchKey = new FormControl('');
  activePanel = new FormControl('');

  subscription = new Subscription();
  selectedType = 'none';
  minDate: Date;
  maxDate: Date;
  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });
  rangeString: string;
  courses = [];

  get isOwner() {
    return this.state.user().role === AccountStatus.OWNER;
  }

  @ViewChild('courseInput') courseInput: ElementRef<HTMLInputElement>;

  constructor(
    private router: Router,
    public state: GlobalStateService,
    private reportService: ReportService,
  ) {}

  async ngOnInit() {
    this.selectedType =
      (this.router.url.split('/').at(2) ?? 'none-eval').split('-').shift() ??
      'none';
  
    this.subscription.add(
      this.searchKey.valueChanges.pipe(debounceTime(500)).subscribe((res) => {
        void 0;
      })
    );
    this.rangeString = 'Filter by date range';
    this.range.valueChanges.subscribe((s) => {
      if (s.end && s.start) {
        this.rangeString =
          s.start.toDateString() + ' - ' + s.end.toDateString();
        this.router.navigate([
          '/reports',
          `${this.selectedType}-evaluation`,
          s.start.toISOString(),
          s.end.toISOString(),
        ]);
      } else this.rangeString = 'Filter by date range';
    });
  }

  displayFn(data: any): string {
    return data && data.title ? data.title : '';
  }

  async onSelectChange(evt: any) {
    this.selectedType = evt.target.value;
    if (evt.target.value !== 'none') {
      this.reportService.course = null;
      this.reportService.courseName$.next('');
      if (this.selectedType === 'course') {
        // this.courses = (await this.course.getByOrgId()).data;
        this.router.navigate(['/reports', `${evt.target.value}-evaluation`]);
      } else {
        this.router.navigate(['/reports', `${evt.target.value}-evaluation`]);
      }
    } else this.router.navigate(['/reports']);
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }

  selectCourse(item: string) {
    this.courseInput.nativeElement.value = item;
    // this.reportService.course =
    //   this.courses.find((x) => x.title === item) ?? null;
    this.reportService.courseName$.next(item);
  }

  openLearnerDetail(id: string) {
    // this.router.navigate(['/reports', 'course-evaluation', id]);
  }
}
