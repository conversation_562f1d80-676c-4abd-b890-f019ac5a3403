import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import { UserItem } from '@lms/core';
import { DatePipe } from '@angular/common';
import { MatPaginatorModule } from '@angular/material/paginator';

@Component({
  selector: 'app-sub-users',
  imports: [MatPaginatorModule, MatMenuModule, MatIconModule, DatePipe],
  templateUrl: './list.component.html',
})
export class UserSubComponent {
  @Input() data: UserItem[] = [];

  @Output() onItem = new EventEmitter<{
    type: 'edit' | 'delete' | 'view';
    data: UserItem;
  }>();

  action(type: 'edit' | 'delete' | 'view', item: UserItem) {
    this.onItem.emit({ type, data: item });
  }
}
