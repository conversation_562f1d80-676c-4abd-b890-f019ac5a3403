@if (view ==='SIGIN') {
<ng-container *ngTemplateOutlet="loginFlow" />
} @if (view ==='VERIFY') {
<ng-container *ngTemplateOutlet="confirmation" />
}
@if (view ==='PROFILE') {
<app-profile [userId]="user.id" (redirect)="onSubmitProfile($event)" />
}

<ng-template #loginFlow>
  <div class="text-center">
    <h1 class="text-center text-lot-blue">Welcome back!</h1>
  </div>
  <form [formGroup]="form" (ngSubmit)="onSubmit()" class="mt-6">
    <p *ngIf="message" class="text-sm font-semibold text-accent my-3 ml-6">
      {{ message }}
    </p>
    <div class="flex flex-col gap-5 mx-3">
      <div class="form-lot-input">
        <div class="field">
          <input
            id="email"
            type="email"
            formControlName="email"
            autocomplete="on"
            placeholder="<EMAIL>"
          />
          <label
            for="email"
            [class.error]="f['email'].invalid && f['email'].dirty"
          >
            Email
          </label>
        </div>
        <app-validation-text controlName="email" />
        <app-validation-text controlName="email" validator="email">
          Invalid Email.
        </app-validation-text>
      </div>

      <div class="form-lot-input">
        <div class="field">
          <input
            id="password"
            type="password"
            formControlName="password"
            autocomplete="on"
            placeholder="************"
          />
          <label
            for="password"
            [class.error]="f['password'].invalid && f['password'].dirty"
          >
            Password
          </label>
        </div>
        <app-validation-text controlName="password" />
      </div>

      <!-- <div class="form-input">
        <label for="password">Password</label>
        <input
          id="password"
          type="password"
          formControlName="password"
          autocomplete="on"
          placeholder="************"
        />
        <app-validation-text controlName="password"></app-validation-text>
      </div> -->
    </div>
    <div class="w-full flex flex-col items-center gap-6 mt-3">
      <button type="submit" class="button-primary w-fit py-1.5 px-6">
        Login
      </button>
      <div class="flex gap-1 text-base">
        <a
          href="javascript:void(0)"
          (click)="gotForgotPassword()"
          class="text-lot-dark-gray italic underline text-sm"
          >Reset your password
        </a>
      </div>
      <div class="flex gap-3 border-t-2 pt-8">
        <button type="button" class="button-primary w-fit py-1">
          Login with SSO
        </button>
        <button
          type="button"
          (click)="register()"
          class="button-primary-outline w-fit py-1"
        >
          Not a User? Register here!
        </button>
      </div>
    </div>
  </form>
</ng-template>

<ng-template #confirmation>
  <section
    class="relative w-full bg-white rounded-lg flex flex-col gap-5 items-center"
  >
    <div class="flex flex-col items-center gap-5">
      <h1 class="text-lot-blue text-3xl font-bold">
        Verification Email Re-sent!
      </h1>
      <p class="text-center mt-5">
        Thank you for signing up! To complete your registration, please confirm
        your email address by clicking the link we have sent to your inbox.
      </p>
      <p class="text-center">
        This step is essential to activate your account and ensure secure access
        to our platform. If you didn't receive the email, please check your spam
        folder or contact our support team for assistance.
      </p>
      <p class="text-center">
        Haven't received an email?
        <a
          href="javascript:void(0);"
          (click)="reverify()"
          class="text-lot-blue underline italic"
          >Resend it</a
        >
      </p>
    </div>
  </section>
</ng-template>
