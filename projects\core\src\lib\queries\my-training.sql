
CREATE OR REPLACE FUNCTION get_my_assignments(filter jsonb)
RETURNS json SECURITY DEFINER AS $function$
DECLARE
    due_records record;
    course_records record;
    count_record int;
    _user_id uuid := (filter->>'userId')::uuid;
BEGIN

    WITH user_assignments AS (
        -- Get all teams and groups the user belongs to
        SELECT 
            ug.user,
            ug.team,
            ug.group
        FROM user_groups ug
        WHERE ug.user = _user_id
    ),
    relevant_enrollments AS (
        -- Get course enrollments based on user's assignments
        SELECT DISTINCT
            ce.id as enrollment_id,
            ce.course as course_id,
            ce."dueDate",
            t.id as team_id,
            g.id as group_id,
            ce.created_at
        FROM course_enrollments ce
        LEFT JOIN teams t ON ce.team = t.id
        LEFT JOIN groups g ON ce.group = g.id
        WHERE 
            -- Due date filter
            ce."dueDate" IS NOT NULL
            -- Match either direct user assignment or team/group assignment
            AND (
                ce.user = _user_id
                OR ce.team IN (SELECT team FROM user_assignments WHERE team IS NOT NULL)
                OR ce.group IN (SELECT "group" FROM user_assignments WHERE "group" IS NOT NULL)
            )
        ORDER BY ce."dueDate" ASC
        LIMIT 4
    )
    SELECT json_agg(
        json_build_object(
        'enrollment', re.enrollment_id,
        'enrolledDate', re.created_at,
        'dueDate', re."dueDate",
        'user', _user_id,
        'progress', ut.progress,
        'status', ut.status,
        'completed_at', ut.completed_at,        
        'certification', ut.completed_at,
        'feedback', ut.feedback,
        'scormLaunchLink', ut."scormLaunchLink",
        'scormRegistrationId', ut."scormRegistrationId",
        'type', 'COURSE',
        'rating', ut.rating,
        'trackings', ut.trackings,
        'lastTrace', ut."lastTrace",
        'course', json_build_object(
            'id', c.id,
            'name', c.name,
            'description', c.description,
            'cover', c.cover
            )
        )
    ) INTO course_records
    FROM relevant_enrollments re
        INNER JOIN courses c ON c.id = re.course_id
        LEFT JOIN user_trackings ut ON ut.enrollment = re.enrollment_id;


    WITH user_assignments AS (
        -- Get all teams and groups the user belongs to
        SELECT 
            ug.user,
            ug.team,
            ug.group
        FROM user_groups ug
        WHERE ug.user = _user_id
    ),
    relevant_enrollments AS (
        -- Get course enrollments based on user's assignments
        SELECT DISTINCT
            ce.id as enrollment_id,
            ce.course as course_id,
            ce."dueDate",
            t.id as team_id,
            g.id as group_id,
            ce.created_at
        FROM course_enrollments ce
        LEFT JOIN teams t ON ce.team = t.id
        LEFT JOIN groups g ON ce.group = g.id
        WHERE 
            -- Due date filter
            ce."dueDate" IS NULL
            -- Match either direct user assignment or team/group assignment
            AND (
                ce.user = _user_id
                OR ce.team IN (SELECT team FROM user_assignments WHERE team IS NOT NULL)
                OR ce.group IN (SELECT "group" FROM user_assignments WHERE "group" IS NOT NULL)
            )
        ORDER BY ce.created_at ASC
        LIMIT 4
    )
    SELECT json_agg(
        json_build_object(
        'enrollment', re.enrollment_id,
        'enrolledDate', re.created_at,
        'dueDate', re."dueDate",
        'user', _user_id,
        'progress', ut.progress,
        'status', ut.status,
        'completed_at', ut.completed_at,        
        'certification', ut.completed_at,
        'feedback', ut.feedback,
        'scormLaunchLink', ut."scormLaunchLink",
        'scormRegistrationId', ut."scormRegistrationId",
        'type', 'COURSE',
        'rating', ut.rating,
        'trackings', ut.trackings,
        'lastTrace', ut."lastTrace",
        'course', json_build_object(
            'id', c.id,
            'name', c.name,
            'description', c.description,
            'cover', c.cover
            )
        )
    ) INTO due_records
    FROM relevant_enrollments re
        INNER JOIN courses c ON c.id = re.course_id
        LEFT JOIN user_trackings ut ON ut.enrollment = re.enrollment_id;

    
    RETURN json_build_object(
        'total', 0,
        'courses', course_records,
        'dueCourses', due_records
    );
END;
$function$
LANGUAGE plpgsql;
