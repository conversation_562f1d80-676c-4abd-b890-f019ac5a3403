import { MatIconModule } from '@angular/material/icon';
import { Component, inject, OnInit, resource } from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import {
  GroupItem,
  getId,
  ITeamGroupItem,
  Team,
  UserItem,
  UserTeamGroupItem,
  markControlsDirty,
  UsersCoreService,
  tryPromise,
} from '@lms/core';
import { MatSelectModule } from '@angular/material/select';
import { NgTemplateOutlet } from '@angular/common';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { UploadUtils } from '../upload/upload.utils';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-user-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatSelectModule,
    NgTemplateOutlet,
    MatChipsModule,
    MatIconModule,
    MatProgressBarModule,
    NgxMaskDirective,
  ],
  templateUrl: 'user-form.component.html',
})
export class UserFormComponent implements OnInit {
  service = inject(UsersCoreService);
  public dialogRef: MatDialogRef<UserFormComponent> = inject(MatDialogRef);

  data: {
    item?: UserItem;
    type: 'ADD' | 'EDIT';
  } = inject(MAT_DIALOG_DATA);

  view = this.data.type;

  get self() {
    return this.service.user.id === this.data?.item?.id;
  }

  editTeamGroup = false;
  isLoading = false;
  error?: string;

  form = new FormGroup({
    id: new FormControl(''),
    groups: new FormArray([
      new FormGroup({
        email: new FormControl('', Validators.required),
        role: new FormControl('learner', Validators.required),
        team: new FormControl('1', Validators.required),
        group: new FormControl({ disabled: true, value: '' }),
        error: new FormControl(false),
      }),
    ]),
  });

  userForm = new FormGroup({
    id: new FormControl(''),
    email: new FormControl('', Validators.required),
    role: new FormControl('learner', Validators.required),
    phone: new FormControl(''),
    title: new FormControl(''),
    firstname: new FormControl(''),
    lastname: new FormControl(''),
  });

  teamSource = resource({
    loader: () => this.service.teamsService.queryTeams(),
  });

  userTeams: ITeamGroupItem[] = [];
  userGroups: ITeamGroupItem[] = [];

  groupOptions: GroupItem[] = [];

  get groups() {
    return this.form.controls['groups'] as FormArray;
  }

  get count() {
    return this.groups.controls.filter((c) => c.get('email')?.value).length;
  }

  get title() {
    return this.data.item?.id ? 'Edit User' : 'Add New Users';
  }

  ngOnInit(): void {
    if (this.data.item) {
      this.userTeams = this.data.item.teams || [];
      this.userGroups = this.data.item.groups || [];
      this.userForm.patchValue({
        id: this.data.item.id,
        email: this.data.item.email,
        role: this.data.item.role,
        phone: this.data.item.phone,
        title: this.data.item.title,
        firstname: this.data.item.firstname,
        lastname: this.data.item.lastname,
      });
    }
  }

  onTeam(id: string, teams: Team[], index: number) {
    const team = teams.filter((x) => x.id === id)[0];
    this.groupOptions = (team.groups || []).map(
      (x) => ({ ...x, team: { id } } as GroupItem)
    );

    if (this.userTeams.some((x) => x.teamId === team.id)) return;

    this.userTeams.push({
      teamId: team.id,
      teamName: team.name,
    } as any);
    if (index > -1) {
      this.groups.controls[index].get('group')?.enable();
    }
  }

  onGroup(ids: string[], groups: GroupItem[]) {
    if (!ids.length) return;
    const selectedGroups = groups.filter((x) => ids.includes(x.id));

    const newGroups = selectedGroups.filter(
      (group) => !this.userGroups.some((x) => x.groupId === group.id)
    );

    this.userGroups.push(
      ...newGroups.map(
        (group) =>
          ({
            teamId: getId(group.team),
            groupId: group.id,
            groupName: group.name,
          } as any)
      )
    );
  }

  removeChips(id: string, type: 'TEAM' | 'GROUP') {
    if (type === 'TEAM') {
      const teamIndex = this.userTeams.findIndex((x) => x.teamId === id);
      if (teamIndex > -1) {
        this.userTeams.splice(teamIndex, 1);
      }
    } else if (type === 'GROUP') {
      const groupIndex = this.userGroups.findIndex((x) => x.groupId === id);
      if (groupIndex > -1) {
        this.userGroups.splice(groupIndex, 1);
      }
    }
  }

  addUser(form: any) {
    this.error = undefined;
    if (
      !form.get('email')?.value?.trim() ||
      !UploadUtils.validateEmail(form.get('email')?.value)
    ) {
      form.get('error')?.setValue(true);
      return;
    }
    form.get('error')?.setValue(false);
    this.groups.push(
      new FormGroup({
        email: new FormControl('', Validators.required),
        role: new FormControl('learner', Validators.required),
        team: new FormControl('1', Validators.required),
        group: new FormControl({ disabled: true, value: '' }),
        error: new FormControl(false),
      })
    );
    // const index = this.groups.length - 2;
    // const prev = this.groups.at(index);
    // this.groups.removeAt(index);
    // this.groups.push(prev);
    // if (prev) {
    //   prev.get('group')?.disable();
    // }
  }

  editNewUser(index: number): void {
    this.error = undefined;
    this.userTeams = [];
    this.userGroups = [];
    this.userForm.reset();
    const formData = (this.groups.at(index) as FormGroup).getRawValue();
    this.userForm.patchValue({
      email: formData.email,
      role: formData.role,
    });
    this.view = 'EDIT';
  }

  removeUser(index: number) {
    if (this.groups.length === 1) return;
    this.groups.removeAt(index);
  }

  async onSubmit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const formData = this.form.getRawValue().groups;

    const emails = formData.map((x: any) => ({
      email: x.email,
      isValid: UploadUtils.validateEmail(x.email),
    }));

    if (emails.some((x) => !x.isValid)) {
      this.error =
        'Please enter valid emails: ' +
        emails
          .filter((x) => !x.isValid)
          .map((x) => x.email)
          .join(', ');
      return;
    }

    this.isLoading = true;
    const users = formData.map(
      (user: any) =>
        ({
          email: user.email,
          role: user.role,
          organization: getId(this.service.organization),
          // firstname: `PENDING-${user.role}-`,
          // lastname: `PENDING-${user.role}-`,
        } as UserItem & any)
    );

    const [error, res] = await tryPromise(this.service.add(users));
    this.isLoading = false;

    if (error || res?.error) {
      console.log(error);
      this.error = res?.error ?? error?.message;
      return;
    }

    const userTeamsGroups = formData
      .filter((x) => x.team !== '1')
      .map((user: any) => {
        const item = {
          user: res.data.find((x) => x.email === user.email)?.id,
          organization: getId(this.service.organization),
          team: user.team,
        } as UserTeamGroupItem & any;
        if (user.group) {
          item.group = user.group;
        }
        return item;
      });
    this.isLoading = true;
    const res2 = await this.service.teamsService.assignUsers({
      news: userTeamsGroups,
      olds: [],
    });

    this.isLoading = false;
    if (res2.length) {
      this.error = res2.join(', ');
      return;
    }

    this.dialogRef.close();
    this.service.userSource.reload();
  }

  async onSubmitEdit() {
    this.error = undefined;
    if (this.isLoading) return;
    if (this.userForm.invalid) {
      markControlsDirty(this.userForm);
      return;
    }
    const user = this.data.item;
    if (!user) {
      return;
    }
    this.isLoading = true;
    const formData = this.userForm.getRawValue();

    const [error, res] = await tryPromise(
      this.service.update([
        {
          id: user.id,
          email: formData.email ?? user.email,
          role: formData.role,
          phone: formData.phone?.replace(/\D/g, ''),
          title: formData.title,
          firstname: formData.firstname,
          lastname: formData.lastname,
          organization: getId(user.organization),
        } as UserItem & any,
      ])
    );
    this.isLoading = false;

    if (error || res?.error) {
      console.log(error);
      this.error = res?.error ?? error?.message;
      return;
    }

    this.isLoading = true;
    const myGroups = this.userGroups.map((group) => {
      const item = {
        user: getId(this.data.item),
        organization: getId(this.service.organization),
        team: group.teamId,
        group: group.groupId,
      } as UserTeamGroupItem & any;
      if (group.userGroupId) {
        item['id'] = group.userGroupId;
      }
      return item;
    });

    const myTeams = this.userTeams.filter(
      (x) => !myGroups.find((y) => y.team === x.teamId)
    );

    myGroups.push(
      ...myTeams.map((team) => {
        const item = {
          user: getId(this.data.item),
          organization: getId(this.service.organization),
          team: team.teamId,
        } as any;
        if (team.userGroupId) {
          item['id'] = team.userGroupId;
        }
        return item;
      })
    );

    if (!myGroups.length) {
      this.isLoading = false;
      this.dialogRef.close();
      this.service.userSource.reload();
      return;
    }

    const res2 = await this.service.reassignUser(myGroups);
    this.isLoading = false;

    if (res2.length) {
      this.error = res2.join(', ');
      return;
    }
    this.dialogRef.close();
    this.service.userSource.reload();
  }
}
