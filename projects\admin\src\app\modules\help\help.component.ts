import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { GlobalStateService } from '@lms/core';
import { GraphQLClient } from 'graphql-request';
import { BlogCategory, BlogPost, GET_All_POST_BY_CATEGORIES_QUERY } from './postQuery';
import { HtmlWrapperComponent } from '@lms/shared';

@Component({
    selector: 'app-help',
    templateUrl: './help.component.html',
    imports: [CommonModule, HtmlWrapperComponent]
})
export class HelpComponent implements OnInit {
  state = inject(GlobalStateService);

  blogs: BlogCategory[] = [];
  currentPost: BlogPost | null = null;

  constructor() { void 0 }

  async ngOnInit() {
    const res = await this.query(GET_All_POST_BY_CATEGORIES_QUERY);
    this.blogs = (res as any).postCategories;
  }

  viewPost(item: BlogPost) {
    this.currentPost = item;
  }

  graphCMSClient() {
    return new GraphQLClient(this.state.envConfig.env.graphCMSApi, {
      headers: {
        authorization: `Bearer ${this.state.envConfig.env.graphCMSToken}`,
      },
    });
  }

  query(queryDoc: any, variables = {}) {
    const graph = this.graphCMSClient();
    if (!graph) {
      return null;
    }
    return graph.request(queryDoc, variables);
  };
}
