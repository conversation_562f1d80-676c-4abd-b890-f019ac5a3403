import { OverlayModule } from '@angular/cdk/overlay';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  signal,
  TemplateRef,
  viewChild,
} from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import {
  AIService,
  MediaBucket,
  MediaCenterService,
  getFileFromBase64,
  getFileFromUrl,
} from '@lms/core';
import { FileType } from '@lms/shared';

@Component({
  selector: 'app-rich-image',
  imports: [OverlayModule, MatTooltipModule, MatProgressBarModule],
  templateUrl: './rich-image.component.html',
  styles: [],
})
export class RichImageComponent implements OnInit {
  aiService = inject(AIService);
  service = inject(MediaCenterService);
  readonly dialog = inject(MatDialog);

  imagePickerRef = viewChild<TemplateRef<any>>('imagePicker');

  @Input() url: string;
  @Output() content = new EventEmitter<string>();

  uploadType: FileType = 'image';

  isLoading = false;
  prompt = signal('');

  aiImages: string[] = [];

  currentUrl: string | null = null;

  error: string | undefined;

  dialogRef: MatDialogRef<any>;

  selectedFile?: File;

  get showPreview() {
    return !!this.selectedFile || !!this.aiImages.length;
  }

  ngOnInit(): void {
    this.currentUrl = this.url;
  }

  openImagePicker() {
    this.dialogRef = this.dialog.open(this.imagePickerRef()!, {
      minWidth: '500px',
    });
  }

  onFileChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files?.length) return;
    const file = input.files[0];
    if (!file.type.startsWith('image/')) {
      this.error = 'Please upload a image file.';
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      this.currentUrl = reader.result as any;
      this.selectedFile = file;
    };
    reader.readAsDataURL(file);
  }

  reset() {
    this.selectedFile = undefined;
    this.aiImages = [];
  }

  async selectImage(url: string) {
    this.isLoading = true;
    // this.downloadImage(url);
    // const file = await getFileFromUrl(url);
    const file = await getFileFromBase64(url);
    this.isLoading = false;
    if (!file) return;
    this.selectedFile = file;
    await this.save(file);
    this.dialogRef.close();
  }

  async generateAI() {
    let prompt = this.prompt();
    this.isLoading = true;
    const res = await this.aiService.generateImage(prompt);
    this.isLoading = false;
    this.aiImages = res.data ?? [];
    if (res.error) {
      this.error = res.error;
      return;
    }
  }

  onRemove() {
    this.openImagePicker();
  }

  async save(file: File) {
    const folder = this.service.state
      .user()
      .organization?.name.replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    const { data, error } = await this.service.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.error = error;
      return;
    }
    if (!data) return;

    this.content.emit(data.url);
  }

  async subscribe() {
    if (!this.selectedFile) {
      this.error = 'Please select a file.';
      return;
    }

    if (this.url) {
      await this.service.removeFile(MediaBucket.IMAGES, this.url);
    }
    await this.save(this.selectedFile);
    this.dialogRef.close();
  }

  downloadImage(url: string) {
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    link.download = 'ai-generated-image';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
