
<div class="flex flex-col h-full min-h-full">
  <div
    class="text-sm font-medium text-center text-gray-500 border-b border-gray-200"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="tab = item.id"
          [routerLink]="[item.path]"
          class="inline-block text-xl p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === item.id"
          [class.font-bold]="tab === item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>

  <div class="flex pb-20 w-full h-full bg-white rounded-3xl p-10 mt-10 overflow-y-auto scrollbar">
    <router-outlet />
  </div>
</div>
