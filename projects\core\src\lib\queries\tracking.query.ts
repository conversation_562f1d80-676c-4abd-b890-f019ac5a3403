export const CourseLearnerPlayer = `
query getCoursePlayer($id: UUID!, $userId: UUID!, $teams: [UUID!], $groups: [UUID!]) {
  course_enrollmentsCollection(
    filter: { or: [{course: {eq: $id}}, {team: {in: $teams}},{group: {in: $groups}}] } 
  ) {
    edges {
      node {
        id
        dueDate
        created_at
        courses {
        id
        name
        short
        description
        cover
        venue
        duration
        creator
        scormCourseId
        organization
        created_by
        created_at
        updated_by
        updated_at
        modulesCollection {
          edges {
            node {
              id
              name
              position
              description
              order
              course
              lessonsCollection {
                edges{
                  node {
                    id
                    name
                    contents
                    module
                    order
                  }
                }
              }
            }
          }
        }
        }
        user_trackingsCollection (
          filter: {
            user: {eq: $userId }
          }
        ) {
          edges {
            node {
              id
              enrollment
              type
              status
              scormLaunchLink
              scormRegistrationId
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      }
    }
  }
  
}
`;
