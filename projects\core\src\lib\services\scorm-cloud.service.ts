import { Injectable, inject } from '@angular/core';
import { ScormClient } from 'scormcloud-client';
import { Learner, Registration } from 'scormcloud-client/dist/types';
import { ConfigurationLoader } from '../environment';
import { GlobalStateService } from './global-state.service';
import { tryPromise } from '../utils';
import { Course, UserItem } from '../models';

@Injectable({
  providedIn: 'root',
})
export class ScormCloundService {
  envConfig = inject(ConfigurationLoader);
  state = inject(GlobalStateService);
  // client: ScormClient;

  get user(): UserItem {
    return this.state.user();
  }
  get client() {
    return this.state.scormClient;
  }
  // init() {
  //   const userAuth = this.envConfig.env.cloudApi['apiId'];
  //   const code = this.envConfig.env.cloudApi['apiKey'];
  //   if (!userAuth || !code) {
  //     throw new Error('CloudAPI appId and password are required');
  //   }
  //   // this.client = new ScormClient(userAuth, code, 'read');
  //   this.client = new ScormClient(userAuth, code, 'write');
  // }

  async getCourses() {
    const res = await this.client.getCourses({
      scope: 'read',
    });

    let items = res.courses;

    let moreToken = res.more;
    while (!!moreToken) {
      const resMore = await this.client.getCourses({
        more: moreToken,
        scope: 'read',
      });
      items = [...items, ...resMore.courses];
      if (resMore.more !== moreToken) {
        moreToken = resMore.more;
      } else {
        moreToken = undefined;
      }
    }
    return items;
  }

  getRegistrationId(courseId: string) {
    const organationName = this.envConfig.env.organization['name'];
    return (
      organationName.replace(/\s/g, '').toLowerCase().trim() +
      '-' +
      courseId +
      '-' +
      this.user?.id
    );
  }

  async getRegistrationStatus(courseId: string) {
    const registrationId = this.getRegistrationId(courseId);
    const [_, res] = await tryPromise<Registration>(
      this.client.getRegistrationProgress(registrationId, {
        scope: 'read',
      })
    );

    return res;
  }

  async subscribeLearner(course: Course) {
    if (!course.scormCourseId) {
      return {
        url: undefined,
        registrationId: undefined,
      };
    }
    const registrationId = this.getRegistrationId(course.id!);
    const registration = await this.getRegistrationStatus(course.id!);

    if (registration?.id && registration?.course.id === course.scormCourseId) {
      return {
        url: 'READY',
        registrationId: registration.id,
      };
    }

    const learner: Learner = {
      id: this.user?.id!,
      firstName: this.user?.firstname,
      lastName: this.user?.lastname,
    };

    const [error, res] = await tryPromise(
      this.client.createRegistration(
        registrationId,
        course.scormCourseId,
        learner,
        { scope: 'write' }
      )
    );

    if (error) {
      console.log(error);
      return {
        url: undefined,
        registrationId: undefined,
      };
    }

    if (res?.success) {
      return {
        url: 'READY',
        registrationId: registrationId,
      };
    }

    return {
      url: undefined,
      registrationId: undefined,
    };
  }

  async playCourse(registrationId: string, courseId: string) {
    const returnUrl =
      this.envConfig.env.learnerAppUrl + '/myTraining/training/' + courseId;
    // const returnUrl = 'http://localhost:3001/my-learning/course/' + courseId;
    // this.init();
    const res = await this.client.createLaunchLink(registrationId, returnUrl, {
      scope: 'read',
    });
    return res.launchLink;
  }

  async uploadScorm(file: File, courseId: string) {
    const formData = new FormData();
    formData.append('file', file);
    const token = `${this.envConfig.env.cloudApi['apiId']}:${this.envConfig.env.cloudApi['apiKey']}`;
    const url = `https://cloud.scorm.com/api/v2/courses/importJobs/upload?courseId=${courseId}`;

    const [error, res] = await tryPromise(
      fetch(url, {
        method: 'POST',
        headers: {
          Authorization: 'Basic ' + btoa(token),
        },
        body: formData,
      }).then((response) => response.json())
    );

    if (error || res?.error) {
      console.log(res?.error);
      return {
        error: error?.message ?? res?.error,
      };
    }

    return {
      data: res['result'] ? courseId : res,
    };
  }

  async deleteScorm(courseId: string) {
    const token = `${this.envConfig.env.cloudApi['apiId']}:${this.envConfig.env.cloudApi['apiKey']}`;
    const url = `https://cloud.scorm.com/api/v2/courses/${courseId}`;

    const [error, res] = await tryPromise(
      fetch(url, {
        method: 'DELETE',
        headers: {
          Authorization: 'Basic ' + btoa(token),
        },
      }).then((response) => response.json())
    );

    if (error || res?.error) {
      console.log(res?.error);
      return {
        error: error?.message ?? res?.error,
      };
    }

    return {
      data: courseId,
    };
  }
}
