import { Component, computed, inject, OnInit } from '@angular/core';
import { PlayerService } from '../services/app.service';
import { ProgressComponent, ResourceHeaderComponent } from '@lms/shared';
import { RouterLink } from '@angular/router';
import { MatMenuModule } from '@angular/material/menu';
import { NgTemplateOutlet } from '@angular/common';
import { NavigationItem } from '../services/common.model';
import { toSignal } from '@angular/core/rxjs-interop';
import { UserTrackingItem } from '@lms/core';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  imports: [MatMenuModule, NgTemplateOutlet, ProgressComponent],
  styles: [
    `
      ::ng-deep {
        ::ng-deep .mat-mdc-menu-panel {
          max-width: 410px !important;
        }
      }
    `,
  ],
})
export class HeaderComponent {
  service = inject(PlayerService);

  source = this.service.courseSource;

  navigations = computed(() => {
    const items: NavigationItem[] = [
      {
        id: 'LearnMate',
        name: 'LearnMate',
        icon: 'mark_unread_chat_alt',
        type: 'TOP',
      },
      {
        id: 'SkillQuest',
        name: 'SkillQuest',
        icon: 'assignment_add',
        type: 'TOP',
      },
    ];
    items.push(...this.service.courseNavigation());
    return items;
  });

  isLaunched = toSignal(this.service.launch.asObservable(), { initialValue: 0 });

  get mode() {
    return this.service.source;
  }

  back() {
    window.close();
  }

  launch() {
    this.service.launch.next(1);
  }

  gotTo(item: NavigationItem) {
    if (!item.current && !item.visited) return;

    if (item.type === 'EXIT') {
      this.back();
      return;
    }

    this.service.viewChat = ['LearnMate', 'SkillQuest'].includes(item.id);
  }
}
