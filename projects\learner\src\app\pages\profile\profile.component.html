<div class="flex flex-col">
  <div
    class="text-sm font-medium text-center text-gray-500 border-b border-gray-200 mt-4"
  >
    <ul class="flex flex-wrap -mb-px">
      @for (item of tabs; track $index) {
      <li class="me-2">
        <a
          href="javascript:void(0)"
          (click)="tab = item.id"
          class="inline-block p-4 border-b-2 rounded-t-lg hover:text-lot-blue hover:border-gray-300"
          [class.text-lot-dark]="tab === item.id"
          [class.font-bold]="tab === item.id"
          [class.border-lot-blue]="tab === item.id"
        >
          {{ item.name }}
        </a>
      </li>
      }
    </ul>
  </div>

  <div class="flex mb-20">
    @if (tab === 1) {
    <app-form-profile />
    } @if (tab === 2) {
    <ng-container *ngTemplateOutlet="notification" />
    }
  </div>
</div>

<ng-template #notification>
  <div class="flex flex-col py-16 gap-5">
    <span>Course assigned</span>
    <div class="flex gap-10">
      <div class="flex gap-2 items-center">
        <input
          id="courseInApp"
          type="checkbox"
          [formControl]="courseInApp"
          class="size-4 text-lot-dark bg-gray-100 border-gray-300 outline-none rounded-xl"
        />
        <label for="courseInApp" class="ms-2 text-lg font-medium text-lot-dark">
          In-App
        </label>
      </div>
      <div class="flex gap-2 items-center">
        <input
          id="courseEmail"
          type="checkbox"
          [formControl]="courseEmail"
          class="size-4 text-lot-dark bg-gray-100 border-gray-300 outline-none rounded-xl"
        />
        <label for="courseEmail" class="ms-2 text-lg font-medium text-lot-dark">
          Email
        </label>
      </div>
      <div class="flex gap-2 items-center">
        <input
          id="courseSMS"
          type="checkbox"
          [formControl]="courseSMS"
          class="size-4 text-lot-dark bg-gray-100 border-gray-300 outline-none rounded-xl"
        />
        <label for="courseSMS" class="ms-2 text-lg font-medium text-lot-dark">
          SMS
        </label>
      </div>
    </div>
  </div>
</ng-template>
