import { inject, Injectable } from '@angular/core';
import { GlobalStateService } from '../services';
import { AuthChangeEvent, Session } from '@supabase/supabase-js';
import { AppStorage } from '../services/storage';
import {
  BillFrequency,
  BillingPlan,
  EdgeError,
  Organization,
  OrgSubscription,
  SubscriptionStatus,
  UserItem,
} from '../models';
import { BehaviorSubject } from 'rxjs';
import { UsersCoreService } from '../api';
import { getExpiredAt, getId } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  state = inject(GlobalStateService);
  userService = inject(UsersCoreService);

  get organization() {
    return this.state.envConfig.env.organization;
  }

  isLoading = false;

  async refreshSession() {
    let session = await this.state.supabase.auth.getSession();
    const user = session?.data?.session?.user;
    if (!user) return;

    await this.getMyInfo(user.id, user.user_metadata['organization']);
    this.state.session.set(session.data.session!);
    return this.state.mySubscription();
  }

  verifySubscription(data: OrgSubscription) {}

  authChanges(
    callback: (event: AuthChangeEvent, session: Session | null) => void
  ) {
    return this.state.supabase.auth.onAuthStateChange(callback);
  }

  async getUserByToken(token: string) {
    const res = await this.state.supabase.auth.getUser(token);
    const user = res.data.user;
    if (!user) return;

    await this.getMyInfo(user.id, user.user_metadata['organization']);
    return this.state.mySubscription();
  }

  // async setupOrganization(payload: any): Promise<{
  //   data?: any;
  //   error?: string;
  // }> {
  //   const user = this.state.user();
  //   if (!user) {
  //     return {
  //       error: 'User data not found ',
  //     };
  //   }
  //   if (!payload.organization) {
  //     return {
  //       error: 'Organization data not found ',
  //     };
  //   }

  //   const resp = await this.state.supabase
  //     .from('organizations')
  //     .insert({
  //       ...payload.organization,
  //       created_by: user.id,
  //       updated_by: user.id,
  //       phone: payload?.user?.phone,
  //     })
  //     .select('*');
  //   if (resp.error) return { error: resp.error.message };

  //   const d: any = {
  //     id: user.id,
  //     organization: (<Organization[]>resp.data)[0].id,
  //     role: 'owner',
  //     email: user.email,
  //     firstname: user.firstname,
  //     lastname: user.lastname,
  //   };
  //   await this.userManagementService.updateOne(d);
  //   await this.state.supabase.auth.refreshSession();
  //   return { data: resp.data };
  // }

  async signUp(payload: {
    user: {
      firstname: string;
      lastname: string;
      email: string;
      phone: string;
      password: string;
    };
    org: {
      name: string;
      email: string;
      phone: string;
      about: {};
      contact: {};
      description: string;
    };
  }) {
    const { data, error } = await this.state.supabase.functions.invoke<{
      data: UserItem[];
      error: EdgeError[];
    }>('register-user', {
      body: JSON.stringify(payload),
    });

    return {
      data: (data?.data || []).filter(Boolean) as UserItem[],
      error: (data?.error
        ?.filter(Boolean)
        ?.map((e) => e.message)
        ?.join(', ') ?? error?.message) as string | undefined,
    };
  }

  async signIn(email: string, password: string) {
    const { error, data } = await this.state.supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) {
      return {
        data: undefined,
        error: error.message,
      };
    }
    await this.getMyInfo(
      data.user?.id,
      data.user.user_metadata['organization']
    );

    return {
      data: data.user,
      error: undefined,
    };
  }

  async getMyInfo(id: string, orgId: string) {
    const { data, error } = await this.state.supabase.rpc('get_my_info', {
      filter: {
        organization: orgId,
        userId: id,
      },
    });

    if (error) {
      return error?.message;
    }

    const info = data['data']['array_agg'][0];
    this.state.mySubscription.set({
      ...info.subscription,
      organization: info.organization,
      used_seat: data['used_seat_count'],
      plan: info.plan,
    } as OrgSubscription);

    this.state.user.set({
      ...info.user,
      organization: info.organization,
      teams: (info.teams || []).filter((x: any) => x.teamId && !x.groupId),
      groups: (info.teams || []).filter((x: any) => x.teamId && x.groupId),
    } as UserItem);

    return undefined;
  }

  async subscribeTrial(orgId: string) {
    const req = await this.state.supabase
      .from('billing_plans')
      .select('*')
      .eq('name', 'TRIAL')
      .limit(1);

    return this.subscribeOrganization({
      orgnaization: orgId,
      plan: ((req.data || []) as BillingPlan[])[0],
      expiredAt: getExpiredAt(BillFrequency.WEEKLY),
      paidAmount: 0,
      addOns: [],
    });
  }

  async subscribeOrganization(request: {
    orgnaization: string;
    plan: BillingPlan;
    expiredAt: string;
    paidAmount: number;
    addOns: BillingPlan[];
  }) {
    if (!request.plan.id) {
      return {
        error: 'Unable to subscribe to Trial Plan',
      };
    }
    const payload = {
      organization: request.orgnaization,
      plan: request.plan.id,
      expired_at: request.expiredAt,
      seat_max: request.plan.countMax,
      current: 1,
      status: SubscriptionStatus.ACTIVE,
      purchaseAmount: request.paidAmount,
      addOns: request.addOns,
    };
    const { data, error } = await this.state.supabase
      .from('org_subscription')
      .insert(payload)
      .select('*');

    return {
      data: (data ?? []) as OrgSubscription[],
      error: error?.message,
    };
  }

  async requestPasswordReset(email: string) {
    return await this.state.supabase.auth.resetPasswordForEmail(email);
  }

  async verifyEmail(email: string, token: string) {
    return await this.state.supabase.auth.verifyOtp({
      email,
      token,
      type: 'email',
      options: {
        redirectTo: window.location.origin + '/reset-password',
      },
    });
  }

  async resendCode(email: string) {
    return await this.state.supabase.auth.signInWithOtp({
      email,
      options: {
        shouldCreateUser: false,
      },
    });
  }

  async resetPassword(password: string) {
    const user = this.state.user();
    if (!user) return;
    const res = await this.state.supabase.auth.updateUser({
      password: password,
      email: user.email,
    });
    return res;
  }

  async signOut() {
    const { error } = await this.state.supabase.auth.signOut();
    if (error) {
      return { error };
    }
    AppStorage.flush();
    sessionStorage.removeItem('ui_recovery_token');
    return {};
  }

  async signOutAndRedirect() {
    await this.signOut();
    sessionStorage.removeItem('ui_recovery_token');
    window.location.assign('/sign-in');
    return {};
  }
}
