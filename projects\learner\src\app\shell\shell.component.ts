import { Component, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { HeaderComponent } from './header/header.component';
import { LoadingService } from '@lms/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'lms-shell',
    templateUrl: './shell.component.html',
    imports: [CommonModule, HeaderComponent, RouterModule, MatProgressBarModule]
})
export class ShellComponent {
  public loader: LoadingService = inject(LoadingService);
  loading$ = this.loader.loading$;
}
