import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Team } from '@lms/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TeamAction } from '../teams.component';

@Component({
  selector: 'app-team-list',
  imports: [MatTooltipModule],
  templateUrl: './list.component.html',
})
export class TeamListComponent {
  @Input() data: (Team & any)[] = [];
  @Input() isTeam = true;

  @Output() onItem = new EventEmitter<{
    type: TeamAction;
    data: Team;
  }>();

  action(type: TeamAction, item: Team) {
    this.onItem.emit({ type, data: item });
  }
}
