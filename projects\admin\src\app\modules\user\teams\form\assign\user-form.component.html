@if (data.type === 'ASSIGN_TEAM_GROUP') {
<div class="flex flex-col items-stretch gap-5 py-1 h-full min-h-full">
  <h1 class="text-2xl font-semibold">
    Assign Users to
    <span class="text-lot-blue italic">{{ data.item.name }}</span>
    {{ data.isTeam ? " Team" : " Group" }}
  </h1>
  <div class="flex flex-col gap-3 mb-8 w-full h-full">
    <div class="flex justify-between items-center">
      <div
        class="rounded-md border border-lot-blue flex items-center gap-2 px-3 py-1"
      >
        <i class="fa-solid fa-magnifying-glass text-lot-blue"></i>
        <input
          type="search"
          name="search"
          id="search"
          [formControl]="userControl"
          [matAutocomplete]="userTmp"
          class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
          placeholder="Search Users"
        />
        <mat-autocomplete
          autoActiveFirstOption
          #userTmp="matAutocomplete"
          (optionSelected)="selectUser($event.option.value, 'ADD')"
          [displayWith]="displayFn"
        >
          @for (option of userOptions(); track option) {
          <mat-option [value]="option">{{ option.userName }}</mat-option>
          }
        </mat-autocomplete>
      </div>

      <div class="flex items-center gap-3">
        <button
          class="button-primary-outline w-fit"
          (click)="close.emit({ action: 'CANCEL' })"
          type="button"
        >
          Cancel
        </button>
        <button
          class="button-primary w-fit"
          type="button"
          (click)="bulkAssignUsers()"
        >
          Save
        </button>
      </div>
    </div>

    <div class="flex-grow min-h-96 h-full">
      <div class="grid grid-cols-2 gap-10 items-center">
        <div class="flex flex-col gap-2 p-4 rounded-xl border h-full">
          <h4 class="text-lot-dark font-semibold mb-2">
            ({{ userGroups.length }}) - Assigned Users
          </h4>

          <ul
            class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
          >
            @for (item of userGroups; track item) {
            <li
              class="flex justify-between items-center gap-3 border-b py-2 group"
            >
              <span>{{ item.userName }}</span>
              <a
                href="javascript:void(0)"
                (click)="selectUser(item, 'REMOVE')"
                class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
              >
                <i class="fa-solid fa-trash"></i>
              </a>
            </li>
            } @empty {
            <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
            }
          </ul>
        </div>
        <div class="flex flex-col gap-2 p-4 rounded-xl border h-full">
          <h4 class="text-lot-dark font-semibold mb-2">
            ({{ users.length }}) - Ready for saving
          </h4>

          <ul
            class="flex flex-col max-h-[480px] overflow-y-auto scrollbar border-t pr-3 mt-2"
          >
            @for (item of users; track item) {
            <li
              class="flex justify-between items-center gap-3 border-b py-2 group"
            >
              <span>{{ item.userName }}</span>
              <div class="flex items-center gap-2">
                <span
                  class="px-1 border rounded-md"
                  [class.border-lot-ai]="item.tag === 'new'"
                  [class.border-lot-danger]="item.tag === 'delete'"
                  [class.text-lot-ai]="item.tag === 'new'"
                  [class.text-lot-danger]="item.tag === 'delete'"
                  >{{ item.tag }}</span
                >
                <a
                  href="javascript:void(0)"
                  (click)="selectUser(item, 'CLEAR')"
                  class="text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl"
                >
                  <i class="fa-solid fa-trash"></i>
                </a>
              </div>
            </li>
            } @empty {
            <li class="text-lot-dark-gray/50 text-center py-2">No items</li>
            }
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
} @if (data.type === 'ASSIGN_USER') {
<div class="flex flex-col gap-5 py-5">
  <h1 class="text-2xl font-semibold">
    Assign Team(s) to {{ data.user.username }}
  </h1>
  <div class="flex flex-col mb-8 w-full min-h-64">
    <div class="grid grid-cols-2 gap-3 items-center">
      <div class="form-input">
        <label for="team">Select Team</label>
        <mat-select
          (valueChange)="onTeam($event, teamSource.value() || [], -1)"
        >
          <mat-option> -- </mat-option>
          @for (item of teamSource.value() || []; track item) {
          <mat-option [value]="item.id">
            {{ item.name }}
          </mat-option>
          }
        </mat-select>
      </div>
      <div class="form-input">
        <label for="group">Select Groups</label>
        <mat-select (valueChange)="onGroup($event, groupOptions)" multiple>
          <mat-option> -- </mat-option>
          @for (item of groupOptions; track item) {
          <mat-option [value]="item.id">
            {{ item.name }}
          </mat-option>
          }
        </mat-select>
      </div>
    </div>

    <div class="grid grid-cols-2 gap-3 items-center mt-3">
      <div class="form-input">
        <label for="teams">Assigned Teams</label>
        <mat-chip-set aria-label="Groups selection">
          @for (team of userTeams; track team) {
          <mat-chip>
            <div class="flex items-center gap-1">
              <span>{{ team.teamName }}</span>
              <a
                href="javascript:void(0)"
                (click)="removeChips(team.teamId, 'TEAM')"
              >
                <i class="fa-solid fa-circle-xmark"></i>
              </a>
            </div>
          </mat-chip>
          }@empty {
          <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
            >Not yet assigned</span
          >
          }
        </mat-chip-set>
      </div>

      <div class="form-input">
        <label for="teams">Assigned Groups </label>
        <mat-chip-set aria-label="Groups selection">
          @for (group of userGroups; track group) {
          <mat-chip>
            <div class="flex items-center gap-1">
              <span>{{ group.groupName }}</span>
              <a
                href="javascript:void(0)"
                (click)="removeChips(group.groupId, 'GROUP')"
              >
                <i class="fa-solid fa-circle-xmark"></i>
              </a>
            </div>
          </mat-chip>
          }@empty {
          <span class="pl-5 mt-4 italic text-lot-dark-gray/90"
            >Not yet assigned</span
          >
          }
        </mat-chip-set>
      </div>
    </div>
  </div>

  <div class="flex justify-end items-center gap-3">
    <button
      class="button-primary-outline w-fit"
      (click)="close.emit({ action: 'CANCEL' })"
      type="button"
    >
      Cancel
    </button>
    <button
      class="button-primary w-fit py-1.5"
      type="button"
      (click)="onSubmitAssignTeamGroup()"
    >
      Save and Close
    </button>
  </div>
</div>
}
