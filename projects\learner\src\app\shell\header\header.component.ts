import { Component, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { AuthService, GlobalStateService } from '@lms/core';
import { MatMenuModule } from '@angular/material/menu';

@Component({
  selector: 'lms-header',
  templateUrl: './header.component.html',
  imports: [RouterLink, MatMenuModule],
})
export class HeaderComponent {
  state = inject(GlobalStateService);
  authService = inject(AuthService);
  router = inject(Router);

  subMenu = [
    {
      icon: 'settings',
      name: 'Settings',
      link: '/myTraining/settings',
    },
    {
      icon: 'help',
      name: 'FAQ',
      link: '/myTraining/faq',
    },
    {
      icon: 'library_books',
      name: 'Resources',
      link: '/myTraining/resources',
    },
    {
      icon: 'input',
      name: 'Logout',
      link: '/',
    },
  ];

  get user() {
    return this.state.user();
  }
 
  signOut(): void {
    this.authService.signOutAndRedirect();
  }
}
