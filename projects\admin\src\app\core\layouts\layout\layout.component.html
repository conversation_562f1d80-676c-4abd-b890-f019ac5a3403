<div class="flex">
  @if (!hideSideMenu) {
  <div
    class="relative w-44 h-screen shadow-xl hidden md:flex flex-col bg-white pl-10 py-20"
  >
    <a
      href="/lms"
      class="text-white text-3xl font-semibold uppercase hover:text-gray-300"
    >
      <img
        alt="Company Logo"
        class="w-auto h-14"
        src="assets/images/new/logo.png"
      />
    </a>

    <div class="flex-grow h-full pt-40">
      <div class="flex flex-col h-full">
        <ul class="flex flex-col gap-3">
          @for (item of menus; track $index) {
          <li>
            @if(item.external){
            <a
              [href]="item.path"
              target="_blank"
              rel="noopener noreferrer"
              class="flex items-center font-semibold text-lg"
              [class.text-lot-blue]="isPath.includes(item.select)"
              [class.text-lot-dark]="!isPath.includes(item.select)"
            >
              {{ item.title }}
            </a>

            } @else{
            <a
              class="flex items-center font-semibold text-lg"
              [routerLink]="[item.path]"
              [class.text-lot-blue]="isPath.includes(item.select)"
              [class.text-lot-dark]="!isPath.includes(item.select)"
            >
              {{ item.title }}
            </a>
            }
          </li>
          }
        </ul>
      </div>
    </div>

    <div class="relative">
      <button
        [matMenuTriggerFor]="menu"
        type="button"
        class="p-2 size-14 rounded-full focus:outline-none"
      >
        <img
          src="https://images.pexels.com/photos/460031/pexels-photo-460031.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
          alt=""
          srcset=""
          class="w-full h-full object-cover rounded-full"
        />
      </button>
      <mat-menu #menu="matMenu">
        @for (item of subMenu; track $index; let last=$last) {
        <a
          class="border-gray-200 w-[200px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-dark hover:text-lot-blue flex items-center gap-2"
          href="javascript:void(0)"
          [class.border-b]="!last"
          (click)="gotTo(item.link)"
        >
          <span class="material-symbols-outlined">{{ item.icon }}</span>
          {{ item.name }}
        </a>
        }
      </mat-menu>
    </div>
  </div>
  }

  <div class="w-full flex flex-col h-screen overflow-y-hidden">
    <ng-container *ngIf="loader.loading$ | async as isLoading">
      <mat-progress-bar
        *ngIf="isLoading"
        color="accent"
        mode="indeterminate"
      ></mat-progress-bar>
    </ng-container>
    <div
      class="w-full overflow-x-hidden border-t flex flex-col h-full scrollbar"
    >
      <main class="w-full h-full flex-grow px-12">
        <router-outlet></router-outlet>
      </main>
    </div>
  </div>
</div>