export const CourseCatalogQuery = `
query getCatalogCourse($id: UUID!, $teams: [UUID!], $dueDate: Datetime, $limit: Int) {
  course_enrollmentsCollection(
  first: $limit,
    orderBy: {created_at: AscNullsLast, dueDate: AscNullsFirst }
    filter: { or: [{user: {eq: $id}}, {team: {in: $teams}}] }
  ){
    edges {
      node {
        id
        dueDate
        created_at
        courses {
          id
          name
          type
          short
          cover
          venue
          duration          
        }
      user_trackingsCollection {
          edges {
            node {
              id
              enrollment
              type
              status
              scormLaunchLink
              scormRegistrationId
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      }
    }
  }
}
`;

export const CourseCatalogDueFirstQuery = `
query getCatalogCourse($id: UUID!, $teams: [UUID!], $dueDate: Datetime, $limit: Int) {
  course_enrollmentsCollection(
  first: $limit,
  orderBy: { dueDate: AscNullsLast }
  filter: { or: [{user: {eq: $id}}, {team: {in: $teams}}] }
  ) {
    edges {
      node {
        id
        dueDate
        created_at
        courses {
          id
          name
          type
          short
          cover
          venue
          duration          
        }
      user_trackingsCollection {
          edges {
            node {
              id
              enrollment
              type
              status
              scormLaunchLink
              scormRegistrationId
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      }
    }
  }
}
`;

export const CourseCatalogNoDueQuery = `
query getCatalogCourse($id: UUID!, $teams: [UUID!], $dueDate: Datetime, $limit: Int) {
  course_enrollmentsCollection(
  first: $limit,
  orderBy: { dueDate: AscNullsLast }
  filter: { or: [{user: {eq: $id}}, {team: {in: $teams}}] }
  ) {
    edges {
      node {
        id
        dueDate
        created_at
        courses {
          id
          name
          type
          short
          cover
          venue
          duration          
        }
      user_trackingsCollection {
          edges {
            node {
              id
              enrollment
              type
              status
              scormLaunchLink
              scormRegistrationId
              progress
              completed_at
              created_by
              dueDate
              updated_by
              updated_at
              rating
              lastTrace
              feedback
              trackings
            }
          }
        }
      }
    }
  }
}
`;
