@import 'scss/main';

mat-expansion-panel-header {
  padding: 0 23px 0 0;

  mat-panel-title {
    display: flex;
    align-items: center;

    span,
    i {
      color: white;
    }

    &:hover {
      i {
        color: $color-primary-100;
      }
    }
  }
}

a {
  &:hover,
  &:active {
    i {
      color: $color-primary-100;
    }
  }
}

.active-nav-link {
  i {
    color: $color-primary-100;
  }
}

.mat-expansion-panel:not(.mat-expanded):not([aria-disabled='true'])
  .mat-expansion-panel-header:hover {
  background: $color-primary-200;
}
