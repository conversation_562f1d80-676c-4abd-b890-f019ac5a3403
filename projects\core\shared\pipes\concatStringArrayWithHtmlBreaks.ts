import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'concatStringArrayWithHtmlBreaksPipe',
  standalone: true
})
export class ConcatStringArrayWithHtmlBreaksPipe implements PipeTransform {
  transform(value?: any, ...args: unknown[]): string {
    if (value && Array.isArray(value) && !!value.join) {
      return value.join('<br/>');
    }
    return '';
  }
}


@Pipe({
  name: 'emptyArrayString',
  standalone: true,
})
export class EmptyArrayStringPipe implements PipeTransform {
  transform(value?: any, ...args: unknown[]): boolean {
    if (value && Array.isArray(value)) {
      return value.length ? value.every((x) => x.length) : false;
    }
    return true;
  }
}
