import { Component, inject, Input, OnInit } from '@angular/core';
import { BillingPlan, tryPromise } from '@lms/core';
import { PaymentService } from '../payment.service';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { PaymentIntent, Stripe } from '@stripe/stripe-js';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import { TermsAndConditionsComponent } from './terms/terms.component';

@Component({
  selector: 'app-payment',
  imports: [
    CurrencyPipe,
    DatePipe,
    MatProgressBarModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  templateUrl: './payment.component.html',
})
export class PaymentComponent implements OnInit {
  paymentService = inject(PaymentService);
  readonly dialog = inject(MatDialog);

  @Input({ required: true }) data: BillingPlan[] = [];

  get amount() {
    return this.data.reduce((a, b) => a + b.price, 0) * 100;
  }

  currency = 'usd';
  stripe: Stripe;
  cardElement: any;
  isLoading = false;
  acceptedTerms = false;
  status: 'NONE' | 'SUCCESS' = 'NONE';
  error?: string;
  paymentIntent?: PaymentIntent;

  get sub() {
    return this.data.filter((p) => p.type === 'PLAN')[0];
  }

  async ngOnInit() {
    const stripe = await this.paymentService.stripePromise;
    if (!stripe) return;
    this.stripe = stripe;
    const elements = stripe.elements();
    this.cardElement = elements?.create('card');
    this.cardElement.mount('#card-element');
  }

  async handleOneTimePayment() {
    this.isLoading = true;
    const { data, error } = await this.paymentService.proceedPayment({
      amount: this.amount,
      currency: this.currency,
      cardElement: this.cardElement,
      stripe: this.stripe,
      organization: this.paymentService.state.user().organization!,
      plans: this.data,
    });
    this.isLoading = false;

    if (error) {
      this.error = error;
      return;
    }
    this.paymentIntent = data!;
    this.status = data?.status === 'succeeded' ? 'SUCCESS' : 'NONE';
  }

  async subscriptionPayment() {
    this.isLoading = true;
    const { data, error } = await this.paymentService.createSubscription({
      customerId: this.paymentService.state.mySubscription()?.paymentId,
      cardElement: this.cardElement,
      stripe: this.stripe,
      org: this.paymentService.state.user().organization!,
      plans: this.data,
    });
    this.isLoading = false;

    if (error) {
      this.error = error;
      return;
    }
    this.paymentIntent = data! as PaymentIntent;
    this.status = data?.status === 'succeeded' ? 'SUCCESS' : 'NONE';
  }

  openTerms() {
    this.dialog
      .open(TermsAndConditionsComponent, {
        minWidth: '600px',
        maxWidth: '800px',
        disableClose: true,
      })
      .afterClosed()
      .subscribe((result) => {
        this.acceptedTerms = result === 'ACCEPT';
      });
  }

  gotBack() {
    window.location.reload();
  }
}
