-- FUNCTION: public.handle_user_active_changes()

-- DROP FUNCTION IF EXISTS public.handle_user_active_changes();

CREATE OR REPLACE FUNCTION public.handle_user_active_changes()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
 begin
   if  old.active = true and  new.active = false then
      update auth.users set banned_until = (NOW() + INTERVAL '10 year') where email = old.email;
   end if;

   if  old.active = false and  new.active = true then
      update auth.users set banned_until = null where email = old.email;
   end if;

   return new;
end; 
$BODY$;

ALTER FUNCTION public.handle_user_active_changes()
    OWNER TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_user_active_changes() TO PUBLIC;

GRANT EXECUTE ON FUNCTION public.handle_user_active_changes() TO anon;

GRANT EXECUTE ON FUNCTION public.handle_user_active_changes() TO authenticated;

GRANT EXECUTE ON FUNCTION public.handle_user_active_changes() TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_user_active_changes() TO service_role;

