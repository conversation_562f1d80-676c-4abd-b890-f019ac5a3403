import { Injectable, inject, signal } from '@angular/core';
import {
  OrgSubscription,
  UserItem,
  defaultTrial,
  defaultUser,
} from '../models';
import { createClient, Session, SupabaseClient } from '@supabase/supabase-js';
import { ConfigurationLoader } from '../environment';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { from } from 'rxjs';
import { GraphQLClient } from 'graphql-request';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ToastMessageType } from '../utils';
import { ToastComponent } from '@lms/shared';
import { ScormClient } from 'scormcloud-client';

@Injectable({ providedIn: 'root' })
export class GlobalStateService {
  envConfig = inject(ConfigurationLoader);
  http = inject(HttpClient);
  snackBar = inject(MatSnackBar);

  user = signal<UserItem>(defaultUser);
  mySubscription = signal<OrgSubscription>(defaultTrial);
  session = signal<Session>({} as Session);
  isMenuOpened = signal(false);
  viewType = signal<'COURSE' | 'LESSON' | 'SETTINGS' | 'PREVIEW'>('COURSE');
  _supabaseClient: SupabaseClient;
  _graphQLClient: GraphQLClient;
  scormClient: ScormClient;

  get supabase() {
    return this._supabaseClient;
  }

  get graphql() {
    return this._graphQLClient;
  }

  get config() {
    return this.envConfig.env;
  }

  get teamGroupIds() {
    return {
      teams: (this.user()?.teams?.map((x) => x.teamId) ?? [])
        .filter(Boolean)
        .filter((x, v, _) => _.indexOf(x) === v),
      groups: (this.user()?.groups?.map((x) => x.groupId) ?? [])
        .filter(Boolean)
        .filter((x, v, _) => _.indexOf(x) === v),
    };
  }

  constructor() {
    this._supabaseClient = createClient(
      this.envConfig.env.supabaseUrl,
      this.envConfig.env.supabaseKey!
    );

    this._graphQLClient = new GraphQLClient(
      this.envConfig.env.supabaseUrl + '/graphql/v1',
      {
        headers: {
          apiKey: `${this.envConfig.env.supabaseKey}`,
        },
      }
    );
    const userAuth = this.envConfig.env.cloudApi['apiId'];
    const code = this.envConfig.env.cloudApi['apiKey'];
    if (!userAuth || !code) {
      throw new Error('CloudAPI appId and password are required');
    }
    this.scormClient = new ScormClient(userAuth, code, 'write');
  }

  openToast(data: { title?: string; message: string; type: ToastMessageType }) {
    this.snackBar.openFromComponent(ToastComponent, {
      data,
      horizontalPosition: 'right',
      verticalPosition: 'top',
      duration: 5000,
    });
  }

  query(queryDoc: any, variables = {}) {
    return this.graphql.request(queryDoc, variables);
  }

  isSessionExpired() {
    const expires_at = this.session()?.expires_at;
    const expired = Date.now() >= (expires_at ?? 0) * 1000;
    return expired;
  }

  getOptions$() {
    return from(this.getOptions());
  }

  async getOptions() {
    const token = await this.supabase.auth
      .getSession()
      .then((res) => res.data.session?.access_token);
    if (!token) {
      return null;
    }
    return {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + token,
      }),
    };
  }
}
