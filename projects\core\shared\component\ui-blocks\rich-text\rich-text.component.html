<form [formGroup]="f" class="form-lot-input mt-2">
  <div class="flex justify-between items-center">
    <label
      for="description"
      [class.error]="f.controls['description'].invalid && f.controls['description'].dirty"
    >
      {{label}}
    </label>
    <app-text-ai [isRichText]="true" (content)="getContent($event)" class="mb-2" />
  </div>
  <div class="field">
    <quill-editor
      formControlName="description"
      [styles]="editorStyle"
      formControlName="description"
      placeholder="Enter your description"
    />
  </div>

  @if(f.controls['description'].errors && f.controls['description'].invalid &&
  (f.controls['description'].dirty || f.controls['description'].touched)){
  <div class="error">
    @if (f.controls['description'].errors['required']) {
    <span>Description is required</span>
    }
  </div>
  }
</form>
