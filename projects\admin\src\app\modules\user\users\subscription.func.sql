CREATE OR REPLACE FUNCTION get_my_info(filter json)
RETURNS json AS $function$
DECLARE 
    user_record record;
    count_record int;
    _user_id uuid := (filter->>'userId')::uuid;
BEGIN

        SELECT  count(*) into count_record
        FROM public.users
        WHERE organization::text = filter->>'organization'::text;

        WITH user_teams AS (
            SELECT
                ug.user as user_id,
                COALESCE(
                    json_agg(json_build_object(
                        'userGroupId', ug.id, 
                        'teamId', t.id, 
                        'teamName', t.name, 
                        'groupId', g.id, 
                        'groupName', g.name
                        )) FILTER (WHERE ug.id IS NOT NULL),
                    '[]'::json
                ) AS team_groups
            FROM public.user_groups ug 
                LEFT JOIN public.teams t ON t.id = ug.team
                LEFT JOIN public.groups g ON g.id = ug.group
            WHERE ug.user = _user_id
            GROUP BY ug.user
        )
        SELECT array_agg(
            json_build_object(
                'user',ur.user,
                'subscription', ur.sub,
                'organization', ur.org,
                'plan', ur.plan,
                'teams', ur.team
                )
        ) INTO user_record
        FROM (
            select 
                to_jsonb(u.*) as user, 
                to_jsonb(org.*) as org, 
                to_jsonb(sub.*) as sub, 
                to_jsonb(pl.*) as plan, 
                ut.team_groups as team
            from public.users u
                inner join public.organizations org on org.id = u.organization
                inner join public.org_subscription sub on sub.organization = org.id and (sub.status = 'ACTIVE' or sub.current = 1)
                inner join public.billing_plans pl on pl.id = sub.plan
                left join user_teams ut on ut.user_id = u.id
            where u.id  = _user_id
        ) ur;

    RETURN json_build_object(
        'used_seat_count', count_record,
        'data', user_record
    );
END;
$function$
LANGUAGE plpgsql;
