<table class="w-full divide-y divide-gray-300">
  <thead>
    <tr>
      <th
        scope="col"
        class="py-3.5 pr-3 pl-4 text-left text-sm font-normal italic text-lot-dark-gray sm:pl-3"
      >
        Name
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Email Address
      </th>
      @if (type === 'USER') {
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Team
      </th>
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Permission Level
      </th>
      } @if (type === 'TEAM') {
      <th
        scope="col"
        class="px-3 py-3.5 text-left text-sm font-normal italic text-lot-dark-gray"
      >
        Role
      </th>
      }
      <th scope="col" class="relative py-3.5 pr-4 pl-3 sm:pr-3"></th>
    </tr>
  </thead>
  <tbody class="bg-white">
    @for (item of data; track $index) {
    <tr
      class="even:bg-gray-50 group text-lot-dark"
      [class.font-semibold]="item.self"
    >
      <td
        class="py-4 pr-3 pl-4 text-base font-medium whitespace-nowrap sm:pl-3"
      >
        @if (item.self) {
        <span class="text-lot-blue mr-2">[You]</span>
        } @if (!item.username ||item.username.includes('PENDING')) {
        <a
          href="javascript:void(0)"
          (click)="action('edit', item)"
          class="text-lot-blue underline italic"
        >
          pending
        </a>
        } @else {
        {{ item.username }}
        }
      </td>
      <td class="px-3 py-4 text-base whitespace-nowrap">
        {{ item.email }}
      </td>
      @if (type === 'USER') {
      <td class="px-3 py-4 text-base whitespace-nowrap">
        @if (item.teams?.length) {
        <a
          href="javascript:void(0)"
          class="flex items-center gap-1"
          [matMenuTriggerFor]="menu"
        >
          {{
            (item.teams || [])[0].teamName +
              " " +
              ((item.teams?.length || 0) > 1
                ? "(" + (item.teams?.length || 0) + ")"
                : "")
          }}
          <mat-icon>keyboard_arrow_down</mat-icon>
        </a>
        <mat-menu #menu="matMenu">
          @for (team of item.teams; track $index) {
          <div
            class="border-gray-200 border-b w-[200px] justify-start py-0 px-5 font-[500] text-lg leading-10 text-lot-dark flex items-center gap-2"
          >
            {{ team.teamName }}
          </div>
          }
          <a
          class="border-gray-200 w-[200px] justify-start py-2 px-5 font-[500] text-lg leading-10 text-lot-blue flex items-center gap-2"
          href="javascript:void(0)"
          (click)="action('team', item)"
        >
          <i class="fa-solid fa-plus"></i>
          Assign
        </a>
        </mat-menu>
        }@else {
        <a
          href="javascript:void(0)"
          (click)="action('team', item)"
          class="text-lot-blue underline"
        >
          Assign
        </a>
        }
      </td>
      <td class="px-3 py-4 text-base font-medium whitespace-nowrap">
        {{ item.role }}
      </td>
      } @if (type === 'TEAM') {
      <td class="px-3 py-4 text-base font-medium whitespace-nowrap">
        {{ item.role }}
      </td>
      }
      <td
        class="py-4 pr-4 pl-3 text-sm sm:pr-3 flex items-center justify-end gap-8"
      >
        <a
          href="javascript:void(0)"
          (click)="action('edit', item)"
          class="text-lot-dark-gray/30 group-hover:text-lot-blue/70 hover:text-lot-blue text-xl"
        >
          <i class="fa-solid fa-pen-to-square"></i>
        </a>
        <a
          href="javascript:void(0)"
          (click)="action('delete', item)"
          [class]="
            item.self
              ? 'pointer-events-none text-lot-dark-gray/30 text-xl'
              : 'text-lot-dark-gray/30 group-hover:text-lot-danger/70 hover:text-lot-danger text-xl'
          "
        >
          <i class="fa-solid fa-trash"></i>
        </a>
      </td>
    </tr>
    }
  </tbody>
</table>
<mat-paginator
  [length]="total"
  [pageSize]="pageSize"
  class="border-t"
  (page)="action('page', data[0], $event)"
/>
