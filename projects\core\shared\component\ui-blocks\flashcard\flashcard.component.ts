import {
  Component,
  ComponentRef,
  Input,
  ViewChild,
  ViewContainerRef,
  AfterViewInit,
  OnDestroy,
  EventEmitter,
  Output,
  inject,
  Type,
} from '@angular/core';
import { LessonWidgetTypes, UIBlockData, WidgetType } from '../ui-block.utils';

@Component({
  selector: 'app-block-flashcard',
  template: `
    <div
      class="flex flex-col gap-4 h-full rounded-lg {{ metaClass }}"
      [style]="styleForm"
    >
      <div
        class="relative cursor-pointer h-full flex flex-col border-t-4 border-lot-blue hover:border-t-8 transform transition duration-300 hover:scale-105 rounded-lg"
        (click)="toggleFlip()"
      >
        <span
          class="absolute top-2 right-3 text-3xl z-10"
          [class.hidden]="!visited"
          [class.text-lot-ai]="visited"
        >
          <i class="fa-solid fa-circle-check"></i>
        </span>
        <span class="absolute text-lot-blue bottom-4 right-4 text-xl z-10">
          <i class="fa-solid fa-arrows-rotate"></i>
        </span>

        <div
          class="w-full h-full bg-white rounded-lg shadow-md p-4 transition-transform duration-500"
          [style.transform]="isFlipped ? 'rotateY(180deg)' : 'rotateY(0)'"
          [class.pointer-events-none]="isFlipped"
        >
          <ng-container #frontContainer class="h-full"></ng-container>
        </div>

        <div
          class="absolute top-0 left-0 w-full h-full bg-white rounded-lg shadow-md p-4 transition-transform duration-500"
          [style.transform]="isFlipped ? 'rotateY(0)' : 'rotateY(-180deg)'"
          [class.pointer-events-none]="!isFlipped"
        >
          <ng-container #backContainer class="h-full"></ng-container>
        </div>
      </div>

      @if (content && content.showButton) {
      <div class="flex justify-center mt-4">
        <button
          (click)="toggleFlip()"
          type="button"
          class="px-4 py-2 text-lot-blue hover:bg-lot-blue/10 rounded-lg transition-colors"
        >
          {{ isFlipped ? 'Show Front' : 'Show Back' }}
        </button>
      </div>
      }
    </div>
  `,
  styles: [
    `
      :host {
        display: block;
        perspective: 1000px;
      }

      .absolute {
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transform-style: preserve-3d;
      }

      .transition-transform {
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transform-style: preserve-3d;
      }

      .pointer-events-none {
        pointer-events: none;
      }
    `,
  ],
})
export class UIBlockFlashcardComponent implements AfterViewInit, OnDestroy {
  viewContainerRef = inject(ViewContainerRef);

  @Input() content: {
    front: {
      type: WidgetType;
      content: any;
    };
    back: {
      type: WidgetType;
      content: any;
    };
    showButton?: boolean;
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };
  @Output() view = new EventEmitter<UIBlockData>();

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-0'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  @ViewChild('frontContainer', { read: ViewContainerRef })
  frontContainer!: ViewContainerRef;

  @ViewChild('backContainer', { read: ViewContainerRef })
  backContainer!: ViewContainerRef;

  isFlipped = false;
  visited = false;

  private componentRefs: ComponentRef<any>[] = [];

  ngAfterViewInit() {
    this.renderComponents();
  }

  ngOnDestroy() {
    this.clearComponents();
  }

  private renderComponents() {
    this.clearComponents();

    const content = this.content;

    const componentFrontType = LessonWidgetTypes.filter(
      (x) => x.id === content.front.type
    )[0].component as any;
    const componentBackType = LessonWidgetTypes.filter(
      (x) => x.id === content.back.type
    )[0].component as any;

    const frontComponent = this.createComponent(
      this.frontContainer,
      componentFrontType
    );
    frontComponent.setInput('content', content.front.content);

    const backComponent = this.createComponent(
      this.backContainer,
      componentBackType
    );
    backComponent.setInput('content', content.back.content);
  }

  private createComponent(
    container: ViewContainerRef,
    component: Type<any>
  ): ComponentRef<any> {
    const componentRef = container.createComponent(component);
    this.componentRefs.push(componentRef);
    return componentRef;
  }

  toggleFlip() {
    this.visited = true;
    this.isFlipped = !this.isFlipped;
    this.view.emit({
      widgetType: WidgetType.FLASHCARD,
      content: this.content,
      userAnswers: { completed: true },
    });
  }

  private clearComponents() {
    this.componentRefs.forEach((ref) => ref.destroy());
    this.componentRefs = [];
    this.frontContainer.clear();
    this.backContainer.clear();
  }
}

// export const getFlashcardTemplate = (
//   type: number,
//   viewContainerRef?: ViewContainerRef
// ) => {
//   viewContainerRef = viewContainerRef || inject(ViewContainerRef);
//   const backComponent = viewContainerRef.createComponent(UIBlockTextComponent);
//   backComponent.setInput('content', {
//     description:
//       '@Component decorator defines a class as an Angular component and provides metadata about the component. It is used to configure the component and its behavior. It is used to configure the component and its behavior.',
//   });
//   switch (type) {
//     case 0:
//       const frontComponent =
//         viewContainerRef.createComponent(UIBlockTextComponent);
//       frontComponent.setInput('content', {
//         heading: 'Web Development Fundamentals',
//         description:
//           'Learn the basics of web development with HTML, CSS, and JavaScript',
//       });
//       return {
//         front: {
//           component: frontComponent,
//           type: WidgetType.TEXT,
//         },
//         back: {
//           component: backComponent,
//           type: WidgetType.TEXT,
//         },
//       };
//     default:
//       const frontComponent1 = viewContainerRef.createComponent(
//         UIBlockImageComponent
//       );
//       frontComponent1.setInput('content', {
//         url: 'https://picsum.photos/800/450',
//         caption: 'Web Development Fundamentals',
//         description:
//           'Learn the basics of web development with HTML, CSS, and JavaScript',
//         position: 'center',
//         textPosition: 'bottom',
//       });
//       return {
//         front: { component: frontComponent1, type: WidgetType.IMAGE },
//         back: { component: backComponent, type: WidgetType.TEXT },
//       };
//   }
// };
