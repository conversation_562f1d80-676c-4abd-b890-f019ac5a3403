import { Application, Router } from 'https://deno.land/x/oak@v17.1.4/mod.ts';
import { Stripe } from 'https://esm.sh/stripe@v12.0.0?target=deno';
import { oakCors } from 'https://deno.land/x/cors@v1.2.2/mod.ts';

const stripeKey = Deno.env.get('STRIPE_SECRET_KEY');
if (!stripeKey) {
  throw new Error('STRIPE_SECRET_KEY environment variable is not set');
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY'), {
  apiVersion: '2023-08-16',
});

const app = new Application();
const router = new Router();

app.use(
  oakCors({
    origin: 'http://localhost:3000',
    methods: ['GET', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  })
);

// Middleware to parse JSON
app.use(async (ctx, next) => {
  if (ctx.request.hasBody) {
    const value = await ctx.request.body.json();
    ctx.state.body = value;
  }
  await next();
});

router.post('/create-payment-intent', async (ctx) => {
  const { amount, currency, description } = ctx.state.body;

  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      description,
    });
    const error = paymentIntent.last_payment_error;
    let message = error?.message;
    switch (error?.type) {
      case 'card_error':
        message = `A payment error occurred: ${error.message}`;
        break;
      case 'invalid_request_error':
        message = 'An invalid request occurred.';
        if (error.param) {
          message = `The parameter ${error.param} is invalid or missing.`;
        }
        break;
      default:
        break;
    }
    
    if (paymentIntent.client_secret) {
      ctx.response.body = { data: paymentIntent.client_secret };
      return;
    }
    ctx.response.body = { data: null, error: message };
  } catch (e) {
    ctx.response.status = 200;
    let item = {
      data: null,
      error: 'An unknown error occurred',
    };
    console.log(e);
    switch (e.type) {
      case 'StripeCardError':
        item.error = `A payment error occurred: ${e.message}`;
        break;
      case 'StripeInvalidRequestError':
        item.error = 'An invalid request occurred.';
        break;
      default:
        item.error = 'Another problem occurred, maybe unrelated to Stripe.';
        break;
    }
    ctx.response.body = item;
  }
});

// Verify if a customer exists
router.post('/verify-customer', async (ctx) => {
  const { email } = ctx.state.body;

  try {
    const customers = await stripe.customers.list({ email, limit: 1 });
    ctx.response.body = {
      data: customers.data.at(0),
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Create a subscription
router.post('/create-subscription', async (ctx) => {
  const { customerId, priceId, frequency, paymentMethodId  } = ctx.state.body;

  const now = new Date();
  const currentDate = now.getDay();
  const currentMonth = now.getMonth() + 1;

  try {

     if (paymentMethodId) {
       await stripe.paymentMethods.attach(paymentMethodId, {
         customer: customerId,
       });

       await stripe.customers.update(customerId, {
         invoice_settings: {
           default_payment_method: paymentMethodId,
         },
       });
     }

    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      expand: ['latest_invoice.payment_intent'],
      payment_behavior: 'default_incomplete', // Important for SCA
      billing_cycle_anchor_config: ['YEARLY', 'YEARLY3'].includes(frequency)
        ? {
            month: currentMonth,
            day_of_month: currentDate,
          }
        : {
            day_of_month: currentDate,
          },
    });

    ctx.response.body = {
      data: {
        subscription,
        requiresAction:
          subscription.status === 'incomplete' &&
          subscription.latest_invoice.payment_intent.status ===
            'requires_action',
      },
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

router.post('/subscriptions', async (ctx) => {
  const { customerId } = ctx.state.body;

  try {
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
    });
    ctx.response.body = { data: subscriptions.data };
  } catch (error: unknown) {
    ctx.response.status = 200;
    ctx.response.body = {
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Get billing history for a customer
router.post('/billing-history', async (ctx) => {
  const { customerId } = ctx.state.body;

  try {
    const invoices = await stripe.invoices.list({ customer: customerId });
    ctx.response.body = { data: invoices.data };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Unsubscribe a customer
router.post('/unsubscribe', async (ctx) => {
  const { subscriptionId } = ctx.state.body;

  try {
    const subscription = await stripe.subscriptions.del(subscriptionId);
    ctx.response.body = { data: subscription };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Unsubscribe a customer
// router.post('/unsubscribe', async (ctx) => {
//   const { subscriptionId } = ctx.state.body;

//   try {
//     const subscription = await stripe.payments.;
//     ctx.response.body = { data: subscription };
//   } catch (error) {
//     ctx.response.status = 200;
//     ctx.response.body = {
//       data: null,
//       error:
//         error instanceof Error ? error.message : 'An unknown error occurred',
//     };
//   }
// });

// Create a new customer
router.post('/create-customer', async (ctx) => {
  const { email, name, phone, metadata } = ctx.state.body;

  try {
    const customer = await stripe.customers.create({
      email,
      name,
      phone,
      metadata,
    });

    ctx.response.body = {
      data: customer,
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Create a new price
router.post('/create-price', async (ctx) => {
  const { amount, currency, recurring, product, nickname } = ctx.state.body;

  try {
    const price = await stripe.prices.create({
      unit_amount: amount * 100, // amount in cents
      currency,
      recurring: recurring
        ? {
            interval: recurring.interval, // 'day', 'week', 'month' or 'year'
            interval_count: recurring.interval_count || 1,
          }
        : null,
      product_data: {
        id: product,
        name: nickname,
      },
      // product, // product ID or name
      nickname: product + ' - ' + nickname, // optional friendly name
    });

    ctx.response.body = {
      data: price,
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Update an existing price
router.post('/update-price', async (ctx) => {
  const { nickname, active, metadata, priceId, amount } = ctx.state.body;

  try {
    // Note: Stripe only allows updating metadata, nickname, and active status
    const price = await stripe.prices.update(priceId, {
      nickname,
      active,
      metadata,
      unit_amount: amount * 100, // amount in cents
    });

    ctx.response.body = {
      data: price,
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// List all prices
router.post('/prices', async (ctx) => {
  const { active, type, currency } = ctx.state.body;

  try {
    const prices = await stripe.prices.list({
      active: active === 'true',
      type: type || 'recurring', // 'recurring' or 'one_time'
      currency,
      limit: 100,
      expand: ['data.product'],
    });

    ctx.response.body = {
      data: prices.data,
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

// Get a specific price
router.post('/prices', async (ctx) => {
  const { priceId } = ctx.state.body;

  try {
    const price = await stripe.prices.retrieve(priceId, {
      expand: ['product'],
    });

    ctx.response.body = {
      data: price,
    };
  } catch (error) {
    ctx.response.status = 200;
    ctx.response.body = {
      data: null,
      error:
        error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
});

app.use(router.routes());
app.use(router.allowedMethods());

console.log('Server running on http://localhost:8000');
await app.listen({ port: 8000 });
