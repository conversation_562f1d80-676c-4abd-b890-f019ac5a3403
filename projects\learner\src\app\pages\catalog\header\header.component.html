<div class="flex items-center gap-4">
  <app-floating
    [with]="'fit'"
    [title]="'Sort By'"
    [options]="sortBy"
    (selected)="sort.emit($event.id)"
  />
  <app-floating
    [with]="'fit'"
    [title]="'View As'"
    [options]="viewAs"
    (selected)="view.emit($event.id)"
  />
  <div class="flex-1">
    <div class="rounded-md border border-lot-blue flex gap-2 px-5 py-2.5">
      <span class="material-symbols-outlined text-lot-blue">search</span>
      <input
        type="search"
        name="search"
        id="search"
        (input)="search.emit($any($event.target).value)"
        class="bg-transparent border-0 w-full text-lot-blue placeholder:text-lot-blue outline-none pl-1"
        placeholder="Search by Name"
      />
    </div>
  </div>
</div>
