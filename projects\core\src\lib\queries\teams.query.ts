export const teamsGroupQuery = `
query getTeams($organizationId: UUID!) {
  teamsCollection(offset: 0, filter: {
    organization: {
      eq: $organizationId
    }
  }){
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    edges {
      node {
        id
        name
        organizations {
          id
          name
        }
        manager
        size
        metadata
        created_by
        created_at
        updated_by
        updated_at
        groupsCollection {
          edges {
            node {
              id
              name
              isdefault
            }
          }
        }
      }
    }
  }
    user_groupsCollection(offset: 0, filter: {
    organization: {
      eq: $organizationId
    }
  }) {
    edges {
      node {
        user
        users {
          id
          email
          role
          lastname
          firstname
        }
        team
        group
      }
    }
  }
}
`;

export const teamsQuery = `
query getTeams($organizationId: UUID!) {
  teamsCollection(offset: 0, filter: {
    organization: {
      eq: $organizationId
    }
  }){
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    edges {
      node {
        id
        name
        organizations {
          id
          name
        }
        manager
        size
        metadata
        created_by
        created_at
        updated_by
        updated_at
        groupsCollection {
          edges {
            node {
              id
              name
              isdefault
            }
          }
        }
      }
    }
  }
}
`;