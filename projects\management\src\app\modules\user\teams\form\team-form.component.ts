import { NgTemplateOutlet } from '@angular/common';
import { Component, inject, OnInit, resource } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormArray,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatChipsModule } from '@angular/material/chips';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import {
  getId,
  GroupItem,
  ITeamGroupItem,
  Team,
  TeamCoreService,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { markControlsDirty } from '@lms/core';
import { debounceTime, startWith, switchMap } from 'rxjs';

@Component({
  selector: 'app-team-form',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    MatDialogModule,
    NgTemplateOutlet,
    MatSelectModule,
    MatChipsModule,
    MatIconModule,
  ],
  templateUrl: 'team-form.component.html',
})
export class TeamFormComponent implements OnInit {
  readonly service = inject(TeamCoreService);
  userService = inject(UsersCoreService);
  public dialogRef: MatDialogRef<TeamFormComponent> = inject(MatDialogRef);

  data: {
    item?: GroupItem;
    user?: UserItem;
    isTeam?: boolean;
    type: 'TEAM' | 'GROUP' | 'ASSIGN' | 'ASSIGN_GROUP';
  } = inject(MAT_DIALOG_DATA);

  view = this.data.type;
  isLoading = false;
  error?: string;

  form = new FormGroup({
    name: new FormControl('', Validators.required),
    groups: new FormArray([
      new FormGroup({
        name: new FormControl(''),
        error: new FormControl(false),
      }),
    ]),
  });

  // users: (UserItem & { enrollId: string })[] = [];
  userControl = new FormControl('');
  userOptions = toSignal(
    this.userControl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) =>
        this.userService.getUserOnlyByQuery(value || '').then((res) =>
          res.data
            .map(
              (x) =>
                ({
                  userGroupId: '',
                  userId: x.id,
                  teamId: this.data.isTeam ? this.data.item?.id : getId(this.data.item?.team),
                  teamName: this.data.item?.name,
                  groupId: !this.data.isTeam ? this.data.item?.id : undefined,
                  groupName: this.data.item?.name,
                  userName: x.name,
                } as ITeamGroupItem)
            )
            .filter(
              (user) => !this.users.some((ex) => ex.userId === user.userId)
            )
        )
      )
    )
  );

  displayFn = (item: any) => item?.name || '';

  get f() {
    return this.form.controls;
  }

  get groups() {
    return this.form.controls['groups'] as FormArray;
  }

  get title() {
    return this.view === 'TEAM'
      ? (this.data.item?.id ? 'Edit ' : 'Create a New ') + 'Team'
      : this.data.item?.id
      ? 'Edit Group '
      : 'Create a Group for ';
  }

  teamSource = resource({
    loader: () => this.service.queryTeams(),
  });

  userTeams: ITeamGroupItem[] = [];
  userGroups: ITeamGroupItem[] = [];
  users: (ITeamGroupItem & { tag: string })[] = [];

  groupOptions: GroupItem[] = [];

  ngOnInit(): void {
    if (this.data.item) {
      this.form.patchValue({
        name: this.data.item.name,
      });
      if (this.data.type === 'TEAM') {
        this.groups.clear();
        for (const group of (this.data.item as Team).groups || []) {
          this.groups.push(
            new FormGroup({
              name: new FormControl(group.name),
              error: new FormControl(false),
            })
          );
        }
      }

      if (this.data.type === 'ASSIGN_GROUP') {
        this.service
          .getUsersAssignedByType(this.data.item.id, this.data.isTeam!)
          .then((res) => {
            this.userGroups = res.data;
          });
      }
    }
  }

  addGroup(form: any) {
    if (!form.get('name')?.value?.trim()) {
      form.get('error')?.setValue(true);
      return;
    }
    form.get('error')?.setValue(false);
    this.groups.push(
      new FormGroup({
        name: new FormControl(''),
        error: new FormControl(false),
      })
    );
  }

  removeGroup(index: number) {
    this.groups.removeAt(index);
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.isLoading = true;
    if (this.view === 'TEAM') {
      const team = await this.executeSaveTeam();
      this.data.item = team as GroupItem;
      this.dialogRef.close();
      this.service.teamSource.reload();
    }
    if (this.view === 'GROUP') {
      const team = this.data.item ?? this.service.viewTeam();
      await this.executeSaveGroup(team as Team);
      this.dialogRef.close();
      this.service.teamSource.reload();
    }
  }

  async saveGroupNew() {
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const team = await this.executeSaveTeam();
    this.service.viewTeam.set(team as Team);
    this.data.item = team as GroupItem;
    this.view = 'GROUP';
  }

  async executeSaveTeam() {
    const payload = {
      name: this.form.value.name,
    } as any;
    if (this.data.item?.id) {
      payload['id'] = this.data.item?.id;
    } else {
      payload['organization'] = getId(this.service.organization);
    }
    const res = await this.service.saveTeam([payload]);
    this.isLoading = false;
    if (res.error.length) {
      this.error = res.error[0];
      return;
    }
    return res.data[0];
  }

  async executeSaveGroup(team: Team) {
    this.isLoading = true;
    const groups = this.form
      .getRawValue()
      .groups.filter((x: any) => !!x.name)
      .map(
        (x: any) =>
          ({
            name: x.name,
            organization: getId(this.service.organization),
            isdefault: false,
            team: team?.id,
          } as GroupItem & any)
      );
    const res = await this.service.saveGroup(groups);
    this.isLoading = false;
    if (res.error.length) {
      this.error = res.error[0];
      return;
    }
  }

  onTeam(id: string, teams: Team[], index: number) {
    const team = teams.filter((x) => x.id === id)[0];
    this.groupOptions = (team.groups || []).map(
      (x) => ({ ...x, team: { id } } as GroupItem)
    );

    if (this.userTeams.some((x) => x.teamId === team.id)) return;

    this.userTeams.push({
      teamId: team.id,
      teamName: team.name,
    } as any);
    if (index > -1) {
      this.groups.controls[index].get('group')?.enable();
    }
  }

  onGroup(ids: string[], groups: GroupItem[]) {
    if (!ids.length) return;
    const selectedGroups = groups.filter((x) => ids.includes(x.id));

    const newGroups = selectedGroups.filter(
      (group) => !this.userGroups.some((x) => x.groupId === group.id)
    );

    this.userGroups.push(
      ...newGroups.map(
        (group) =>
          ({
            teamId: getId(group.team),
            groupId: group.id,
            groupName: group.name,
          } as any)
      )
    );
  }

  removeChips(id: string, type: 'TEAM' | 'GROUP') {
    if (type === 'TEAM') {
      const teamIndex = this.userTeams.findIndex((x) => x.teamId === id);
      if (teamIndex > -1) {
        this.userTeams.splice(teamIndex, 1);
      }
    } else if (type === 'GROUP') {
      const groupIndex = this.userGroups.findIndex((x) => x.groupId === id);
      if (groupIndex > -1) {
        this.userGroups.splice(groupIndex, 1);
      }
    }
  }

  async onSubmitAssignTeamGroup() {
    this.error = undefined;
    if (this.isLoading || !this.data.user) return;

    this.isLoading = true;
    const myGroups = this.userGroups.map((group) => {
      const item = {
        user: getId(this.data.user),
        organization: getId(this.service.organization),
        team: group.teamId,
        group: group.groupId,
      } as any;
      return item;
    });

    const myTeams = this.userTeams.filter(
      (x) => !myGroups.find((y) => y.team === x.teamId)
    );

    myGroups.push(
      ...myTeams.map((team) => {
        const item = {
          user: getId(this.data.user),
          organization: getId(this.service.organization),
          team: team.teamId,
        } as any;
        return item;
      })
    );

    if (!myGroups.length) {
      this.isLoading = false;
      this.dialogRef.close();
      return;
    }

    const res2 = await this.service.assignUsers({
      news: myGroups,
      olds: [],
    });
    this.isLoading = false;

    if (res2.length) {
      this.error = res2.join(', ');
      return;
    }
    this.dialogRef.close(1);
    this.userService.userSource.reload();
  }

  selectUser(item: ITeamGroupItem, action: 'ADD' | 'REMOVE' | 'CLEAR') {
    if (
      action === 'ADD' &&
      !this.userGroups.some((x) => x.userId === item.userId)
    ) {
      this.users.push({ ...item, tag: 'new' });
    }
    if (action === 'REMOVE') {
      this.userGroups = this.userGroups.filter((x) => x.userId !== item.userId);
      this.users.push({ ...item, tag: 'delete' });
    }
    if (action === 'CLEAR') {
      this.users = this.users.filter((x) => x.userId !== item.userId);
    }
  }

  async bulkAssignUsers() {
    this.error = undefined;
    if (this.isLoading || !this.users.length) return;

    this.isLoading = true;
    const newItems = this.users
      .filter((x) => x.tag === 'new')
      .map((u) => {
        const item = {
          user: u.userId,
          organization: getId(this.service.organization),
          team: u.teamId,
          group: u.groupId,
        } as any;
        return item;
      });

    const res2 = await this.service.assignUsers({
      news: newItems,
      olds: this.users
        .filter((x) => x.tag === 'delete')
        .map((x) => x.userGroupId)
        .filter(Boolean),
    });
    this.isLoading = false;

    if (res2.length) {
      this.error = res2.join(', ');
      return;
    }
    this.dialogRef.close(1);
    this.service.teamSource.reload();
  }
}
