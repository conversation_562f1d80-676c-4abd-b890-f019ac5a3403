
https://workos.com/blog/source-sso-solutions
https://www.permit.io/blog/top-12-open-source-auth-tools

https://github.com/Turistforeningen/leaflet-routing

https://www.youtube.com/watch?v=F8dnYNTncoU
https://github.com/ruvictor/map-app-directions/blob/master/app.js

https://github.com/bluehalo/ngx-leaflet

https://javascript.plainenglish.io/a-simplified-journey-of-leaflet-js-integration-with-angular-step-by-step-guide-b223f19b71f0
https://www.digitalocean.com/community/tutorials/angular-angular-and-leaflet

https://www.pgsclusters.com/postgres-cloud

TODO

https://tempmailo.com/

https://temp-mail.io/en

https://www.relume.io/

https://www.figma.com/design/Sny93GSu1qugzAdekAvbBl/Personal-Portfolio-Website-Template-(Community)?node-id=7-191&t=YhjL7euRYJd2RApV-0

https://www.figma.com/community/portfolio-templates?resource_type=mixed&editor_type=all&price=all&sort_by=all_time&creators=all

https://nativenotify.com/

https://lovable.dev/

https://docs.sheetjs.com/docs/demos/frontend/angular/


write a function that receives an array of 2 parameters payload and existing items and each params has the same type {id:string;user:string;team:string;} and returns an object of with two properties, new items and delete items of the same type. the result should have only records combine where the records shouldn't exist in existings items and if not exist in payload then mark them as delete items


Shortcuts:

Ctrl+k  Ctrl+0    = Collapse All
Ctrl+k  Ctrl+l   = collapse single


Ctrl+k  Ctrl+j   = unflold single
Ctrl+k  Ctrl+l   = collapse single
