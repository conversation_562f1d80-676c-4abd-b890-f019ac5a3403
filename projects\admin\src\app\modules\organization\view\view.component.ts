import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService, Organization, OrganizationService } from '@lms/core';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { firstValueFrom } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { OrgFormComponent } from '../form/form.component';

@Component({
    selector: 'app-org-view',
    imports: [
        CommonModule,
        MatTableModule,
        MatPaginatorModule,
        ReactiveFormsModule,
    ],
    templateUrl: './view.component.html',
    styleUrls: ['./view.component.scss']
})
export class OrgViewComponent implements OnInit {
  searchKey = new FormControl('');
  displayedColumns: string[] = ['name', 'description', 'nbUser', 'action'];

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  dataSource = new MatTableDataSource<Organization>();

  constructor(
    private orgService: OrganizationService,
    private notification: NotificationService,
    public dialog: MatDialog
  ) {}

  async ngOnInit(): Promise<void> {
    await this.loadData();
    this.searchKey.valueChanges.subscribe((res) => {
      this.dataSource.filter = res?.trim()?.toLowerCase() ?? '';
      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    });
  }

  async loadData(): Promise<void> {
    const data = (await this.orgService.get()).data;
    this.dataSource = new MatTableDataSource(data);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  async openForm(item: Organization | null) {
    const result = await firstValueFrom(
      this.dialog
        .open(OrgFormComponent, {
          width: '550px',
          data: item,
        })
        .afterClosed()
    );

    if (result?.payload) {
      const { error } = await this.orgService.saveOrganization(result?.payload);
      debugger;
      if (error) {
        this.notification.error('Organization saving failed: ' + error);
      } else {
        this.notification.success('Organization is saved successfully.');
        await this.loadData();
      }
    }
  }
}
