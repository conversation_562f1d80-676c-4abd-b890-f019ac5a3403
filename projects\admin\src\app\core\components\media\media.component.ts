import { NgTemplateOutlet } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FileLoaderComponent, FileType } from '@lms/shared';
import { MatDialogModule } from '@angular/material/dialog';
import { MediaBucket, MediaCenterService } from '@lms/core';

@Component({
  selector: 'app-media-center',
  standalone: true,
  imports: [FileLoaderComponent, NgTemplateOutlet, MatDialogModule],
  templateUrl: './media.component.html',
})
export class MediaCenterComponent {
  service = inject(MediaCenterService);

  @Input() multiple = false;
  @Input() selectedType: 'image' | 'video' | 'document' = 'image';
  @Output() selected = new EventEmitter<any>();

  selectedItems: any[] = [];
  currentTab = 0;
  mediaFile: File | null = null;
  previewUrl?: string | ArrayBuffer | null = null;
  error?: string;

  view: 'FILE' | 'UPLOAD' = 'FILE';

  tab: FileType = 'image';
  tabs = [
    { id: 'image' as FileType, name: 'Images', icon: 'fa fa-image' },
    { id: 'video' as FileType, name: 'Videos', icon: 'fa fa-video' },
    { id: 'audio' as FileType, name: 'Audios', icon: 'fa fa-music' },
    { id: 'doc' as FileType, name: 'Documents', icon: 'fa fa-file' },
  ];

  resources = [
    // Sample data structure - replace with your actual data
    {
      id: 1,
      type: 'image',
      url: 'assets/images/new/card-3.jpeg',
      name: 'Image 1',
    },
    // { id: 2, type: 'video', url: 'path/to/video1.mp4', name: 'Video 1' },
    // { id: 3, type: 'document', url: 'path/to/doc1.pdf', name: 'Document 1' },
  ];

  filterResources(type: string) {
    return this.resources.filter((resource) => resource.type === type);
  }

  isSelected(item: any): boolean {
    return this.selectedItems.some((selected) => selected.id === item.id);
  }

  toggleSelection(item: any) {
    if (this.multiple) {
      const index = this.selectedItems.findIndex(
        (selected) => selected.id === item.id
      );
      if (index === -1) {
        this.selectedItems.push(item);
      } else {
        this.selectedItems.splice(index, 1);
      }
    } else {
      this.selectedItems = [item];
      this.confirmSelection();
    }
  }

  confirmSelection() {
    this.selected.emit(
      this.multiple ? this.selectedItems : this.selectedItems[0]
    );
  }

  onTabChange(index: number) {
    this.currentTab = index;
    this.selectedItems = [];
  }

  async onFileUploaded(files: File[]) {
    // Implement your file upload logic here
    // You might want to use a service to handle the upload
    this.mediaFile = files[0];
    this.generatePreview();
  }

  async save() {
    if (!this.mediaFile) return;
    this.selected.emit(this.mediaFile);

    const bucket = this.getBucketName(this.selectedType);
    const folder = this.service.state
      .user()
      .organization?.name.replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    const res = await  this.service.uploadFile(this.mediaFile, bucket);

    // const res = await this.service.uploadMultipleFiles(
    //   [this.mediaFile],
    //   bucket,
    //   folder
    // );

    // if (res.errors.length) {
    //   this.error = res.errors.join(', ');
    //   return;
    // }

    console.log(res);
  }

  generatePreview(): void {
    if (!this.mediaFile) return;

    const reader = new FileReader();
    reader.onload = () => {
      this.previewUrl = reader.result;
    };
    reader.readAsDataURL(this.mediaFile);
  }

  getBucketName(type: 'image' | 'video' | 'document') {
    switch (type) {
      case 'image':
        return MediaBucket.IMAGES;
      case 'video':
        return MediaBucket.VIDEOS;
      case 'document':
        return MediaBucket.DOCUMENTS;
      default:
        return MediaBucket.MEDIA;
    }
  }
}
