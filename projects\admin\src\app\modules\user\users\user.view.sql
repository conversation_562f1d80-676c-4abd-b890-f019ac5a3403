DROP VIEW users_view;
CREATE VIEW users_view AS
SELECT 
    u.id,
    u.email,
    u.firstname,
    u.lastname,
    u.role,
    u.organization,
    tm.id as "teamId",
    tm.name as "teamName",
    gm.id as "groupId",
    gm.name as "groupName"
FROM 
    public.users u
  LEFT JOIN public.user_groups ug ON u.id = ug.user
  LEFT JOIN public.teams tm on tm.id = ug.team
  LEFT JOIN public.groups gm on gm.id = ug.group;


CREATE OR REPLACE FUNCTION get_users(filter json)
RETURNS json AS $function$
DECLARE 
    user_records record;
    team_group_record record;
    count_record int;
BEGIN

        SELECT  count(*) into count_record
        FROM public.users
        WHERE (organization::text = filter->>'organization'::text
              AND 
                role::text = coalesce(
                    case
                        when (filter->>'role'::text) IS NULL then role::text
                        else filter->>'role'::text
                    end,
                    role::text
                )
              )
              AND (
                email ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then email
                        else filter->>'query'::text
                    end,
                    email
                ) || '%'
                OR firstname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then firstname
                        else filter->>'query'::text
                    end,
                    firstname
                ) || '%'
                OR lastname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then lastname
                        else filter->>'query'::text
                    end,
                    lastname
                ) || '%'
            );

    CREATE TEMP TABLE temp_users (
        id uuid,
        email text,
        firstname text,
        lastname text,
        role text,
        title text,
        phone text,
        organization uuid
    );
   -- Insert data into the temporary table 
   INSERT INTO temp_users (
        id,
        email,
        firstname,
        lastname,
        role,
        title,
        phone,
        organization
    )
    SELECT cr.*
    FROM (
        SELECT 
            id,
            email,
            firstname,
            lastname,
            role,
            title,
            phone,
            organization
        FROM public.users
        WHERE (organization::text = filter->>'organization'::text
              AND 
                role::text = coalesce(
                    case
                        when (filter->>'role'::text) IS NULL then role::text
                        else filter->>'role'::text
                    end,
                    role::text
                )
              )
              AND (
                email ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then email
                        else filter->>'query'::text
                    end,
                    email
                ) || '%'
                OR firstname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then firstname
                        else filter->>'query'::text
                    end,
                    firstname
                ) || '%'
                OR lastname ILIKE '%' || coalesce(
                    case
                        when (filter->>'query'::text) IS NULL then lastname
                        else filter->>'query'::text
                    end,
                    lastname
                ) || '%'
            )
        LIMIT (filter->>'size')::bigint 
        OFFSET (filter->>'page')::bigint
    ) cr;

    -- Status Record
    SELECT array_agg(
        json_build_object(
            'id',ur.id,
            'email', ur.email,
            'firstname', ur.firstname,
            'lastname', ur.lastname,
            'role', ur.role,
            'title', ur.title,
            'phone', ur.phone,
            'organization', ur.organization)
    ) INTO user_records
    FROM temp_users ur;

    -- Carrier Record
    SELECT array_agg(
        json_build_object(
            'userGroupId', cr."userGroupId", 
            'userId', cr."userId", 
            'teamId', cr."teamId", 
            'teamName', cr."teamName", 
            'groupId', cr."groupId", 
            'groupName', cr."groupName"
            )
    ) INTO team_group_record
    FROM (
        SELECT 
            ug.id as "userGroupId",
            ug.user as "userId",
            tm.id as "teamId",
            tm.name as "teamName",
            gm.id as "groupId",
            gm.name as "groupName"
        FROM public.user_groups ug
            LEFT JOIN temp_users tu on tu.id = ug.user
            LEFT JOIN public.teams tm on tm.id = ug.team
            LEFT JOIN public.groups gm on gm.id = ug.group
        --WHERE ug.organization::text = filter->>'organization'::text
    ) AS cr;

    DROP TABLE temp_users;

    RETURN json_build_object(
        'total', count_record,
        'users', user_records,
        'team_groups', team_group_record
    );
END;
$function$
LANGUAGE plpgsql;


