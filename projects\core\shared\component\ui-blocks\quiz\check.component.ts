import { Component, Input, Output, EventEmitter } from '@angular/core';
import { HtmlWrapperComponent } from '@lms/shared';

@Component({
  selector: 'app-block-check',
  imports: [HtmlWrapperComponent],
  template: `
    <div
      class="flex flex-col gap-4 px-5 py-8 rounded-lg w-full"
      [style]="styleForm"
    >
      <div class="flex flex-col gap-2 mb-5">
        <h3 class="text-xl font-semibold">{{ content.title }}</h3>

        @if (content.description) {
        <app-ui-html-wrapper
          class="text-lg break-words"
          [content]="content.description"
        />
        }
      </div>

      @if (content.type === 'checkbox') {
      <div class="flex flex-col gap-4">
        @for (label of content.options; track label.id) {
        <div class="flex items-start gap-3">
          <input
            type="checkbox"
            [id]="label.id"
            [checked]="isSelected(label.id)"
            (change)="onCheckboxChange(label.id)"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded accent-lot-blue cursor-pointer"
          />
          <label [for]="label.id" class="cursor-pointer">{{
            label.label
          }}</label>
        </div>
        }
      </div>
      } @if (content.type === 'radiobox') {
      <div class="flex flex-col gap-4">
        @for (label of content.options; track label.id) {
        <div class="flex items-start gap-3">
          <input
            type="radio"
            [id]="label.id"
            [name]="content.questionId"
            [checked]="selectedRadio === label.id"
            (change)="onRadioChange(label.id)"
            class="mt-1 w-5 h-5 border-2 border-lot-dark rounded-full accent-lot-blue cursor-pointer"
          />
          <label [for]="label.id" class="cursor-pointer">{{
            label.label
          }}</label>
        </div>
        }
      </div>
      } @if (content.type === 'blank') {
      <div class="flex flex-col gap-4">
        @for (label of content.options; track label.id; let i = $index) {
        <div class="flex flex-col gap-2">
          <label [for]="label.id" class="font-medium"
            >{{ i + 1 }} - Answer for the blank (__)
          </label>
          <input
            type="text"
            [id]="label.id"
            [value]="blankAnswers[label.id] || ''"
            (input)="onBlankChange(label.id, $event)"
            class="p-2 border border-lot-gray rounded-lg focus:outline-none focus:border-lot-blue"
            placeholder="Type your answer here..."
          />
        </div>
        }
      </div>
      }
      <!-- @if (showSubmit) {
      <div class="flex justify-end mt-4">
        <button
          (click)="onSubmit()"
          class="px-6 py-2 bg-lot-blue text-white rounded-lg hover:bg-lot-blue/90 transition-colors"
        >
          {{ content.buttonLabel || 'Submit' }}
        </button>
      </div>
      } -->
    </div>
  `,
})
export class UIBlockCheckComponent {
  @Input() content: {
    type: 'checkbox' | 'radiobox' | 'blank';
    questionId: string;
    title: string;
    description: string;
    options: {
      id: string;
      label: string;
    }[];
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }

  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }

  @Output() view = new EventEmitter<{
    content: any;
    action: 'submit' | 'change';
    userAnswers: { [key: string]: any };
  }>();

  selectedCheckboxes: Set<string> = new Set();
  selectedRadio: string | null = null;
  blankAnswers: { [key: string]: string } = {};

  // get showSubmit(): boolean {
  //   return this.content.showSubmit ?? true;
  // }

  isSelected(id: string): boolean {
    return this.selectedCheckboxes.has(id);
  }

  onCheckboxChange(id: string): void {
    if (this.selectedCheckboxes.has(id)) {
      this.selectedCheckboxes.delete(id);
    } else {
      this.selectedCheckboxes.add(id);
    }
    this.emitAnswer();
  }

  onRadioChange(id: string): void {
    this.selectedRadio = id;
    this.emitAnswer();
  }

  onBlankChange(id: string, event: Event): void {
    const input = event.target as HTMLInputElement;
    this.blankAnswers[id] = input.value;
    this.emitAnswer();
  }

  emitAnswer(action: 'submit' | 'change' = 'change'): void {
    let answers: { [key: string]: any };

    switch (this.content.type) {
      case 'checkbox':
        answers = Array.from(this.selectedCheckboxes);
        break;
      case 'radiobox':
        answers = [this.selectedRadio!];
        break;
      case 'blank':
        answers = this.blankAnswers;
        break;
      default:
        answers = {};
    }

    this.view.emit({
      action: action,
      content: this.content,
      userAnswers: answers,
    });
    this.selectedRadio = null;
    this.blankAnswers = {};
    this.selectedCheckboxes.clear();
  }

  onSubmit(): void {
    this.emitAnswer('submit');
  }
}

export const getCheckTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        type: 'checkbox',
        options: [
          {
            id: 'cb1',
            label:
              'Which programming languages do you know? (Select all that apply)',
          },
          {
            id: 'cb2',
            label: 'JavaScript',
          },
          {
            id: 'cb3',
            label: 'Python',
          },
          {
            id: 'cb4',
            label: 'Java',
          },
        ],
        showSubmit: true,
        meta: {
          width: 100,
          padding: 6,
          background: 'lot-light-gray',
          color: 'lot-dark',
        },
      };
    case 1: {
      return {
        type: 'radiobox',
        options: [
          {
            id: 'rb1',
            label: 'Beginner',
          },
          {
            id: 'rb2',
            label: 'Intermediate',
          },
          {
            id: 'rb3',
            label: 'Advanced',
          },
        ],
        groupName: 'skillLevel',
        showSubmit: true,
        meta: {
          width: 50,
          padding: 4,
          margin: 2,
          background: 'lot-white',
          color: 'lot-blue',
        },
      };
    }
    case 2: {
      return {
        type: 'blank',
        options: [
          {
            id: 'blank1',
            label: 'What is your favorite programming language?',
          },
          {
            id: 'blank2',
            label: 'How many years of experience do you have?',
          },
        ],
        showSubmit: false,
        meta: {
          width: 33,
          height: 100,
          padding: 8,
          background: 'lot-gray/20',
          color: 'lot-dark',
        },
      };
    }
    case 3: {
      return {
        type: 'checkbox',
        options: [
          {
            id: 'ccb1',
            label: 'I agree to the terms and conditions',
          },
        ],
        meta: {
          color: 'lot-dark',
        },
      };
    }

    default:
      return {
        type: 'radiobox',
        options: [
          {
            id: 'cr1',
            label: 'Option A',
          },
          {
            id: 'cr2',
            label: 'Option B',
          },
        ],
        groupName: 'customGroup',
        showSubmit: true,
        meta: {
          width: 100,
          height: 50,
          padding: 6,
          margin: 4,
          background: 'lot-blue',
          color: 'white',
        },
      };
  }
};
