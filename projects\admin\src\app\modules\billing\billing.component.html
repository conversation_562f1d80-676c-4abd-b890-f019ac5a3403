<div class="flex flex-col w-full pt-4">
  @if (view === 'BILLING') {
  <header class="h-20">
    <h1 class="text-lot-dark text-2xl font-bold">Your Subscription</h1>
  </header>
  <div class="flex-grow h-full">
    <div class="flex flex-col gap-12 w-full h-full">
      <div class="flex gap-12 items-center">
        <div class="flex gap-5">
          <span class="text-lot-blue text-[63px]">
            <i [class]="'fa-solid ' + subscription.plan.icon"></i>
          </span>
          <div class="flex flex-col">
            <span class="text-lot-dark text-2xl font-black">{{
              subscription.plan.name
            }}</span>
            <span class="text-sm"
              >Start Date: {{ subscription.created_at | date : "longDate" }}</span
            >
            <span class="text-sm text-lot-ai-dark font-semibold"
              >Expires: {{ subscription.expired_at | date : "longDate" }}</span
            >
            <span
              class="text-sm"
              [class.text-lot-danger]="remaingSeats <= 1"
              [class.text-lot-dark-gray]="remaingSeats > 1"
              >{{ subscription.used_seat }}/{{ subscription.plan.countMax }}
              seats remaining
            </span>
          </div>
        </div>

        <div class="flex-grow">
          <div
            class="flex justify-between items-center bg-lot-ai/20 from-white to-lot-ai/70 py-6 px-8 rounded-xl"
          >
            <h1 class="text-lot-dark font-bold">
              LearnMate and TeachMate AI Features
            </h1>
            <div class="flex items-center gap-2">
              <span
                class="text-2xl text-lot-ai-dark from-lot-ai-dark to-lot-ai"
              >
                <i class="fa-solid fa-circle-check"></i>
              </span>
              <span>{{ hasAiSubscription ? "Active" : "Not Yet" }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex items-center gap-3 mt-4">
      @if (hasSubscriptionPayment) {
      <button type="button"
        (click)="view = 'PAYMENT'" class="button-primary w-fit">
        Manage Subscription
      </button>
      }
      <button
        type="button"
        (click)="subscribe()"
        class="button-primary-outline border-none w-fit"
      >
        Change Plan
      </button>
      <button
        type="button"
        (click)="view = 'USERS'"
        class="button-primary-outline border-none w-fit"
      >
        Active Users
      </button>
    </div>

    <div class="flex flex-col gap-3 mt-12">
      <h1 class="text-lot-dark text-2xl font-bold">Your Subscription</h1>
      <div class="w-full bg-white rounded-xl px-10 pb-10 pt-6">
        <app-resource-loader [source]="source" />
        <app-billing-history [data]="source.value() || []" />
      </div>
    </div>
  </div>
  } @if (view === 'USERS') {
  <header>
    <a
      href="javascript:void(0)"
      (click)="view = 'BILLING'"
      class="text-lot-dark/70 hover:text-lot-blue text-sm"
    >
      < Back to Your Subscription
    </a>
    <h1 class="text-lot-dark text-2xl font-bold">Active Users</h1>
  </header>
  <app-billing-active-users class="mt-10" />
  } @if (view === 'PAYMENT') {
  <header>
    <a
      href="javascript:void(0)"
      (click)="view = 'BILLING'"
      class="text-lot-dark/70 hover:text-lot-blue text-sm"
    >
      < Back to Your Subscription
    </a>
    <h1 class="text-lot-dark text-2xl font-bold">
      Manage Subscription/Payment
    </h1>
  </header>
  <app-manage-subscription class="mt-10" />
  }
</div>
