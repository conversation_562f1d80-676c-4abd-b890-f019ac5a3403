import { Component, computed, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { UserAction, UserListComponent } from './list/list.component';
import { DialogComponent, ResourceHeaderComponent } from '@lms/shared';
import { MatDialog } from '@angular/material/dialog';
import { UserFormComponent } from './form/user-form.component';
import { AccountStatus, ToastMessageType, UserItem, UsersCoreService } from '@lms/core';
import { firstValueFrom } from 'rxjs';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TeamFormComponent } from '../teams/form/team-form.component';

@Component({
  selector: 'app-users',
  imports: [UserListComponent, ResourceHeaderComponent, MatProgressBarModule],
  templateUrl: './users.component.html',
})
export class UsersComponent implements OnInit {
  readonly dialog = inject(MatDialog);
  service = inject(UsersCoreService);

  source = this.service.userSource;
  isLoading = false;
  error?: string;
  users = computed(() => (this.source.value() || []) as UserItem[]);
  totalCount = computed(() => this.service.totalCount());

  tab = 1;
  tabs = [
    {
      id: 1,
      name: 'All Users',
      role: AccountStatus.NONE,
    },
    {
      id: 2,
      name: 'Admins',
      role: AccountStatus.ADMINISTRATOR,
    },
    {
      id: 3,
      name: 'Learners',
      role: AccountStatus.LEARNER,
    },
    {
      id: 4,
      name: 'Pending',
      role: AccountStatus.NONE,
    },
  ];

  ngOnInit(): void {
    this.service.filter.set({
      payload: {
        query: '',
        role: undefined,
        sorting: [],
      },
      paging: {
        page: 1,
        size: 10,
      },
    });
  }
  async action({
    type,
    data,
    page,
  }: {
    type: UserAction;
    data: UserItem;
    page?: number;
  }) {
    if (type === 'edit') {
      this.dialog.open(UserFormComponent, {
        minWidth: '800px',
        data: {
          type: 'EDIT',
          item: data,
        },
      }).afterClosed().subscribe(() => this.service.userSource.reload());
    }

    if (type === 'delete') {
      const res = await firstValueFrom(
        this.dialog
          .open(DialogComponent, {
            width: '450px',
            data: {
              type: 'DELETE',
              message: `You're about to delete ${data.firstname} ${data.lastname}`,
              title: 'Are you sure to remove this user?',
            },
          })
          .afterClosed()
      );

      if (res) {
        this.isLoading = true;
        const result = await this.service.delete(data.id);
        this.isLoading = false;
        if (result.error) {
          this.error = result.error;
          this.service.state.openToast({
            title: 'Delete Request Failed',
            message: 'Failed: ' + result.error,
            type: ToastMessageType.ERROR,
          });
        }
        if (result.data) {
          this.service.state.openToast({
            title: 'Delete Request Successful',
            message: 'User deleted successfully',
            type: ToastMessageType.SUCCESS,
          });
          this.service.userSource.reload();
        }
      }
    }

    if (type === 'page') {
      this.service.filter.update((f) => ({
        payload: f?.payload!,
        paging: {
          page: page || 1,
          size: 10,
        },
      }));
    }
    if (type === 'team') {
      this.dialog
        .open(TeamFormComponent, {
          width: '600px',
          data: {
            type: 'ASSIGN_USER',
            item: undefined,
            user: data,
          },
        })
        .afterClosed()
        .subscribe(() => this.service.userSource.reload());
    }
  }

  setTab(tab: { id: number; name: string; role: AccountStatus }) {
    this.tab = tab.id;
    const paging = {
      page: 1,
      size: 10,
    };
    if (
      [AccountStatus.ADMINISTRATOR, AccountStatus.LEARNER].includes(tab.role)
    ) {
      this.service.filter.set({
        payload: {
          query: '',
          role: tab.role,
          sorting: [],
        },
        paging,
      });
    } else {
      this.service.filter.set({
        payload: {
          query: '',
          role: undefined,
          sorting: [],
          showPending: tab.id === 4,
        },
        paging,
      });
    }
  }
}
