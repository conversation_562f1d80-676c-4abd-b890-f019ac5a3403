import { Component, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { AuthService, markControlsDirty } from '@lms/core';
import { ValidationTextComponent } from '@lms/shared';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  selector: 'app-verify-email',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    ValidationTextComponent,
    MatButtonModule,
    RouterLink,
    NgxMaskDirective,
  ],
  templateUrl: './verify.component.html',
  styles: [
    `
      input[type='number']::-webkit-outer-spin-button,
      input[type='number']::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      input[type='number'] {
        -moz-appearance: textfield;
      }
    `,
  ],
})
export class VerifyEmailComponent implements OnInit {
  form: FormGroup;

  isSignMode = 1;
  customPatterns = {
    '0': { pattern: new RegExp('^[0-9]{4,10}$') },
  };
  emailParam: string;

  message?: string;
  submitted = false;

  get organization() {
    return this.authSvc.organization['name'] ?? '';
  }

  get emailMasked() {
    return this.maskEmail(this.emailParam);
  }

  constructor(
    private route: ActivatedRoute,
    private authSvc: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    const email = this.route.snapshot.params['email'];
    if (this.isValidEmail(email)) {
      this.emailParam = email;
      // this.authSvc.resendCode(this.emailParam).then(res => {
      //   if (res.error?.message.includes("Signups not allowed for otp")) {
      //     this.isSignMode = 4;
      //   } else {
      //     this.message = res.error?.message;
      //   }
      // });
      // this.isSignMode = 4;
      this.form = new FormGroup({
        code: new FormControl('', [Validators.required]),
      });
    } else {
      this.isSignMode = 2;
      this.form = new FormGroup({
        email: new FormControl(email, [Validators.required, Validators.email]),
      });
    }
  }

  onInput(input: HTMLInputElement) {
    if (input.value.length > 10) {
      input.value = input.value.slice(0, 10);
    }
  }

  async resendCode() {
    this.authSvc.isLoading = true;
    const { error } = await this.authSvc.resendCode(this.emailParam);
    this.authSvc.isLoading = false;
    this.message = error?.message;
    this.isSignMode = 1;
  }

  async submit() {
    this.message = undefined;
    if (this.form.invalid || !(this.form.value.email || this.form.value.code)) {
      markControlsDirty(this.form);
      return;
    }
    if (this.form.value.code && !this.form.value.email) {
      this.authSvc.isLoading = true;
      const res = await this.authSvc.verifyEmail(
        this.emailParam,
        this.form.value.code.toString()
      );
      this.isSignMode = res.error?.message?.includes(
        'Token has expired or is invalid'
      )
        ? 3
        : 1;
      this.message =
        this.isSignMode === 3
          ? 'Your verification Code has expired or is invalid'
          : res.error?.message;
      this.authSvc.isLoading = false;
      if (res.data.user && res.data.session) {
        this.authSvc.isLoading = true;
        const { userInfo: data, error } = await this.authSvc.userService.getOne(
          res.data.user?.id!
        );
        this.authSvc.isLoading = false;
        this.message = error;
        if (data) {
          this.router.navigate(['/change-password']);
        }
      }
    }
    if (this.form.value.email && !this.form.value.code) {
      this.authSvc.isLoading = true;
      const { error } = await this.authSvc.resendCode(this.form.value.email);
      this.message = error?.message;
      this.authSvc.isLoading = false;
      if (!error) {
        this.emailParam = this.form.value.email;
        this.form = new FormGroup({
          code: new FormControl('', [Validators.required]),
        });
        this.isSignMode = 1;
      }
    }
  }

  goLogin() {
    this.router.navigate(['/sign-in']);
  }

  maskEmail(email: string): string {
    if (!email) return '';
    const parts = email.split('@');
    const maskedLocalPart = parts[0].slice(0, 3) + '...' + parts[0].slice(-2);
    return `${maskedLocalPart}@${parts[1]}`;
  }

  isValidEmail(email: string): boolean {
    const regex =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return regex.test(email);
  }
}
