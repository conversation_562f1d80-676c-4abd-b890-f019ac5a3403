import { MatTabsModule } from '@angular/material/tabs';
import {
  Component,
  OnChanges,
  SimpleChanges,
  inject,
  signal,
} from '@angular/core';
import {
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialogRef,
} from '@angular/material/dialog';
import {
  GroupItem,
  UserAudienceItem,
  AccountStatus,
  UserItem,
  SubgroupAudienceItem,
  ApiRespone,
  TeamCoreService,
  UsersCoreService,
} from '@lms/core';
import { Observable, of } from 'rxjs';
import { AssignUserV2Component } from './users/a-user-v2.component';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-user-assigner',
  imports: [AsyncPipe, MatTabsModule, AssignUserV2Component, MatDialogModule],
  templateUrl: './assigner.component.html',
})
export class UserAssignerComponent implements OnChanges {
  dialogRef: MatDialogRef<UserAssignerComponent> = inject(MatDialogRef);
  teamService = inject(TeamCoreService);
  userService = inject(UsersCoreService);

  data: {
    item: GroupItem;
    type: 'TEAM' | 'GROUP';
  } = inject(MAT_DIALOG_DATA);

  get item() {
    return this.data.item;
  }

  UserItem = signal<{
    data: {
      user: UserItem;
      enrolls: {
        duedate?: string;
        type?: 'user' | 'subgroup' | 'group' | 'course' | null | undefined;
        id?: string | undefined;
      }[];
    }[];
    count: number;
  }>({
    data: [],
    count: 0,
  });
  userInfosFiltered: Observable<UserAudienceItem[]> = of([]);

  items: Array<SubgroupAudienceItem> = [];
  lastGroupSelected: string;
  cacheItems: any = {};

  _users: ApiRespone<UserItem> = {
    data: [],
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['audienceData'].currentValue) {
      this.loadData();
      if (this.lastGroupSelected) this.getSubgroup(this.lastGroupSelected);
    }
  }

  async loadData(query?: string, page = 0) {
    const res = await this.userService.getUsers({
      payload: { query, role: AccountStatus.LEARNER },
      paging: {
        page,
        size: 10,
      },
    });
    this._users = res;
    this.UserItem.set({
      data: res.data.map((u) => {
        return {
          user: u,
          enrolls:
            //this.audienceData?.user?.find((x) => x.user.id === u.id)?.enrolls ??
            [],
        };
      }),
      count: 0, // res.count ?? 0,
    });
  }

  async getSubgroup(id: string) {
    if (this.cacheItems[id]) {
      this.items =
        this.cacheItems[id]?.map((sg: GroupItem) => ({
          subgroup: sg,
          enrolls:
            //this.audienceData?.subgroup?.find((x) => x.subgroup.id === sg.id)
            [],
        })) ?? [];
      return;
    }
    // const { subgroups } = await this.teamService.getSubGroupsByGroup(id);
    // this.items =
    //   subgroups?.map((sg: any) => ({
    //     subgroup: sg,
    //     enrolls:
    //       this.audienceData?.subgroup?.find((x) => x.subgroup.id === sg.id)
    //         ?.enrolls ?? [],
    //   })) ?? [];
    // this.cacheItems[id] = subgroups;
    this.lastGroupSelected = id;
  }

  // async saveItem(
  //   item: {
  //     new: Array<any>;
  //     deleted: Array<string>;
  //   },
  //   type: 'GROUP' | 'SUBGROUP' | 'USER'
  // ) {
  //   const res = await firstValueFrom(
  //     this.dialog
  //       .open(DueDateFormComponent, {
  //         width: '450px',
  //       })
  //       .afterClosed()
  //   );

  //   if (res?.dueDate || !res?.cancel) {
  //     this.save.emit({
  //       type,
  //       item,
  //       dueDate: res?.dueDate,
  //     });
  //   }
  // }

  // async searchUser(term: string) {
  //   // const filtr = this.userInfos$?.pipe(
  //   //   take(1),
  //   //   map((res) =>
  //   //     res.data.filter((u) =>
  //   //       (
  //   //         u.user?.firstname +
  //   //         ' ' +
  //   //         u.user?.lastname +
  //   //         ' ' +
  //   //         u.user?.email +
  //   //         ' ' +
  //   //         u.user?.phone
  //   //       )
  //   //         ?.toLowerCase()
  //   //         ?.includes(term.toLowerCase())
  //   //     )
  //   //   )
  //   // );

  //   // if (
  //   //   this._users.data.some((u) =>
  //   //     (u.firstname + ' ' + u.lastname + ' ' + u.email + ' ' + u.phone)
  //   //       ?.toLowerCase()
  //   //       ?.includes(term.toLowerCase())
  //   //   )
  //   // ) {
  //   //   this.userInfosFiltered = filtr ?? [];
  //   //   return;
  //   // }
  //   this.loadData(term);
  // }

  // async updateItemAssignment(item: AudienceItemEnroll) {
  //   if (!item.id) return;
  //   const res = await firstValueFrom(
  //     this.dialog
  //       .open(DueDateFormComponent, {
  //         width: '450px',
  //         data: {
  //           item,
  //         },
  //       })
  //       .afterClosed()
  //   );
  //   if (res?.dueDate && !res?.cancel && res.dueDate !== item.duedate) {
  //     this.onUpdateSingleAssignmentDuedate.emit({
  //       id: item.id,
  //       type: item.type,
  //       duedate: res?.dueDate,
  //     });
  //   }
  // }
}
