-- FUNCTION: public.handle_new_user()

-- DROP FUNCTION IF EXISTS public.handle_new_user();

CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF SECURITY DEFINER
AS $BODY$

  begin
  insert
   into public.users
   (
      "id",
      "email",
      "organization",
      "firstname",
      "lastname",
      "phone",
      "name",
      "created_by",
      "company",
      "role"
   )
    values
   (
      new.id,
      new.email,
      uuid(new."raw_user_meta_data"::json->>'organization'::text),
      coalesce(new."raw_user_meta_data"::json->>'firstname'::text, null),
      coalesce(new."raw_user_meta_data"::json->>'lastname'::text, null),
      coalesce(new."raw_user_meta_data"::json->>'phone'::text, null),
      coalesce(new."raw_user_meta_data"::json->>'name'::text, null),
      coalesce(uuid(new."raw_user_meta_data"::json->>'created_by'::text), null),
      coalesce(new."raw_user_meta_data"::json->>'company'::text, null),
      coalesce(new."raw_user_meta_data"::json->>'role'::text, null)
   );

   insert into public.user_audits (id, email, "created_by", "invitedDate")
   values (new.id, new.email, new.id, NOW());

    return new;
  end;
$BODY$;

ALTER FUNCTION public.handle_new_user()
    OWNER TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO PUBLIC;

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO anon;

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO authenticated;

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO postgres;

GRANT EXECUTE ON FUNCTION public.handle_new_user() TO service_role;

