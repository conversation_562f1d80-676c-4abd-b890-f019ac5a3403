import { Injectable, inject } from '@angular/core';
import {
  MatSnackBar,
  MatSnackBarHorizontalPosition,
  MatSnackBarVerticalPosition,
} from '@angular/material/snack-bar';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  private snackBar: MatSnackBar = inject(MatSnackBar);

  success(
    message: string,
    duration = 5000,
    horizontalPosition: MatSnackBarHorizontalPosition = 'center',
    verticalPosition: MatSnackBarVerticalPosition = 'top'
  ) {
    this.snackBar.open(message, '✕', {
      duration,
      panelClass: ['notifications', 'success'],
      horizontalPosition,
      verticalPosition,
    });
  }
  warning(
    message: string,
    duration = 5000,
    horizontalPosition: MatSnackBarHorizontalPosition = 'center',
    verticalPosition: MatSnackBarVerticalPosition = 'top'
  ) {
    this.snackBar.open(message, '✕', {
      duration,
      panelClass: ['notifications', 'warning', 'info'],
      horizontalPosition,
      verticalPosition,
    });
  }

  error(
    message: string,
    duration = 5000,
    horizontalPosition: MatSnackBarHorizontalPosition = 'center',
    verticalPosition: MatSnackBarVerticalPosition = 'top'
  ) {
    this.snackBar.open(message, '✕', {
      duration,
      panelClass: ['notifications', 'error'],
      horizontalPosition,
      verticalPosition,
    });
  }
}
