<div class="flex flex-col gap-4 px-10 pt-5">
  <h1 class="text-lot-dark text-2xl font-bold">UI Widgets</h1>
  <div class="flex gap-5 w-full border-t py-5 max-h-[620px]">
    <section class="flex flex-col w-80 min-w-80 overflow-y-auto scrollbar">
      @for (item of widgets; track $index) {
      <a
        href="javascript:void(0)"
        (click)="viewTemplate(item)"
        class="flex items-center gap-5 hover:bg-lot-light-blue/50 px-5 py-3 rounded-md {{
          currentWidget === item.id ? 'bg-lot-light-blue/50' : ''
        }}"
      >
        <i
          class="{{ item.icon }} text-xl {{
            item.shortName.includes('AI') ? 'text-lot-ai' : 'text-lot-blue'
          }}"
        ></i>
        <span class="text-sm">{{ item.shortName }}</span>
      </a>
      }
    </section>
    <div class="flex-grow overflow-y-auto scrollbar px-10 pt-5 pb-10">
      <div class="flex flex-col gap-8">
        @if (isAIWidget) {
        <app-block-ai [data]="currentWidget" (view)="getAiData($event)" />
        }
        <div #widgetHost class="w-full"></div>
      </div>
    </div>
  </div>
</div>
