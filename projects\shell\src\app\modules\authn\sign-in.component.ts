import { CommonModule } from '@angular/common';
import { AfterViewInit, Component, inject, OnInit } from '@angular/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RouterOutlet } from '@angular/router';
import { AuthService } from '@lms/core';

@Component({
  selector: 'app-sign-in',
  imports: [CommonModule, MatProgressBarModule, RouterOutlet],
  templateUrl: './sign-in.component.html',
  styleUrls: ['./sign-in.component.scss'],
})
export class SignInComponent implements OnInit, AfterViewInit {
  authService = inject(AuthService);

  get year() {
    return new Date().getFullYear();
  }

  organization: { [key: string]: string };

  logo = 'assets/images/logo.svg';

  get isLoading() {
    return this.authService.isLoading;
  }

  ngOnInit(): void {
    this.organization = this.authService.organization;
    const { confirmation_url } = Object.fromEntries(
      new URLSearchParams(window.location.search)
    );
    if (confirmation_url) {
      window.location.href = decodeURIComponent(confirmation_url);
    }
  }

  ngAfterViewInit(): void {
    if (!this.organization) {
      setTimeout(() => {
        this.organization = this.authService.organization;
      }, 1000);
    }
  }
}
