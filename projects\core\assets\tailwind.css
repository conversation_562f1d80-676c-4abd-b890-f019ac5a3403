@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  h1 {
    @apply font-semibold text-2xl;
  }

  h2 {
    @apply font-semibold text-xl;
  }

  h3 {
    @apply font-semibold text-base;
  }

  h4 {
    @apply font-semibold text-sm;
  }
}
:root {
  --lot-dark: #002747;
  --lot-blue: #2e7ddb;
  --lot-gray: #d9d9d9;
  --lot-light-gray: #f9f9f9;
  --lot-white: #ecf6ff;
  --lot-ai: #18cc78;
  --lot-ai-dark: #0ea1ab;
  --lot-dark-gray: #686a6d;
  --lot-light-blue: #9ecaff;
  --lot-gold: #ffac33;
  --lot-warning: #dd6f01;
  --lot-danger: #f95757;
  --lot-disabled: #bac2cc;
}

@layer components {
  .button-primary {
    @apply bg-lot-blue hover:bg-lot-blue/75 text-white font-semibold py-1 px-4 rounded-md border-0;
    @apply text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-primary-outline {
    @apply bg-white hover:bg-lot-blue hover:text-white font-semibold py-1 px-4 rounded-md border-2 border-lot-blue;
    @apply text-lot-blue text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-primary-ghost {
    @apply bg-white hover:bg-lot-blue hover:text-white font-semibold py-1 px-4 rounded-md border-none;
    @apply text-lot-blue text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-ghost-outline {
    @apply bg-transparent font-semibold py-1 px-4 rounded-md hover:border-2 hover:border-lot-blue text-lot-blue text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-primary-dark {
    @apply bg-lot-dark hover:bg-lot-dark/55 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-secondary {
    @apply bg-lot-blue hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-danger {
    @apply bg-lot-danger hover:bg-red-700 text-white font-semibold py-2 px-4 rounded border-0;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-outline {
    @apply bg-white hover:bg-secondary hover:text-white font-semibold py-2 px-4 rounded border-2 border-secondary;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }
  .button-outline-danger {
    @apply bg-white hover:bg-accent text-accent hover:text-white font-semibold py-2 px-4 rounded border-2 border-accent;
    @apply h-10 text-center;
    &:disabled {
      @apply bg-gray-300 text-gray-500;
    }
  }

  .form-input {
    @apply w-full flex flex-col gap-2;

    label {
      @apply text-lot-dark-gray pl-2;
    }

    input,
    mat-select,
    textarea,
    select {
      @apply bg-transparent rounded-md border border-lot-gray px-5 py-2.5 w-full text-lot-dark placeholder:text-lot-gray outline-none;

      &.ng-invalid,
      &.ng-touched,
      &.ng-dirty,
      &.invalid {
        color: var(--lot-danger);
      }
      &:disabled {
        @apply bg-gray-300 text-gray-500;
      }
    }

    quill-editor {
      &.ng-invalid.ng-dirty {
        border: 2px solid var(--lot-danger);
        border-radius: 0.5rem;
      }
    }
  }

  .router-link-active {
    @apply border-blue-500 text-blue-500 border-b-4;
  }

  .form-lot-input {
    @apply w-full flex flex-col gap-1;
    .field {
      @apply w-full relative bg-white;

      label {
        @apply absolute text-xl text-lot-dark-gray/80 font-semibold duration-300 transform -translate-y-4 scale-75 top-1 z-10 origin-[0] bg-white px-2 peer-focus:px-2 peer-focus:text-lot-blue peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-0 peer-focus:scale-75 peer-focus:-translate-y-4 left-7;

        &.error {
          @apply text-lot-danger peer-focus:text-lot-danger;
        }
      }

      input,
      mat-select,
      textarea,
      select {
        @apply block px-4 pb-2.5 pt-4 w-full text-lg text-lot-dark bg-white rounded-lg border border-gray-300 appearance-none placeholder:text-lot-gray placeholder:text-base focus:outline-none focus:ring-0 focus:border-lot-blue;
        &.ng-invalid.ng-dirty {
          @apply border-lot-danger focus:border-lot-danger;
        }
        &.ng-valid {
          @apply bg-white;
        }
        &:disabled {
          @apply bg-gray-200 text-gray-600;
        }
      }

      quill-editor {
        @apply border border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-lot-blue rounded-lg placeholder:text-lot-gray placeholder:text-lg;
        &.ng-invalid.ng-dirty {
          @apply border-lot-danger focus:border-lot-danger border-2;
        }
      }
    }

    .error {
      @apply text-sm text-lot-danger ml-4;
    }
  }
}

input.ng-dirty.ng-invalid,
textarea.ng-dirty.ng-invalid,
select.ng-dirty.ng-invalid {
  border: 2px solid var(--lot-danger);
}


@layer utilities {
  .scrollbar {
    --scrollbar-bg: #00274794;
  }
  .scrollbar::-webkit-scrollbar {
    width: 10px;
    height: 50px;
  }

  .scrollbar::-webkit-scrollbar-track {
    border-radius: 100vh;
    background:  #9ecaff61;
  }

  .scrollbar::-webkit-scrollbar-thumb {
    background: var(--scrollbar-bg);
    border-radius: 100vh;
    border: 1px solid var(--lot-light-gray);
  }

  .scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-bg);
  }
}
