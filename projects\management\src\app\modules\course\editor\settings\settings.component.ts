import { Component, Input } from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { Course } from '@lms/core';

@Component({
  selector: 'app-course-settings',
  imports: [
    CommonModule,
    MatPaginatorModule,
    MatTableModule,
    ReactiveFormsModule,
    
  ],
  templateUrl: 'settings.component.html',
})
export class CourseSettingsComponent {
  @Input({ required: true }) data: Course;
}
