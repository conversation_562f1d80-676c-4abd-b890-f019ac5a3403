<div class="flex flex-col gap-5">
  <div class="flex">
    <div class="flex-grow">
      <ng-container
        *ngTemplateOutlet="isLearningPath ? pathTemplate : ledTemplate"
      />
    </div>
    @if (viewCourse) {
    <div
      class="flex flex-col gap-5 rounded-3xl bg-lot-light-blue/10 p-5 w-[574px] break-words"
    >
      <div class="flex items-center justify-between">
        <span></span>
        <a
          href="javascript:void(0)"
          class="text-lot-danger"
          (click)="viewCourse = undefined"
        >
          <span class="material-symbols-outlined"> close </span>
        </a>
      </div>
      <div class="h-80">
        <img
          src="{{ viewCourse.cover }}"
          alt=""
          class="w-full h-full object-cover rounded-3xl"
        />
      </div>
      <div class="flex items-center justify-between">
        <span class="font-semibold text-lot-dark text-xl">{{
          viewCourse.name
        }}</span>
        <button
          class="button-primary w-fit"
          (click)="goToCourse(viewCourse.id!)"
        >
          View Full Course
        </button>
      </div>
      <div class="flex flex-col gap-4 mb-6">
        <span class="text-sm font-semibold pb-3 border-b border-lot-blue w-fit">
          Course Brief
        </span>
        <p class="text-sm">{{ viewCourse.short }}...</p>
        <a
          href="javascript:void(0)"
          class="text-xs italic text-lot-blue underline"
          (click)="viewCourse = undefined"
        >
          Close Panel
        </a>
      </div>
    </div>
    }
  </div>
</div>

<ng-template #pathTemplate>
  <div class="flex flex-col gap-4">
    <div class="flex flex-col gap-5 w-full mt-8">
      @for (item of items | sortByOrder; track $index; let i=$index) {
      <app-expandable [headerTemplate]="customHeader" [isExpanded]="i === 0">
        <ng-template #customHeader let-context let-toggle="toggle">
          <a
            href="javascript:void(0)"
            (click)="toggle(context)"
            class="bg-transparent border-0 outline-none h-fit flex justify-between"
          >
            <span class="text-lot-blue font-semibold text-xl">
              {{ item.category }}
            </span>
            <span
              class="material-symbols-outlined text-lot-dark font-bold text-3xl"
              >{{ context.isExpanded ? "remove" : "add" }}</span
            >
          </a>
        </ng-template>
        <div class="px-10 py-3 bg-lot-light-blue/10 rounded-lg">
          @for (course of item.courses; track $index) {
          <div class="flex items-center justify-between py-5 border-b">
            <span>{{ course.name }}</span>
            <div class="flex items-center gap-2">
              <button class="button-primary" (click)="viewCourse = course">
                Learn More
              </button>
              <a
                href="javascript:void(0)"
                (click)="removeCourse(item.category, course.id)"
                class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
              >
                <i class="fa-solid fa-trash"></i>
              </a>
            </div>
          </div>
          }
          <a
            href="javascript:void(0)"
            class="flex items-center gap-3 text-lot-dark-gray border-y py-4"
            (click)="addCourse(item.category)"
          >
            <i class="fa-solid fa-plus"></i>
            Add a Course
          </a>
        </div>
      </app-expandable>
      }
      <a
        href="javascript:void(0)"
        class="flex items-center gap-3 text-lot-dark-gray border-y py-4"
        (click)="addCourse()"
      >
        <i class="fa-solid fa-plus"></i>
        Add a Group
      </a>
    </div>
  </div>
</ng-template>

<ng-template #ledTemplate>
  <div class="flex flex-col gap-5 w-full">
    <h3 class="text-lot-blue text-lg">Prerequisites</h3>
    <div class="flex flex-col gap-5">
      <div class="flex gap-9 items-start">
        @for (course of prerequisites | sortByOrder; track $index; let i=$index)
        {
        <div class="flex flex-col gap-5 group w-[300px]">
          <a
            href="javascript:void(0);"
            (click)="viewCourse = course"
            class="relative w-full h-40"
          >
            <img
              src="{{ course.cover }}"
              alt=""
              class="w-full h-full object-cover rounded-3xl"
            />
            <div
              class="absolute rounded-3xl inset-0 bg-opacity-0 group-hover:bg-lot-dark/70 flex justify-center items-center"
            >
              <div class="hidden group-hover:flex text-white">
                <span>Learn More</span>
              </div>
            </div>
          </a>
          <div class="flex-1">
            <div class="flex flex-col gap-3 h-full">
              <h1 class="font-semibold text-lg">
                {{ course.name }}
              </h1>
              <a
                href="javascript:void(0)"
                (click)="removeCourse('', course.id)"
                class="text-lot-dark-gray/50 group-hover:text-lot-danger/70 text-xl"
              >
                <i class="fa-solid fa-trash"></i>
              </a>
            </div>
          </div>
        </div>
        }
      </div>
      <a
        href="javascript:void(0)"
        class="flex items-center gap-3 text-lot-dark-gray border-y py-4"
        (click)="addCourse()"
      >
        <i class="fa-solid fa-plus"></i>
        Add a Prerequisite
      </a>
    </div>

    <div class="flex flex-col gap-3">
      <h3 class="text-lot-blue text-lg">Sessions</h3>
      <ng-container
        *ngTemplateOutlet="
        sessionTmp;
        context: {
          items: sessions,
        }
      "
      />
    </div>
  </div>
</ng-template>

<ng-template #sessionTmp let-items="items">
  <div class="flex flex-col gap-10">
    @for (session of items; track $index; let i=$index) {
    <app-expandable [headerTemplate]="customHeader" [isExpanded]="i === 0">
      <ng-template #customHeader let-context let-toggle="toggle">
        <a
          href="javascript:void(0)"
          (click)="toggle(context)"
          class="bg-transparent border-0 outline-none h-fit flex justify-between"
        >
          <div class="flex justify-between items-center w-full mr-10">
            <span class="text-lot-blue font-semibold text-xl">
              {{ session.name }}
            </span>

            <div class="flex items-center gap-3">
              <a
              href="javascript:void(0)"
              (click)="editSession(session.id)"
              class="text-lot-dark-gray/50 group-hover:text-lot-blue/70 text-xl"
            >
              <i class="fa-solid fa-pen-to-square"></i>
            </a>
            <a
              href="javascript:void(0)"
              (click)="removeSession(session.id)"
              class="text-lot-danger/70 text-xl"
            >
              <i class="fa-solid fa-trash"></i>
            </a>
            </div>
          </div>
          <span
            class="material-symbols-outlined text-lot-dark font-bold text-3xl"
            >{{ context.isExpanded ? "remove" : "add" }}</span
          >
        </a>
      </ng-template>
      <div class="px-10 py-3 bg-lot-light-blue/10 rounded-lg">
        <div
          class="flex justify-between items-start gap-3 bg-lot-light-blue/20 w-full pt-8 pb-14 px-8 text-lot-dark rounded-md"
        >
          <div class="flex flex-col gap-2">
            <div class="flex gap-3">
              <span class="text-lot-dark-gray">Instructor:</span>
              <span>{{ session.instructor }}</span>
            </div>
            <div class="flex gap-3">
              <span class="text-lot-dark-gray">Location:</span>
              <span>{{ session.location }}</span>
            </div>
            <div class="flex gap-3">
              <span class="text-lot-dark-gray">Event Type:</span>
              <span>{{ session.type }}</span>
            </div>

            <span class="text-lot-dark-gray underline text-sm">More Details</span>

            <div class="grid grid-cols-2 gap-3">
              <span class="text-lot-dark-gray w-fit">Class Size:</span>
              <span>{{ session.classSize }}</span>
            </div>
            <!-- <div class="grid grid-cols-2 gap-3">
              <span class="w-fit">Seats Remaining:</span>
              <span>{{ session.classSize - session.booked }}</span>
            </div> -->
            <div class="grid grid-cols-2 gap-3">
              <span class="text-lot-dark-gray w-fit">Starts at:</span>
              <span>{{
                session.startDate | date : "MMMM d, y hh:mm a z"
              }}</span>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <span class="text-lot-dark-gray w-fit">Ends at:</span>
              <span>{{ session.endDate | date : "MMMM d, y hh:mm a z" }}</span>
            </div>
          </div>
          <div class="flex items-center gap-3">
            <span class="material-symbols-outlined text-6xl"> event </span>
            <div class="flex flex-col justify-end text-right">
              <span class="text-lot-blue font-semibold">{{
                session.endDate | date : "longDate"
              }}</span>
              <span>
                {{ session.startDate | date : "hh.mm a" }} -
                {{ session.endDate | date : "hh.mm a" }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </app-expandable>
    } @empty {
    <div
      class="flex flex-col items-center justify-center gap-3 bg-lot-light-blue/20 w-full h-40"
    >
      <p>No any sessions available.</p>
      <button class="button-primary" (click)="addSession()">Add Session</button>
    </div>
    } @if (sessions.length) {
    <a
      href="javascript:void(0)"
      class="flex items-center gap-3 text-lot-dark-gray border-y py-4"
      (click)="addSession()"
    >
      <i class="fa-solid fa-plus"></i>
      Add a Session
    </a>
    }
  </div>
</ng-template>

<ng-template #formTemplate>
  <form
    [formGroup]="form"
    (ngSubmit)="onSubmit()"
    class="flex flex-col gap-5 p-4"
  >
    <mat-dialog-content>
      <h1 class="text-2xl font-semibold mb-5">
        {{ f["id"].value ? "Edit " : "Create a New " }} Session
      </h1>
      @if (isLoading) {
      <mat-progress-bar mode="indeterminate" />
      } @if (error) {
      <p class="text-center font-semibold text-lot-danger">{{ error }}</p>
      }
      <div class="flex flex-col gap-5 min-h-96">
        <div class="form-lot-input">
          <div class="field">
            <input
              type="text"
              id="name"
              formControlName="name"
              placeholder="How to Make a Good Sale"
            />
            <label
              for="name"
              [class.error]="f['name'].invalid && f['name'].dirty"
            >
              Session Name
            </label>
          </div>
          @if(f['name'].errors && f['name'].invalid && f['name'].dirty){
          <div class="error">
            @if (f['name'].errors['required']) {
            <span>Session name is required</span>
            } @if (f['name'].errors['minlength']) {
            <span>Session name must be at least 3 characters</span>
            }
          </div>
          }
        </div>
        <div class="form-lot-input">
          <div class="field">
            <input
              type="text"
              id="instructor"
              formControlName="instructor"
              placeholder="John Doe"
            />
            <label for="instructor">Instructor</label>
          </div>
          <app-validation-text controlName="instructor" />
        </div>
        <div class="grid grid-cols-2 gap-5">
          <div class="form-lot-input">
            <div class="field">
              <input
                type="text"
                id="location"
                formControlName="location"
                placeholder="Online"
              />
              <label for="location">Location</label>
            </div>
            <app-validation-text controlName="location" />
          </div>
          <div class="form-lot-input">
            <div class="field">
              <select id="type" formControlName="type">
                <option value="VIRTUAL">Virtual</option>
                <option value="PHYSICAL">Physical</option>
              </select>
              <label for="type">Session Type</label>
            </div>
            <app-validation-text controlName="type" />
          </div>
          <div class="form-lot-input">
            <div class="field">
              <div class="flex items-center">
                <input
                  type="text"
                  id="startDate"
                  class="relative w-full"
                  [matDatepicker]="picker"
                  [min]="minDate"
                  formControlName="startDate"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="picker"
                  class="absolute right-0"
                />
              </div>

              <mat-datepicker #picker />
              <label for="startDate"> Start Date </label>
            </div>
            <app-validation-text controlName="startDate" />
          </div>
          <div class="form-lot-input">
            <div class="field">
              <div class="flex items-center">
                <input
                  type="text"
                  id="startTime"
                  formControlName="startTime"
                  class="relative w-full"
                  [matTimepicker]="startTimepicker"
                  [min]="minDate"
                />
                <mat-timepicker-toggle
                  [for]="startTimepicker"
                  matIconSuffix
                  [for]="startTimepicker"
                  class="absolute right-0"
                />
              </div>

              <mat-timepicker #startTimepicker />
              <label for="startTime"> Start Time </label>
            </div>
          </div>
          <div class="form-lot-input">
            <div class="field">
              <div class="flex items-center">
                <input
                  type="text"
                  id="endDate"
                  formControlName="endDate"
                  class="relative w-full"
                  [matDatepicker]="pickerEndDate"
                  [min]="minDate"
                />
                <mat-datepicker-toggle
                  matIconSuffix
                  [for]="pickerEndDate"
                  class="absolute right-0"
                />
              </div>

              <mat-datepicker #pickerEndDate />
              <label for="endDate"> End Date </label>
            </div>
            <app-validation-text controlName="endDate" />
          </div>
          <div class="form-lot-input">
            <div class="field">
              <div class="flex items-center">
                <input
                  type="text"
                  id="endTime"
                  formControlName="endTime"
                  class="relative w-full"
                  [matTimepicker]="endTimepicker"
                  [min]="minDate"
                />
                <mat-timepicker-toggle
                  [for]="endTimepicker"
                  matIconSuffix
                  [for]="endTimepicker"
                  class="absolute right-0"
                />
              </div>

              <mat-timepicker #endTimepicker />
              <label for="endTime"> End Time </label>
            </div>
          </div>
          <div class="form-lot-input">
            <div class="field">
              <input
                type="number"
                id="classSize"
                formControlName="classSize"
                placeholder="10"
              />
              <label for="classSize">Class Size</label>
            </div>
            <app-validation-text controlName="classSize" />
          </div>
          <div class="form-lot-input">
            <div class="field">
              <input
                type="url"
                id="url"
                formControlName="url"
                placeholder="www.meeting.com"
              />
              <label for="url">Session URL</label>
            </div>
          </div>
        </div>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <div class="flex justify-end items-center gap-4">
        <button
          class="button-primary-outline w-fit"
          (click)="dialogRef?.close()"
          type="button"
        >
          Cancel
        </button>
        <button class="button-primary w-fit py-1.5" type="submit">
          Save and Close
        </button>
      </div>
    </mat-dialog-actions>
  </form>
</ng-template>
