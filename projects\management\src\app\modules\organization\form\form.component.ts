import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ValidationTextComponent } from '@lms/shared';
import { Address, markControlsDirty, Organization } from '@lms/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatButtonModule } from '@angular/material/button';

@Component({
    selector: 'app-org-form',
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        ValidationTextComponent,
        MatDialogModule,
        MatDividerModule,
        MatButtonModule
    ],
    templateUrl: './form.component.html',
    styleUrls: ['./form.component.scss']
})
export class OrgFormComponent {
  form = new FormGroup({
    name: new FormControl('', Validators.required),
    description: new FormControl(''),
    billingName: new FormControl('', Validators.required),
    billingEmail: new FormControl('', Validators.required),
    billingAddress1: new FormControl('', Validators.required),
    billingCity: new FormControl('', Validators.required),
    billingZipCode: new FormControl('', Validators.required),
    billingCountry: new FormControl('', Validators.required),
  });

  constructor(
    public dialogRef: MatDialogRef<OrgFormComponent>,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA)
    public data: Organization
  ) {
    if (this.data) {
      this.form.patchValue({
        name: data.name,
        description: data.description,
        billingName: data.contact?.name,
        billingEmail: data.contact?.email,
        billingAddress1: data.contact?.address?.address1,
        billingCity: data.contact?.address?.city,
        billingZipCode: data.contact?.address?.zipCode,
        billingCountry: data.contact?.address?.country,
      });
    }
  }

  onSubmit() {
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const payload = {
      name: this.form.value.name,
      description: this.form.value.description,
      contact: {
        name: this.form.value.billingName,
        email: this.form.value.billingEmail,
        address: {
          address1: this.form.value.billingAddress1,
          city: this.form.value.billingCity,
          zipCode: this.form.value.billingZipCode,
          country: this.form.value.billingCountry,
        } as Address,
      },
    } as any;
    if (this.data && this.data.id) {
      payload['id'] = this.data.id;
    }
    this.dialogRef.close({ payload });
  }
}
