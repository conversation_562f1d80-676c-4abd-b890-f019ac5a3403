import {
  Component,
  inject,
  Input,
  OnInit,
  TemplateRef,
  viewChild,
} from '@angular/core';
import {
  CourseCoreService,
  Enrollment,
  getId,
  GroupItem,
  InstructorEnrollment,
  LearningInstructorService,
  Team,
  TeamCoreService,
  ToastMessageType,
  UserItem,
  UsersCoreService,
} from '@lms/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { toSignal } from '@angular/core/rxjs-interop';
import { debounceTime, map, startWith, switchMap } from 'rxjs';
import { NgTemplateOutlet } from '@angular/common';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';

@Component({
  selector: 'app-instructor-assignment',
  templateUrl: './assign-team.component.html',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
    NgTemplateOutlet,
    MatDatepickerModule,
    MatProgressBarModule,
  ],
})
export class AssignmentComponent implements OnInit {
  service = inject(CourseCoreService);
  teamService = inject(TeamCoreService);
  userService = inject(UsersCoreService);
  readonly instructorService = inject(LearningInstructorService);
  readonly dialog = inject(MatDialog);

  @Input({ required: true }) data: {
    id: string;
    label: string;
    courseIds: string[];
  };

  dueDateRef = viewChild<TemplateRef<any>>('dueDateTmp');
  minDate = new Date();

  isLoading = false;
  error?: string;
  dialogRef: MatDialogRef<any>;

  teams: (Team & { teamOnly: boolean; enrollId: string; tag?: 'NEW' | 'DELETE' })[] = [];
  groups: (GroupItem & { enrollId: string; tag?: 'NEW' | 'DELETE' })[] = [];
  users: (UserItem & {
    name: string;
    enrollId: string;
    tag?: 'NEW' | 'DELETE';
  })[] = [];

  itemsCourseToSave: { items: Enrollment[]; toDelete: string[] } = {
    items: [],
    toDelete: [],
  };
  itemsInstructorToSave: { items: InstructorEnrollment[]; toDelete: string[] } =
    {
      items: [],
      toDelete: [],
    };

  get readToSave() {
    return (
      this.itemsInstructorToSave.items.length ||
      this.itemsInstructorToSave.toDelete.length
    );
  }
  get teamItems() {
    return this.teams
      .filter((x) => !!x.id && x.teamOnly)
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  dueDateControl = new FormControl('');

  teamControl = new FormControl('');
  teamOptions = toSignal(
    this.teamControl.valueChanges.pipe(
      startWith(''),
      switchMap((value) =>
        this.teamService
          .queryTeamsAndGroups()
          .then((res) =>
            res
              .filter((option) =>
                option.name.toLowerCase().includes(value || '')
              )
              .filter((t) => !this.teams.some((ex) => ex.id === t.id))
          )
      )
    )
  );

  groupControl = new FormControl('');
  groupOptions = toSignal(
    this.groupControl.valueChanges.pipe(
      startWith(''),
      switchMap((value) =>
        this.teamService.queryTeamsAndGroups().then((res) => {
          const teams = res
            .filter((t) => t.groups?.length)
            .map((t) =>
              (t.groups || []).map((g) => ({
                ...g,
                team: t,
                name: `${t.name}: ${g.name}`,
              }))
            )
            .flat()
            .filter((option) =>
              option.name.toLowerCase().includes(value || '')
            );
          return teams;
        })
      )
    )
  );

  userControl = new FormControl('');
  userOptions = toSignal(
    this.userControl.valueChanges.pipe(
      startWith(''),
      debounceTime(500),
      switchMap((value) =>
        this.userService
          .getUserOnlyByQuery(value || '')
          .then((res) =>
            res.data
              .filter((user) => !this.users.some((ex) => ex.id === user.id))
              .map((u) => ({ ...u, name: u.username }))
          )
      )
    )
  );

  displayFn = (item: any) => item?.name || '';

  async ngOnInit() {
    await this.release();
    this.instructorService.actionTrigger.subscribe((res) => {
      if (res.type === 'LEARNINGPATH') {
        this.getDueDate();
      }
    });
  }

  private async release() {
    const res = await this.instructorService.getEnrollment(this.data.id);
    if (res.error) {
      throw new Error('Unable to fetch data. Please refresh this page');
    }
    this.teams = res.data.teams.sort((a, b) => a.name.localeCompare(b.name));
    this.groups = res.data.groups.sort((a, b) => a.name.localeCompare(b.name));
    this.users = res.data.users
      .map((u) => ({ ...u, name: u.username }))
      .sort((a, b) => a.username.localeCompare(b.username));
  }

  removeItem(id: string, type: 'TEAM' | 'GROUP' | 'USER') {
    this.error = undefined;
    if (type === 'TEAM') {
      this.teams = this.teams.map((x) => ({
        ...x,
        tag: x.id === id ? 'DELETE' : x.tag,
      }));
      this.teams = this.teams.filter(
        (x) => !(x.id === id && !x.enrollId && x.tag === 'DELETE')
      );
    }
    if (type === 'GROUP') {
      this.groups = this.groups.map((x) => ({
        ...x,
        tag: x.id === id ? 'DELETE' : x.tag,
      }));
      this.groups = this.groups.filter(
        (x) => !(x.id === id && !x.enrollId && x.tag === 'DELETE')
      );
    }
    if (type === 'USER') {
      this.users = this.users.map((x) => ({
        ...x,
        tag: x.id === id ? 'DELETE' : x.tag,
      }));
      this.users = this.users.filter(
        (x) => !(x.id === id && !x.enrollId && x.tag === 'DELETE')
      );
    }
    this.refreshCourseToSave();
    this.refreshInstructorToSave();
  }

  async selectItem(item: any, type: 'TEAM' | 'GROUP' | 'USER') {
    this.error = undefined;
    if (type === 'TEAM') {
      const dupCheck = await this.checkEnrolledInTeamGroup(
        {
          teamId: item.id,
          teamName: item.name,
          groupName: '',
        },
        true
      );
      if (!this.teams.some((t) => t.id === item.id) && dupCheck === 1) {
        this.teams.push({ ...item, teamOnly: true, tag: 'NEW' });
        this.teams = this.teams.sort((a, b) => a.name.localeCompare(b.name));
      }
      this.teamControl.reset();
    }
    if (type === 'GROUP') {
      const dupCheck = await this.checkEnrolledInTeamGroup(
        {
          teamId: item.team?.id,
          teamName: item.team?.name,
          groupName: item.name,
        },
        false
      );
      if (!this.groups.some((t) => t.id === item.id) && dupCheck === 1) {
        this.groups.push({ ...item, tag: 'NEW' });
        this.groups = this.groups.sort((a, b) => a.name.localeCompare(b.name));
      }
      this.groupControl.reset();
    }
    if (type === 'USER') {
      const dupCheck = await this.checkUserEnrolledInTeamGroup(item);
      if (!this.users.some((t) => t.id === item.id) && dupCheck === 1) {
        this.users.push({ ...item, tag: 'NEW' });
        this.users = this.users.sort((a, b) =>
          a.username.localeCompare(b.username)
        );
      }
      this.userControl.reset();
    }
    this.refreshCourseToSave();
    this.refreshInstructorToSave();
  }

  getDueDate() {
    if (!this.readToSave) return;
    this.dialogRef = this.dialog.open(this.dueDateRef()!);
  }

  async submit() {
    this.error = undefined;
    this.dialogRef.close();
    const dueDate = this.dueDateControl.value
      ? new Date(this.dueDateControl.value!).toISOString()
      : undefined;

    this.refreshCourseToSave(dueDate);
    this.refreshInstructorToSave(dueDate);

    this.isLoading = true;
    const res = await Promise.all([
      this.service.saveEnrollment(this.itemsCourseToSave),
      this.instructorService.saveEnrollment(this.itemsInstructorToSave),
    ]);
    this.isLoading = false;
    this.dialogRef.close();
    this.postResponse(res);
    await this.release();
    this.itemsCourseToSave = {
      items: [],
      toDelete: [],
    };
    this.itemsInstructorToSave = {
      items: [],
      toDelete: [],
    };
  }

  private postResponse(res: [string[], string[]]) {
    if (!res[0].length) {
      this.service.state.openToast({
        title: 'Save Request Successful',
        message: 'course Enrollment saved successfully',
        type: ToastMessageType.SUCCESS,
      });
    }
    if (!res[1].length) {
      this.service.state.openToast({
        title: 'Save Request Successful',
        message: this.data.label + ' enrollment saved successfully',
        type: ToastMessageType.SUCCESS,
      });
    }
    if (res[0].length) {
      this.error = res[0].join(', ');
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'course enrollment failed: ' + this.error,
        type: ToastMessageType.ERROR,
      });
    }
    if (res[1].length) {
      this.error = res[1].join(', ');
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: this.data.label + ' failed: ' + this.error,
        type: ToastMessageType.ERROR,
      });
    }
  }

  private refreshCourseToSave(dueDate?: string) {
    const itemsToDelete = [
      ...this.users
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
      ...this.teams
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
      ...this.groups
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
    ].filter(Boolean);

    const items = this.users
      .filter((u) => u.tag === 'NEW')
      .map(
        (u) =>
          ({
            id: u.enrollId,
            course: '-',
            user: u.id,
            dueDate: dueDate,
          } as Enrollment)
      );
    items.push(
      ...this.teamItems
        .filter((u) => u.tag === 'NEW')
        .map(
          (u) =>
            ({
              id: u.enrollId,
              course: '-',
              team: u.id,
              dueDate: dueDate,
            } as Enrollment)
        )
    );
    items.push(
      ...this.groups
        .filter((u) => u.tag === 'NEW')
        .map(
          (u) =>
            ({
              id: u.enrollId,
              course: '-',
              team: u.team?.id,
              group: u.id,
              dueDate: dueDate,
            } as Enrollment)
        )
    );
    const courseItems = [] as Enrollment[];
    this.data.courseIds.forEach((courseId) => {
      courseItems.push(
        ...items.map((x) => ({
          ...x,
          course: courseId,
        }))
      );
    });
    this.itemsCourseToSave = { items: courseItems, toDelete: itemsToDelete };
  }
  private refreshInstructorToSave(dueDate?: string) {
    const itemsToDelete = [
      ...this.users
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
      ...this.teams
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
      ...this.groups
        .filter((u) => u.tag === 'DELETE' && u.enrollId)
        .map((u) => u.enrollId),
    ].filter(Boolean);

    const items = this.users
      .filter((u) => u.tag === 'NEW')
      .map(
        (u) =>
          ({
            id: u.enrollId,
            instructor: this.data.id,
            user: u.id,
            dueDate: dueDate,
          } as InstructorEnrollment)
      );
    items.push(
      ...this.teamItems
        .filter((u) => u.tag === 'NEW')
        .map(
          (u) =>
            ({
              id: u.enrollId,
              instructor: this.data.id,
              team: u.id,
              dueDate: dueDate,
            } as InstructorEnrollment)
        )
    );
    items.push(
      ...this.groups
        .filter((u) => u.tag === 'NEW')
        .map(
          (u) =>
            ({
              id: u.enrollId,
              instructor: this.data.id,
              team: u.team?.id,
              group: u.id,
              dueDate: dueDate,
            } as InstructorEnrollment)
        )
    );
    this.itemsInstructorToSave = { items, toDelete: itemsToDelete };
  }

  async checkUserEnrolledInTeamGroup({
    id: userId,
    name,
  }: {
    id: string;
    name: string;
  }) {
    const res = await this.service.getUsersInTeamsAndGroups([userId]);
    const teamIds = res
      .map((t) => getId(t.team) ?? getId(t.group?.team))
      .filter(Boolean);

    const dupUsers = this.teamItems.filter((t) => teamIds.includes(t.id));

    if (!dupUsers.length) return 1;
    this.service.state.openToast({
      message: dupUsers
        .map(
          (u) => `<strong>${name}</strong> is already in team:<br/> ${u.name}`
        )
        .join(', '),
      type: ToastMessageType.WARNING,
    });
    return 0;
  }
  async checkEnrolledInTeamGroup(
    {
      teamId,
      teamName,
      groupName,
    }: { teamId: string; teamName: string; groupName: string },
    isTeam: boolean
  ) {
    if (isTeam) {
      if (!this.groups.some((g) => g.team?.id === teamId)) return 1;
      this.service.state.openToast({
        message: `Team <b>${teamName}</b> already has groups assigned,<br/> you have to add individual groups of this time <br/> or remove all assigned groups of this team and add the team again.`,
        type: ToastMessageType.WARNING,
      });
      return 0;
    }
    if (!this.teamItems.some((g) => g.id === teamId)) return 1;
    this.service.state.openToast({
      message: `Group <b>${groupName}</b> cannot be added <br/> because it belongs to team <b>${teamName}</b> <br/> which is already assigned.`,
      type: ToastMessageType.WARNING,
    });
    return 0;
  }
}
