import { Component, inject, Input, OnInit, signal } from '@angular/core';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  Course,
  CourseCoreService,
  markControlsDirty,
  MediaBucket,
  MediaCenterService,
  stripHtml,
  ToastMessageType,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { FileLoaderComponent, FileType, RichTextComponent } from '@lms/shared';
import { MatDialog } from '@angular/material/dialog';
import { CourseCoWriterComponent } from './selector/co-writer/co-writer.component';
import { CourseRequisiteComponent } from './selector/requites/requites.component';
import { MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-course-settings',
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    RichTextComponent,
    FileLoaderComponent,
    MatChipsModule,
    MatIconModule,
  ],
  templateUrl: 'settings.component.html',
})
export class CourseSettingsComponent implements OnInit {
  readonly mediaService = inject(MediaCenterService);
  readonly service = inject(CourseCoreService);
  readonly dialog = inject(MatDialog);

  @Input({ required: true }) data: Course;

  form = new FormGroup({
    name: new FormControl('', [Validators.required, Validators.minLength(3)]),
    description: new FormControl('', Validators.required),
    venue: new FormControl(''),
    duration: new FormControl(0),
    language: new FormControl(''),
    frequency: new FormControl(''),
    requiredScore: new FormControl(70),
    scormCourseId: new FormControl({ value: '3M', disabled: true }),
  });

  uploadType: FileType = 'image';

  frequencyOptions = [
    {
      id: '3M',
      label: 'Every 3 months',
    },
    {
      id: '6M',
      label: 'Every 6 months',
    },
    {
      id: '1YR',
      label: 'Every 1 year',
    },
    {
      id: '2Y',
      label: 'Every 2 years',
    },
    {
      id: '3Y',
      label: 'Every 3 years',
    },
  ];

  preRequisites: {
    id: string;
    name: string;
    cover: string;
    type: 'PRE' | 'POST';
  }[] = [];
  postRequisites: {
    id: string;
    name: string;
    cover: string;
    type: 'PRE' | 'POST';
  }[] = [];
  readonly tags = signal<string[]>([]);

  coWriters: { id: string; name: string; image: string }[] = [];

  isLoading = false;
  error?: string;
  uploadedFile?: File;

  get f() {
    return this.form.controls;
  }
  get author() {
    return this.data.creator;
  }

  ngOnInit(): void {
    if (this.data) {
      this.tags.set(this.data.tags?.split(',') || []);
      this.form.patchValue({
        name: this.data.name,
        description: this.data.description,
        venue: this.data.venue,
        duration: this.data.duration,
        language: this.data.language,
        frequency: this.data.frequency,
        requiredScore: this.data.requiredScore,
        scormCourseId: this.data.scormCourseId,
      });
      this.preRequisites = (this.data.requisites || []).filter(
        (r) => r.type === 'PRE'
      );
      this.postRequisites = (this.data.requisites || []).filter(
        (r) => r.type === 'POST'
      );
      this.coWriters = this.data.coWriters || [];
    }

    this.service.enrollTrigger.subscribe((res) => {
      if (res.type === 'SETTINGS') {
        this.onSubmit();
      }
    });
  }

  removeTag(keyword: string) {
    this.tags.update((keywords) => {
      const index = keywords.indexOf(keyword);
      if (index < 0) {
        return keywords;
      }

      keywords.splice(index, 1);
      // this.announcer.announce(`removed ${keyword}`);
      return [...keywords];
    });
  }

  addTag(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();

    // Add our keyword
    if (value) {
      this.tags.update((keywords) => [...keywords, value]);
    }

    // Clear the input value
    event.chipInput!.clear();
  }

  addCoWriter() {
    this.dialog
      .open(CourseCoWriterComponent, {
        minWidth: '400px',
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.coWriters = res.users;
        }
      });
  }

  removeCoWriter(index: number) {
    this.coWriters.splice(index, 1);
  }

  addRequisite(type: 'PRE' | 'POST') {
    this.dialog
      .open(CourseRequisiteComponent, {
        minWidth: '400px',
        data: type,
      })
      .afterClosed()
      .subscribe((res) => {
        if (res?.courses) {
          if (type === 'PRE') {
            this.preRequisites = res.courses;
          } else {
            this.postRequisites = res.courses;
          }
        }
      });
  }

  removeRequisite(index: number, type: 'PRE' | 'POST') {
    if (type === 'PRE') {
      this.preRequisites.splice(index, 1);
    } else {
      this.postRequisites.splice(index, 1);
    }
  }

  async onFileUploaded(files: File[]) {
    this.uploadedFile = files[0];
  }

  async onSubmit() {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }
    const cover = await this.saveCover();
    const payload = {
      id: this.data.id,
      name: this.form.value.name,
      description: this.form.value.description,
      cover: cover,
      short: stripHtml(this.form.value.description!)?.substring(0, 250),
      tags: this.tags().length ? this.tags().join(',') : undefined,
      language: this.form.value.language,
      frequency: this.form.value.frequency,
      requiredScore: this.form.value.requiredScore,
      requisites: [...this.preRequisites, ...this.postRequisites],
      coWriters: this.coWriters,
    } as any;

    const res = await this.service.update(payload);
    this.isLoading = false;
    if (res.error) {
      this.error = res.error;
      this.service.state.openToast({
        title: 'Save Request Failed',
        message: 'Failed: ' + res.error,
        type: ToastMessageType.ERROR,
      });
      return;
    }
    this.service.state.openToast({
      title: 'Save Request Successful',
      message: 'Course saved successfully',
      type: ToastMessageType.SUCCESS,
    });
    this.service.courseSource.reload();
  }

  async saveCover() {
    const file = this.uploadedFile;
    if (!file) return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    const folder = (this.service.organization?.name ?? '_orgo_logo_')
      .replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    this.isLoading = true;
    const { data, error } = await this.mediaService.uploadFile(
      file,
      MediaBucket.IMAGES,
      folder
    );

    if (error) {
      this.isLoading = false;
      this.error = error;
      return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    }

    if (!data) {
      this.isLoading = false;
      return this.data.cover ?? 'assets/images/new/card-3.jpeg';
    }
    this.isLoading = false;
    return data.url ?? this.data.cover ?? 'assets/images/new/card-3.jpeg';
  }
}
