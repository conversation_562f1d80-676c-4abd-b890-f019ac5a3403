import {
  Component,
  EventEmitter,
  inject,
  OnChang<PERSON>,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  CourseCoreService,
  markControlsDirty,
  MediaBucket,
  MediaCenterService,
} from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { NgStyle, NgTemplateOutlet } from '@angular/common';
import { QuillEditorComponent } from 'ngx-quill';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
  MatDialog,
} from '@angular/material/dialog';
import {
  ExpandableComponent,
  FileLoaderComponent,
  getFlashcardsTemplate,
  LessonWidgetTypes,
  RichImageComponent,
  RichTextComponent,
} from '@lms/shared';
import { WidgetCardFormComponent } from './card-form/card-form.component';
import { v4 as gUID } from 'uuid';
import { MediaCenterComponent } from '../../../../core';

@Component({
  selector: 'app-lesson-widget-editor',
  imports: [
    NgStyle,
    FormsModule,
    ReactiveFormsModule,
    MatProgressBarModule,
    QuillEditorComponent,
    MatSelectModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatIconModule,
    MatTabsModule,
    MatExpansionModule,
    NgTemplateOutlet,
    MatDialogModule,
    ExpandableComponent,
    WidgetCardFormComponent,
    RichTextComponent,
    RichImageComponent,
    FileLoaderComponent,
  ],
  templateUrl: './lesson-widget-editor.component.html',
})
export class LessonWidgetEditorComponent implements OnInit, OnChanges {
  readonly service = inject(CourseCoreService);
  fileService = inject(MediaCenterService);
  readonly fb = inject(FormBuilder);
  readonly dialog = inject(MatDialog);
  readonly dialogRef = inject(MatDialogRef<LessonWidgetEditorComponent>);
  readonly data: { widgetType: string; widgetContent: any } =
    inject(MAT_DIALOG_DATA);

  widgetType!: string;
  widgetContent: any;
  @Output() contentUpdated = new EventEmitter<any>();

  isLoading = false;
  error?: string;

  // Available widget types
  widgetTypes = LessonWidgetTypes;

  // Main form
  form!: FormGroup;

  // Editor style for quill editor
  editorStyle = {
    height: '200px',
    backgroundColor: '#ffffff',
    border: 'none',
  };

  ngOnInit(): void {
    // Initialize from dialog data
    this.widgetType = this.data.widgetType;
    this.widgetContent = this.data.widgetContent;
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // This is for when the component is used as a regular component, not in a dialog
    if (
      (changes['widgetType'] || changes['widgetContent']) &&
      this.widgetType
    ) {
      this.initializeForm();
    }
  }

  initializeForm(): void {
    // Create base form with meta section that's common to all widget types
    this.form = this.fb.group({
      meta: this.fb.group({
        width: [this.widgetContent?.meta?.width || 100],
        height: [this.widgetContent?.meta?.height],
        padding: [this.widgetContent?.meta?.padding || 10],
        margin: [this.widgetContent?.meta?.margin],
        background: [this.widgetContent?.meta?.background || 'lot-gray/20'],
        color: [this.widgetContent?.meta?.color || 'lot-dark'],
      }),
    });

    // Add specific form controls based on widget type
    this.addWidgetSpecificControls();
  }

  loadMedia() {
    this.dialog.open(MediaCenterComponent, {
      maxWidth: '80vw',
      width: '100%',
    });
    //MediaCenterComponent
  }

  // async onFileUploaded(files: File[], form: FormGroup, prop: string) {
  //   if (!files.length) return;
  //   const file = files[0];
  //   form.get(prop)?.setValue(res.data);
  // }

  addWidgetSpecificControls(): void {
    switch (this.widgetType) {
      case 'TEXT':
      case 'PROCESS':
      case 'SORTING':
      case 'INTERACTIVE':
        this.addTextControls();
        break;
      case 'IMAGE':
        this.addImageControls();
        break;
      case 'VIDEO':
        this.addVideoControls();
        break;
      case 'FLASHCARD':
        this.addFlashcardControls();
        break;
      case 'QUIZ':
        this.addQuizControls();
        break;
      case 'CARD':
        this.addCardControls();
        break;
      case 'QUOTE':
        this.addQuoteControls();
        break;
      case 'LIST':
        this.addListControls();
        break;
      case 'GALLERY':
        this.addGalleryControls();
        break;
      case 'RESOURCE':
        this.addResourceControls();
        break;
      case 'CHART':
        this.addChartControls();
        break;
      case 'DIVIDER':
        this.addDividerControls();
        break;
      default:
        this.addTextControls(); // Default to text controls
        break;
    }
  }

  addTextControls(): void {
    this.form.addControl(
      'heading',
      new FormControl(this.widgetContent?.heading || '')
    );
    this.form.addControl(
      'subHeading',
      new FormControl(this.widgetContent?.subHeading || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );

    // Add columns if they exist
    if (this.widgetContent?.columns) {
      this.form.addControl(
        'columnSize',
        new FormControl(this.widgetContent?.columnSize || 2)
      );
      this.form.addControl(
        'columns',
        this.fb.array(
          this.widgetContent?.columns.map((col: string) =>
            this.fb.control(col)
          ) || []
        )
      );
    } else {
      this.form.addControl('columnSize', new FormControl(2));
      this.form.addControl('columns', this.fb.array([]));
    }

    // Add table if it exists
    if (this.widgetContent?.table) {
      const tableGroup = this.fb.group({
        headers: this.fb.array(
          this.widgetContent?.table.headers.map((header: string) =>
            this.fb.control(header)
          ) || []
        ),
        rows: this.fb.array([]),
      });

      // Add rows
      if (this.widgetContent?.table.rows) {
        const rowsArray = tableGroup.get('rows') as FormArray;
        this.widgetContent.table.rows.forEach((row: string[]) => {
          rowsArray.push(
            this.fb.array(row.map((cell: string) => this.fb.control(cell)))
          );
        });
      }

      this.form.addControl('table', tableGroup);
    } else {
      this.form.addControl(
        'table',
        this.fb.group({
          headers: this.fb.array([]),
          rows: this.fb.array([]),
        })
      );
    }
  }

  addImageControls(): void {
    this.form.addControl(
      'url',
      new FormControl(this.widgetContent?.url || '', Validators.required)
    );
    this.form.addControl(
      'caption',
      new FormControl(this.widgetContent?.caption || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'position',
      new FormControl(this.widgetContent?.position || '')
    );
    this.form.addControl(
      'textPosition',
      new FormControl(this.widgetContent?.textPosition || '')
    );
    this.form.addControl('fileUrlType', new FormControl(1));
  }

  addVideoControls(): void {
    this.form.addControl(
      'url',
      new FormControl(this.widgetContent?.file?.url || '', Validators.required)
    );
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'isEmbedded',
      new FormControl(this.widgetContent?.file?.isEmbedded || '')
    );
    this.form.addControl(
      'preventSkip',
      new FormControl(this.widgetContent?.file?.preventSkip || '')
    );
    this.form.addControl(
      'requirePass',
      new FormControl(this.widgetContent?.requirePass || true)
    );
    this.form.addControl(
      'fileUrlType',
      new FormControl(this.widgetContent?.file?.isEmbedded ? 3 : 1)
    );
    this.form.addControl('file', new FormControl<File | null>(null));
  }

  addFlashcardControls(): void {
    this.form.addControl('front', new FormControl(this.widgetContent?.front));
    this.form.addControl('back', new FormControl(this.widgetContent?.back));

    this.form.addControl(
      'requirePass',
      new FormControl(this.widgetContent?.requirePass || '')
    );
    const template = getFlashcardsTemplate(0);
    const cardArray = this.fb.array([] as FormGroup[]);
    if (this.widgetContent?.cards?.length) {
      this.widgetContent.cards.forEach((card: any) => {
        cardArray.push(
          this.fb.group({
            id: [gUID()],
            front: [card.front || template.cards[0].front],
            back: [card.back.content ? card.back : template.cards[0].back],
          })
        );
      });
    }
    this.form.addControl('cards', cardArray);
    this.form.addControl('cardValues', cardArray);
  }

  addQuizControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'canPrevious',
      new FormControl(this.widgetContent?.canPrevious || false)
    );
    this.form.addControl(
      'showSummary',
      new FormControl(this.widgetContent?.showSummary || false)
    );
    this.form.addControl(
      'passageScore',
      new FormControl(this.widgetContent?.passageScore || 70)
    );
    this.form.addControl(
      'requirePass',
      new FormControl(this.widgetContent?.requirePass || true)
    );
    // Add questions array
    const questionsArray = this.fb.array([] as FormGroup[]);

    for (const question of this.widgetContent?.questions || []) {
      const optionsArray = this.fb.array([] as FormGroup[]);
      if (question.options) {
        question.options.forEach((option: any) => {
          optionsArray.push(
            this.fb.group({
              id: [option.id || ''],
              label: [option.label || ''],
              isCorrect: [option.isCorrect || false],
            })
          );
        });
      }

      questionsArray.push(
        this.fb.group({
          id: [question.id || ''],
          title: [question.title || ''],
          description: [question.description || ''],
          type: [question.type || 'checkbox'],
          options: optionsArray,
        })
      );
    }

    this.form.addControl('questions', questionsArray);
  }

  addCardControls(): void {
    this.form.addControl(
      'name',
      new FormControl(this.widgetContent?.name || '')
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'stackPosition',
      new FormControl(this.widgetContent?.stackPosition || 'center')
    );
    this.form.addControl(
      'image',
      new FormControl(this.widgetContent?.image || '')
    );
    this.form.addControl(
      'requirePass',
      new FormControl(this.widgetContent?.requirePass || '')
    );
  }

  addQuoteControls(): void {
    this.form.addControl(
      'quote',
      new FormControl(this.widgetContent?.quote || '', Validators.required)
    );
    this.form.addControl(
      'author',
      new FormControl(this.widgetContent?.author || '')
    );
    this.form.addControl(
      'source',
      new FormControl(this.widgetContent?.source || '')
    );
    this.form.addControl(
      'style',
      new FormControl(this.widgetContent?.style || 'simple')
    );
    this.form.addControl(
      'authorImage',
      new FormControl(this.widgetContent?.authorImage || '')
    );
    this.form.addControl(
      'alignment',
      new FormControl(this.widgetContent?.alignment || 'center')
    );
  }

  addListControls(): void {
    this.form.addControl(
      'style',
      new FormControl(this.widgetContent?.style || 'disc')
    );
    this.form.addControl(
      'heading',
      new FormControl(this.widgetContent?.heading || '')
    );

    // Add items array
    const itemsArray = this.fb.array([] as FormGroup[]);
    if (this.widgetContent?.items) {
      this.widgetContent.items.forEach((item: any) => {
        itemsArray.push(
          this.fb.group({
            text: [item.text || ''],
            checked: [item.checked || false],
          })
        );
      });
    }
    this.form.addControl('items', itemsArray);
  }

  addGalleryControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'type',
      new FormControl(this.widgetContent?.type || 'grid')
    );
    this.form.addControl(
      'columnSize',
      new FormControl(this.widgetContent?.columnSize || 1)
    );

    // Add images array
    const imagesArray = this.fb.array([] as FormGroup[]);
    if (this.widgetContent?.images) {
      this.widgetContent.images.forEach((image: any) => {
        imagesArray.push(
          this.fb.group({
            url: [image.url || '', Validators.required],
            caption: [image.caption || ''],
            fileUrlType: [1],
          })
        );
      });
    }
    this.form.addControl('images', imagesArray);
  }

  addResourceControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'url',
      new FormControl(this.widgetContent?.file?.url || '', Validators.required)
    );
    this.form.addControl(
      'description',
      new FormControl(this.widgetContent?.description || '')
    );
    this.form.addControl(
      'type',
      new FormControl(this.widgetContent?.type || 'link')
    );
    this.form.addControl(
      'fileUrlType',
      new FormControl(this.widgetContent?.fileUrlType || 1)
    );
    this.form.addControl(
      'requirePass',
      new FormControl(this.widgetContent?.requirePass || true)
    );
    this.form.addControl('fileUrlType', new FormControl(1));
  }

  addChartControls(): void {
    this.form.addControl(
      'title',
      new FormControl(this.widgetContent?.title || '')
    );
    this.form.addControl(
      'type',
      new FormControl(this.widgetContent?.type || 'bar')
    );
    this.form.addControl(
      'data',
      new FormControl(this.widgetContent?.data || '{}')
    );
  }

  addDividerControls(): void {
    this.form.addControl(
      'style',
      new FormControl(this.widgetContent?.style || 'solid')
    );
    this.form.addControl(
      'thickness',
      new FormControl(this.widgetContent?.thickness || 1)
    );
  }

  // Helper methods for form arrays
  getFormArray(path: string): FormArray {
    return this.form.get(path) as FormArray;
  }

  getFormArrayForChild(control: string, form: AbstractControl): FormArray {
    return form.get(control) as FormArray;
  }

  getFormForChild(form: AbstractControl) {
    return Object.values((form as FormGroup).controls);
  }

  addColumn(): void {
    const columns = this.getFormArray('columns');
    columns.push(this.fb.control(''));
  }

  removeColumn(index: number): void {
    const columns = this.getFormArray('columns');
    columns.removeAt(index);
  }

  addTableHeader(): void {
    const headers = this.getFormArray('table.headers');
    headers.push(this.fb.control(''));
    const rows = this.getFormArray('table.rows');
    for (let i = 0; i < rows.length; i++) {
      const row = rows.at(i) as FormArray;
      row.push(this.fb.control(''));
    }
  }

  removeTableHeader(index: number): void {
    const headers = this.getFormArray('table.headers');
    headers.removeAt(index);
    const rows = this.getFormArray('table.rows');
    for (let i = 0; i < rows.length; i++) {
      const row = rows.at(i) as FormArray;
      row.removeAt(index);
    }
  }

  addTableRow(): void {
    const rows = this.getFormArray('table.rows');
    const headers = this.getFormArray('table.headers');
    const cells = this.fb.array([]);

    // Create a cell for each header
    for (let i = 0; i < headers.length; i++) {
      cells.push(this.fb.control(''));
    }

    rows.push(cells);
  }

  removeTableRow(index: number): void {
    const rows = this.getFormArray('table.rows');
    rows.removeAt(index);
  }

  removeFlashcard(index: number): void {
    const cards = this.getFormArray('cards');
    cards.removeAt(index);
  }

  addQuestion(): void {
    const questions = this.getFormArray('questions');
    questions.push(
      this.fb.group({
        id: [gUID()],
        title: [''],
        description: [''],
        type: ['checkbox'],
        options: this.fb.array([]),
      })
    );
  }

  addFlashcard(): void {
    const cards = this.getFormArray('cards');
    const template = getFlashcardsTemplate(0);
    cards.push(
      this.fb.group({
        id: [gUID()],
        front: [template.cards[0].front],
        back: [template.cards[0].back],
      })
    );
  }

  removeQuestion(index: number): void {
    const questions = this.getFormArray('questions');
    questions.removeAt(index);
  }

  addOption(questionIndex: number): void {
    const question = this.getFormArray('questions').at(
      questionIndex
    ) as FormGroup;
    const options = question.get('options') as FormArray;
    options.push(
      this.fb.group({
        id: [gUID()],
        label: [''],
        isCorrect: [false],
      })
    );
  }

  removeOption(questionIndex: number, optionIndex: number): void {
    const question = this.getFormArray('questions').at(
      questionIndex
    ) as FormGroup;
    const options = question.get('options') as FormArray;
    options.removeAt(optionIndex);
  }

  addListItem(): void {
    const items = this.getFormArray('items');
    items.push(
      this.fb.group({
        text: [''],
        checked: [false],
      })
    );
  }

  removeListItem(index: number): void {
    const items = this.getFormArray('items');
    items.removeAt(index);
  }

  addGalleryImage(): void {
    const images = this.getFormArray('images');
    images.push(
      this.fb.group({
        url: ['', Validators.required],
        caption: [''],
        description: [''],
      })
    );
  }

  removeGalleryImage(index: number): void {
    const images = this.getFormArray('images');
    images.removeAt(index);
  }

  async onSubmit(): Promise<void> {
    if (this.isLoading) return;
    if (this.form.invalid) {
      markControlsDirty(this.form);
      return;
    }

    this.isLoading = true;

    try {
      // Prepare the content based on the form
      const content = this.prepareContentFromForm();

      const file = this.form.get('file')?.value as File;
      if (file) {
        const url = await this.saveFile(file);
        content.url = url;
        if (content?.file?.url) {
          content.file.url = url;
        }
      }

      // If used in a dialog, close it with the result
      if (this.dialogRef) {
        this.dialogRef.close(content);
      } else {
        // Otherwise emit the updated content (for regular component usage)
        this.contentUpdated.emit(content);
      }

      this.error = undefined;
    } catch (error) {
      this.error = 'An error occurred while saving the widget content.';
      console.error(error);
    } finally {
      this.isLoading = false;
    }
  }

  async saveFile(file: File) {
    const folder = this.service.state
      .user()
      .organization?.name.replace(/\s/g, '-')
      .toLowerCase()
      .trim();

    const { data, error } = await this.fileService.uploadFile(
      file,
      this.widgetType === 'IMAGE'
        ? MediaBucket.IMAGES
        : this.widgetType === 'VIDEO'
        ? MediaBucket.VIDEOS
        : MediaBucket.DOCUMENTS,
      folder
    );

    if (error) {
      this.error = error;
      return;
    }
    if (!data) return;

    return data.url;
  }

  saveFlashcardForm(
    card: AbstractControl<any, any>,
    data: any,
    type: 'front' | 'back'
  ) {
    if (type === 'front') {
      card.get('front')?.setValue(data);
    } else {
      card.get('back')?.setValue(data);
    }
    const cardValues = this.form.get('cardValues') as FormArray;
    const index = cardValues.controls.findIndex(
      (x) => x.value.id === card.value.id
    );
    if (index !== -1) {
      cardValues.setControl(index, card);
    }
  }

  prepareContentFromForm(): any {
    const formValue = this.form.value;
    const content: any = {
      meta: formValue.meta,
    };

    switch (this.widgetType) {
      case 'TEXT':
      case 'AITEXT':
      case 'PROCESS':
      case 'SORTING':
      case 'INTERACTIVE':
        if (formValue.heading) content.heading = formValue.heading;
        if (formValue.subHeading) content.subHeading = formValue.subHeading;
        if (formValue.description) content.description = formValue.description;

        // Add columns if they exist and are not empty
        if (formValue.columns && formValue.columns.length > 0) {
          content.columns = formValue.columns.filter(
            (col: string) => col.trim() !== ''
          );
          content.columnSize = formValue.columnSize;
        }

        // Add table if headers exist and are not empty
        if (
          formValue.table &&
          formValue.table.headers &&
          formValue.table.headers.length > 0
        ) {
          content.table = {
            headers: formValue.table.headers.filter(
              (header: string) => header.trim() !== ''
            ),
            rows: formValue.table.rows,
          };
        }
        break;

      case 'IMAGE':
      case 'AIIMAGE':
        content.url = formValue.url;
        if (formValue.caption) content.caption = formValue.caption;
        if (formValue.description) content.description = formValue.description;
        if (formValue.position) content.position = formValue.position;
        if (formValue.textPosition)
          content.textPosition = formValue.textPosition;
        break;

      case 'VIDEO':
        content.file = {};
        content.type = 'video';
        content.requirePass = formValue.requirePass;
        content.file.isEmbedded = +formValue.fileUrlType === 3;
        if (formValue.url) content.file.url = formValue.url;
        if (formValue.title) content.title = formValue.title;
        if (formValue.description) content.description = formValue.description;
        if (formValue.fileUrlType)
          content.file.preventSkip = formValue.preventSkip;
        break;

      case 'FLASHCARD':
        // content.front = formValue.frontValue ?? this.widgetContent?.front;
        // content.back = formValue.backValue ?? this.widgetContent?.back;
        //     meta: {
        //   column?: number;
        //   spacing?: number;
        //   padding?: number;
        //   margin?: number;
        // };
        content.requirePass = formValue.requirePass;
        content.cards = formValue.cardValues;
        content.column = formValue.cardValues.length;
        break;

      case 'QUIZ':
        if (formValue.title) content.title = formValue.title;
        if (formValue.canPrevious) content.canPrevious = formValue.canPrevious;
        if (formValue.showSummary) content.showSummary = formValue.showSummary;
        if (formValue.passageScore)
          content.passageScore = formValue.passageScore;
        content.requirePass = formValue.requirePass;
        content.questions = formValue.questions;
        content.answers = Object.entries(
          formValue.questions
            .filter((w: any) => w.type !== 'blank')
            .map((q: any) =>
              q.options
                .filter((w: any) => w.isCorrect)
                .map((o: any) => ({ questionId: q.id, answer: o.id }))
            )
            .flat()
            .reduce(
              (acc: Record<string, string[]>, { questionId, answer }: any) => {
                if (!acc[questionId]) {
                  acc[questionId] = [];
                }
                acc[questionId].push(answer);
                return acc;
              },
              {} as Record<string, string[]>
            )
        ).map(([questionId, answers]) => ({ questionId, answer: answers }));
        const answerForBlanks = Object.entries(
          formValue.questions
            .filter((w: any) => w.type === 'blank')
            .map((q: any) =>
              q.options.map((o: any) => ({
                questionId: q.id,
                answer: o.label,
              }))
            )
            .flat()
            .reduce(
              (acc: Record<string, string[]>, { questionId, answer }: any) => {
                if (!acc[questionId]) {
                  acc[questionId] = [];
                }
                acc[questionId].push(answer);
                return acc;
              },
              {} as Record<string, string[]>
            )
        ).map(([questionId, answers]) => ({ questionId, answer: answers }));
        content.answers.push(...answerForBlanks);
        break;

      case 'CARD':
        if (formValue.name) content.name = formValue.name;
        if (formValue.description) content.description = formValue.description;
        if (formValue.image) content.image = formValue.image;
        if (formValue.stackPosition)
          content.stackPosition = formValue.stackPosition;
        break;

      case 'QUOTE':
        content.quote = formValue.quote;
        if (formValue.author) content.author = formValue.author;
        if (formValue.source) content.source = formValue.source;
        if (formValue.authorImage) content.authorImage = formValue.authorImage;
        if (formValue.alignment) content.alignment = formValue.alignment;
        if (formValue.style) content.style = formValue.style;
        break;

      case 'LIST':
        content.style = formValue.style;
        if (formValue.heading) content.heading = formValue.heading;
        content.items = formValue.items;
        break;

      case 'GALLERY':
        if (formValue.title) content.title = formValue.title;
        if (formValue.type) content.type = formValue.type;
        if (formValue.columnSize) content.columnSize = formValue.columnSize;
        content.images = formValue.images;
        content.requirePass = formValue.requirePass;
        break;

      case 'RESOURCE':
        content.file = {};
        if (formValue.title) content.title = formValue.title;
        if (formValue.url) content.file.url = formValue.url;
        if (formValue.description) content.description = formValue.description;
        content.type = formValue.type;
        content.requirePass = formValue.requirePass;
        content.fileUrlType = formValue.fileUrlType;
        break;

      case 'DIVIDER':
        content.style = formValue.style;
        content.thickness = formValue.thickness;
        break;
    }

    return content;
  }
}
