import { Component, inject } from '@angular/core';
import { AuthService, GlobalStateService, LoadingService } from '@lms/core';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import {
  Router,
  RouterLink,
  RouterOutlet,
} from '@angular/router';
import { AsyncPipe, NgIf } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { MenuItem } from './menu';

@Component({
  selector: 'app-layout',
  imports: [
    MatProgressBarModule,
    RouterOutlet,
    NgIf,
    AsyncPipe,
    RouterLink,
    MatMenuModule,
  ],
  templateUrl: './layout.component.html',
})
export class LayoutComponent {
  state = inject(GlobalStateService);
  authService = inject(AuthService);
  loader = inject(LoadingService);
  router = inject(Router);

  menus = mainMenu;

  subMenu = [
    {
      icon: 'input',
      name: 'Logout',
      link: '/',
    },
  ];

  get isPath() {
    return window.location.pathname;
  }

  gotTo(link: string) {
    if (link === '/') {
      this.authService.signOutAndRedirect();
      return;
    }
    this.router.navigate([link]);
  }
}

const mainMenu = [
  {
    title: 'Home',
    path: '/management',
    select: '/management/dashboard',
    icon: 'fas fa-columns',
    active: true,
  },
  {
    title: 'Courses',
    icon: 'fas fa-book',
    active: true,
    path: '/management/courses',
    select: '/management/courses',
  },
  {
    title: 'Users',
    path: '/management/users',
    select: '/management/users',
    active: true,
  },
  {
    title: 'Reporting',
    path: '/management/reporting',
    select: '/management/reporting',
    icon: 'fas fa-file-medical-alt',
    active: true,
  },
  {
    title: 'Billing',
    icon: 'fas fa-file-invoice-dollar',
    active: true,
    access: 'owner',
    path: '/management/billings',
    select: '/management/billings',
  }
] as MenuItem[];
