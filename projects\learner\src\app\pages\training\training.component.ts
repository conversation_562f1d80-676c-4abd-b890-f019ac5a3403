import { DatePip<PERSON>, <PERSON>son<PERSON>ipe, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  computed,
  inject,
  input,
  OnDestroy,
  OnInit,
  resource,
  signal,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Course,
  UserTrackingItem,
  getId,
  LearningService,
  ScormCloundService,
  TrainingService,
} from '@lms/core';
import {
  ExpandableComponent,
  HtmlWrapperComponent,
  ProgressComponent,
  ResourceHeaderComponent,
  StartRatingComponent,
} from '@lms/shared';

@Component({
  selector: 'app-training',
  imports: [
    ResourceHeaderComponent,
    HtmlWrapperComponent,
    StartRatingComponent,
    ExpandableComponent,
    NgTemplateOutlet,
    ProgressComponent,
  ],
  templateUrl: './training.component.html',
})
export class TrainingComponent implements OnInit, OnDestroy {
  activeRoute = inject(ActivatedRoute);
  trainingService = inject(TrainingService);
  learningService = inject(LearningService);
  scormCloundService = inject(ScormCloundService);

  id = input<string>('');
  pathId = input<string>('');

  rating = signal<number>(0);
  comments = signal<string>('');
  courseSource = this.trainingService.course;

  course = computed(() => this.courseSource.value()?.course);
  tracking = computed(() => this.courseSource.value()?.tracking);

  tab = 1;
  extraTab = 1;
  isPlaying = false;
  windowRef: any;
  isLoading = false;

  get disableFeedback() {
    return this.tracking()?.status === 'COMPLETED' || !!this.tracking()?.feedback?.rate;
  }

  tabs = computed(() =>
    this.pathId()
      ? [
          {
            id: 1,
            name: 'Learning Path',
          },
          {
            id: 2,
            name: 'Course Information',
          },
          {
            id: 3,
            name: 'Instructor Bio',
          },
          {
            id: 4,
            name: 'Reviews and Feedback',
          },
        ]
      : [
          {
            id: 1,
            name: 'Course Information',
          },
          {
            id: 2,
            name: 'Instructor Bio',
          },
          {
            id: 3,
            name: 'Reviews and Feedback',
          },
          {
            id: 4,
            name: 'Resource',
          },
        ]
  );

  ngOnInit(): void {
    this.trainingService.courseId.set(this.id());
  }

  async launchCourse(info: Course, tracking?: UserTrackingItem) {
    let registrationId = tracking?.scormRegistrationId;
    if (info && !tracking?.id) {
      registrationId = (await this.trainingService.addTracking(info, tracking!))
        .data?.scormRegistrationId;
    }

    if (info.scormCourseId && registrationId) {
      const url = await this.scormCloundService.playCourse(
        registrationId,
        info.id!
      );
      window.open(url, '_blank');
      return;
    }

    this.isPlaying = true;
    const query = new URLSearchParams({
      id: info.id,
      source: 'LEARNER',
    });
    this.windowRef = window.open(
      `go-training?${query.toString()}`,
      'LearnOrTeach',
      'popup'
    );
    this.windowRef?.focus();
  }

  launch(info: { course: Course | undefined; learningPath: any | undefined }) {}

  async saveFeedback() {
    this.isLoading = true;
    const rating = this.rating();
    const comments = this.comments();
    const trackingId = this.tracking()?.id;
    if (!rating || !comments || trackingId) return;
    const res = await this.trainingService.updateTracking(trackingId!, {
      ...this.tracking,
      feedback: {
        rate: rating,
        description: comments,
      },
    } as any);
    this.isLoading = false;
    this.trainingService.course.reload();
  }

  exit() {
    this.windowRef?.close();
    this.isPlaying = false;
    this.trainingService.course.reload();
  }

  ngOnDestroy(): void {
    this.exit();
  }
}
