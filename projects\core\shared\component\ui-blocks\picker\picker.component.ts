import {
  Component,
  ComponentRef,
  inject,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { LessonWidgetTypes } from '../ui-block.utils';
import { CardTemplate } from '../card.component';
import { getTextTemplate } from '../text.component';
import { getResourceTemplate, getVideoTemplate } from '../resource.component';
import { getQuoteTemplate } from '../quote.component';
import { getListTemplate } from '../list.component';
import { getImageTemplate } from '../image.component';
import { getGalleryTemplate } from '../gallery.component';
import { getDividerTemplate } from '../divider.component';
import { getQuizTemplate } from '../quiz/quiz.component';
import { UIBlockAIComponent } from '../ai-widget/ai-widget.component';
import { getFlashcardsTemplate } from '../flashcard/flashcards.component';

@Component({
  selector: 'app-block-uipicker',
  imports: [UIBlockAIComponent],
  templateUrl: './picker.component.html',
  styles: [`
    // .component-name-tag {
    //   position: absolute;
    //   top: -10px;
    //   right: -10px;
    //   background-color: #3b82f6;
    //   color: white;
    //   padding: 2px 6px;
    //   border-radius: 9999px;
    //   font-size: 0.75rem;
    //   font-weight: bold;
    // }
  `]
})
export class UIBlockPickerComponent implements OnInit {
  public dialogRef: MatDialogRef<UIBlockPickerComponent> = inject(MatDialogRef);
  viewContainerRef = inject(ViewContainerRef);

  data: {
    id: string;
    content: Record<string, any>;
  } = inject(MAT_DIALOG_DATA);

  @ViewChild('widgetHost', { read: ViewContainerRef, static: true })
  widgetHostRef!: ViewContainerRef;

  get isAIWidget() {
    return ['IMAGE', 'TEXT'].includes(this.currentWidget.id);
  }

  widgets = LessonWidgetTypes;
  // .filter(
  //   (x) => 'AIIMAGE' !== x.id && 'AITEXT' !== x.id
  // );
  currentWidget?: any;
  private componentRefs: ComponentRef<any>[] = [];

  componentMap = new Map<
    string,
    {
      getTemplate: (type: number, viewContainerRef?: ViewContainerRef) => any;
      sample: number;
    }
  >([
    ['TEXT', { getTemplate: getTextTemplate, sample: 6 }],
    // ['PROCESS', { getTemplate: getTextTemplate, sample: 6 }],
    ['SORTING', { getTemplate: getTextTemplate, sample: 6 }],
    ['INTERACTIVE', { getTemplate: getTextTemplate, sample: 6 }],
    ['CARD', { getTemplate: CardTemplate, sample: 6 }],
    ['QUIZ', { getTemplate: getQuizTemplate, sample: 4 }],
    ['FLASHCARD', { getTemplate: getFlashcardsTemplate, sample: 2 }],
    ['GALLERY', { getTemplate: getGalleryTemplate, sample: 7 }],
    ['IMAGE', { getTemplate: getImageTemplate, sample: 2 }],
    ['LIST', { getTemplate: getListTemplate, sample: 3 }],
    ['QUOTE', { getTemplate: getQuoteTemplate, sample: 6 }],
    ['VIDEO', { getTemplate: getVideoTemplate, sample: 2 }],
    ['RESOURCE', { getTemplate: getResourceTemplate, sample: 3 }],
    ['DIVIDER', { getTemplate: getDividerTemplate, sample: 3 }],
    ['CHART', { getTemplate: getResourceTemplate, sample: 1 }],
  ]);

  ngOnInit(): void {
    this.currentWidget = this.data;
    const widget = this.widgets.find((x) => x.id === this.data.id);
    if (widget) {
      this.viewTemplate(widget);
    }
  }

  viewTemplate(widget: any) {
    this.currentWidget = widget;
    this.widgetHostRef?.clear();
    // if (this.isAIWidget) return;
    const template = this.componentMap.get(widget.id);
    for (const item of Array.from(Array(template!.sample).keys())) {
      const componentRef = this.widgetHostRef.createComponent<any>(
        widget.component as any
      );
      const uiContent = template!.getTemplate(item, this.viewContainerRef);
      componentRef.setInput('content', uiContent);
      this.viewContainerRef.clear();
      if (['DIVIDER'].includes(widget.id)) {
        componentRef.setInput('todo', true);
      }

      componentRef.instance?.view?.subscribe((data: any) => {
        this.dialogRef.close({ data, widget });
      });
      this.componentRefs.push(componentRef);
      this.styleComponent(componentRef, uiContent.uiLabel);
    }
  }

  getAiData(data: any) {
    this.dialogRef.close(data);
  }

  
  private styleComponent(componentRef: ComponentRef<any>, label: string) {
    const element = componentRef.location.nativeElement;
    element.className = '';
    element.classList.add(
      'block',
      'p-4',
      'mb-2',
      'transition-all',
      'bg-lot-light-gray',
      'border-lot-gray',
      'border'
    );

    const div = document.createElement('div') as HTMLElement;
    div.className = 'w-full border-b pb-4 text-lot-blue text-lg';
    div.textContent = label || 'Template Block';
    element.prepend(div);
  }
}
