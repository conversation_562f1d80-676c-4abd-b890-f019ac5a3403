import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-block-card',
  template: `
    <div
      class="flex flex-col gap-4 rounded-lg cursor-pointer {{
        metaClass
      }} transform transition duration-300 hover:scale-105 overflow-hidden"
      [style]="styleForm"
      (click)="view.emit(content)"
    >
      <div
        class="flex flex-col gap-4 h-full"
        [class.items-center]="content.stackPosition === 'center'"
        [class.items-end]="content.stackPosition === 'right'"
      >
        <img
          [src]="content.image"
          [alt]="content.name"
          class="max-w-md object-cover"
          [class.rounded-full]="content.imageRounded"
          [class.rounded-lg]="!content.imageRounded"
        />
        @if (content.name) {
        <h3
          class="text-lg font-semibold"
          [class.text-center]="content.stackPosition === 'center'"
          [class.text-end]="content.stackPosition === 'right'"
        >
          {{ content.name }}
        </h3>
        } @if (content.description) {
        <div
          class="text-lot-dark-gray break-words"
          [class.text-center]="content.stackPosition === 'center'"
          [class.text-end]="content.stackPosition === 'right'"
          [innerHTML]="content.description"
        ></div>
        } @if (content.action) {
        <a
          [href]="content.action.url"
          class="text-lot-blue hover:underline mt-2"
          [class.text-center]="content.stackPosition === 'center'"
          [class.text-end]="content.stackPosition === 'right'"
        >
          {{ content.action.label }}
        </a>
        }
      </div>
    </div>
  `,
})
export class UIBlockCardComponent {
  @Input() content: {
    readyForLecture: boolean;
    image: string;
    name?: string;
    description?: string;
    imageRounded: boolean;
    stackPosition: 'left' | 'center' | 'right';
    action?: {
      label: string;
      url: string;
    };
    meta: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color: string;
    };
  };
  @Output() view = new EventEmitter();

  get styleForm() {
    return this.content?.meta?.background?.includes('#')
      ? `background-color: ${this.content?.meta?.background}; color: ${this.content?.meta?.color}`
      : ``;
  }
  
  get metaClass() {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      height: !this.content?.meta?.height
        ? 'h-full'
        : this.content?.meta?.height === 100
        ? 'h-full'
        : this.content?.meta?.height === 50
        ? 'h-1/2'
        : this.content?.meta?.height === 33
        ? 'h-1/3'
        : this.content?.meta?.height === 25
        ? 'h-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? 'p-10'
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? 'bg-lot-gray/20'
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    const position =
      (this.content?.meta?.width || 0) < 100
        ? ' mx-auto justify-center items-center '
        : '';
    return Object.values(meta).join(' ') + position;
  }
}

export const CardTemplate = (type: number) => {
  switch (type) {
    case 0:
      return {
        uiLabel: 'Image Card',
        image: 'assets/images/new/card-1.jpeg',
        imageRounded: false,
        stackPosition: 'left',
      };
    case 1:
      return {
        uiLabel: 'Image with Text Card',
        image: 'assets/images/new/card-1.jpeg',
        name: 'How to Make a Good Proposal!',
        description:
          'Pants crank note crystallize pulling weeks after illustration. Across about. Praesent libero',
        imageRounded: false,
        stackPosition: 'left',
      };
    case 2:
      return {
        uiLabel: 'Centered Image with Text Card',
        image: 'assets/images/new/card-2.jpeg',
        name: 'How to Write a Great Proposal!',
        description:
          'The quick brown fox jumps over the lazy dog. Fusce nec tellus sed augue semper porta. ipsum dolor sit amet',
        imageRounded: false,
        stackPosition: 'center',
      };
    case 3:
      return {
        uiLabel: 'Image with Text on Right Card',
        image: 'assets/images/new/card-3.jpeg',
        name: 'How to Build a Website!',
        description:
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
        imageRounded: false,
        stackPosition: 'right',
      };
    case 4:
      return {
        uiLabel: 'Profile Card',
        image: 'assets/images/new/card-3.jpeg',
        name: 'Jane Smith',
        description: 'Senior Software Engineer',
        imageRounded: true,
        stackPosition: 'center',
        action: {
          label: 'View Profile',
          url: '/profile/jane-smith',
        },
      };
    default:
      return {
        uiLabel: 'Image with Text Card',
        image: 'assets/images/new/card-1.jpeg',
        name: 'How to Make a Good Proposal!',
        description:
          'Pants crank note crystallize pulling weeks after illustration. Across about. Praesent libero',
        imageRounded: false,
        stackPosition: 'left',
      };
  }
};
