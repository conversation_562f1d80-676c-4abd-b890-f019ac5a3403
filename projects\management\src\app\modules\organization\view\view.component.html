<h2 class="font-semibold text-secondary text-2xl mb-4">Organizations</h2>
<div class="bg-white px-4 pt-4 pb-4 lms-container rounded overflow-x-auto">
  <div class="flex flex-col md:flex-row gap-4 pb-2">
    <button
      class="flex items-center bg-secondary hover:bg-blue-700 text-white font-semibold py-2 px-9 rounded text-sm"
      (click)="openForm(null)"
    >
      <i class="fa fa-plus"></i>
      <span class="ml-5">Create New</span>
    </button>
    <div class="flex-1 relative">
      <i class="fa fa-search search-icon text-lot-blue absolute mt-3 mx-2"></i>
      <input
        [formControl]="searchKey"
        class="search-form text-10xs appearance-none block w-full bg-dark-100 text-gray-700 p-l rounded py-3 pr-4 pl-8 leading-tight focus:bg-white focus:border-lot-blue"
        type="text"
        placeholder="Search for Organization"
      />
    </div>
  </div>

  <mat-table
    [dataSource]="dataSource"
    matSort
    matSortActive="created_at"
    matSormat-cellirection="desc"
  >
    <ng-container matColumnDef="name">
      <mat-header-cell
        *matHeaderCellDef
        class="font-semibold text-primary-400 border-opacity-30"
        >Name</mat-header-cell
      >
      <mat-cell
        *matCellDef="let row"
        data-label="name"
        class="font-semibold text-secondary border-opacity-30"
        >{{ row.name }}</mat-cell
      >
    </ng-container>

    <ng-container matColumnDef="description">
      <mat-header-cell
        *matHeaderCellDef
        class="font-semibold text-primary-400 border-opacity-30"
      >
        Description
      </mat-header-cell>
      <mat-cell
        *matCellDef="let row"
        data-label="Number Of Subjects"
        class="text-10xs text-secondary border-opacity-30"
        >{{ row.description }}</mat-cell
      >
    </ng-container>

    <ng-container matColumnDef="nbUser">
      <mat-header-cell
        *matHeaderCellDef
        class="font-semibold text-primary-400 border-opacity-30"
      >
        Number Of Users
      </mat-header-cell>
      <mat-cell
        *matCellDef="let row"
        data-label="Number Of Users"
        class="text-10xs text-secondary border-opacity-30"
        >{{ row.nbUser || 0 }}</mat-cell
      >
    </ng-container>

    <ng-container matColumnDef="action">
      <mat-header-cell *matHeaderCellDef></mat-header-cell>
      <mat-cell *matCellDef="let row" class="text-right border-opacity-30">
        <button class="button-secondary w-32" (click)="openForm(row)">
          View Details
        </button>
      </mat-cell>
    </ng-container>

    <mat-header-row
      *matHeaderRowDef="displayedColumns"
      class="border-opacity-30"
    ></mat-header-row>
    <mat-row *matRowDef="let row; columns: displayedColumns"></mat-row>
  </mat-table>
  <mat-paginator
    [length]="dataSource.data.length"
    [pageSize]="10"
  ></mat-paginator>
</div>
