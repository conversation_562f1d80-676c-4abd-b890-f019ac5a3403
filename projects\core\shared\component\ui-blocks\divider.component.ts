import { NgStyle } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-block-divider',
  imports: [NgStyle],
  template: `
    <div
      class="{{ metaClass }}"
      (click)="view.emit(content)"
      [class.cursor-pointer]="todo"
      [class.py-5]="todo"
      [class.my-5]="todo"
      [class.background-lot-light-gray]="todo"
    >
      @switch (content.type) { @case ('solid') {
      <hr
        [ngStyle]="{
          'border-top-style': 'solid',
          'border-top-width.px': content.thickness || 1
        }"
      />
      } @case ('dashed') {
      <hr
        [ngStyle]="{
          'border-top-style': 'dashed',
          'border-top-width.px': content.thickness || 1
        }"
      />
      } @case ('dotted') {
      <hr
        [ngStyle]="{
          'border-top-style': 'dotted',
          'border-top-width.px': content.thickness || 1
        }"
      />
      } @case ('blank') {
      <div [style.height.px]="content.thickness || 20"></div>
      } @case ('button') {
      <button
        class="button-primary-outline w-full"
        [style.margin]="content.meta?.margin + 'px 0'"
        (click)="view.emit(content)"
      >
        {{ content.buttonLabel || 'Continue' }}
      </button>
      } }
    </div>
  `,
})
export class UIBlockDividerComponent {
  @Input() content: {
    type: 'solid' | 'dashed' | 'dotted' | 'blank' | 'button';
    thickness?: number;
    buttonLabel?: string;
    meta?: {
      width?: number;
      height?: number;
      padding?: number;
      margin?: number;
      background?: string;
      color?: string;
    };
  };
  @Input() todo = false;

  @Output() view = new EventEmitter<any>();

  get metaClass(): string {
    const meta = {
      width: !this.content?.meta?.width
        ? 'w-full'
        : this.content?.meta?.width === 100
        ? 'w-full'
        : this.content?.meta?.width === 50
        ? 'w-1/2'
        : this.content?.meta?.width === 33
        ? 'w-1/3'
        : this.content?.meta?.width === 25
        ? 'w-1/4'
        : '',
      padding: !this.content?.meta?.padding
        ? ''
        : 'p-' + this.content?.meta?.padding,
      margin: !this.content?.meta?.margin
        ? ''
        : 'm-' + this.content?.meta?.margin,
      background: !this.content?.meta?.background
        ? ''
        : 'bg-' + this.content?.meta?.background,
      color: !this.content?.meta?.color
        ? ''
        : 'text-' + this.content?.meta?.color,
    };
    return Object.values(meta).join(' ');
  }
}

export const getDividerTemplate = (type: number) => {
  switch (type) {
    case 1: // Solid line
      return {
        uiLabel: 'Solid Line Divider',
        type: 'solid',
        thickness: 1,
        meta: {
          width: 100,
          margin: 20,
          color: 'lot-dark',
        },
      };

    case 2: // Dashed line
      return {
        uiLabel: 'Dashed Line Divider',
        type: 'dashed',
        thickness: 2,
        meta: {
          width: 75,
          margin: 30,
          color: 'lot-gray',
        },
      };

    case 3: // Dotted line
      return {
        uiLabel: 'Dotted Line Divider',
        type: 'dotted',
        thickness: 1,
        meta: {
          width: 50,
          margin: 25,
          color: 'lot-blue',
        },
      };

    case 4: // Blank space
      return {
        uiLabel: 'Blank Space Divider',
        type: 'blank',
        thickness: 40,
        meta: {
          width: 100,
          margin: 0,
        },
      };

    case 5: // Button divider
      return {
        uiLabel: 'Button Divider',
        type: 'button',
        buttonLabel: 'Continue to Next Section',
        meta: {
          width: 50,
          margin: 20,
          color: 'lot-blue',
        },
      };

    default: // Default solid line
      return {
        uiLabel: 'Solid Line Divider',
        type: 'solid',
        thickness: 1,
        meta: {
          width: 100,
          margin: 20,
          color: 'lot-dark',
        },
      };
  }
};
